"""
PowerPoint automation settings and configurations.
Contains priority lists and mappings for chart and data processing.
"""

# Chart priority list - determines the order of chart processing and selection
chart_priority = [
    "revenue",
    "arpu",
    "usersPenetration",
    "volume",
    "pricePerUnit",
    "spendPerEmployee",
    "revenueShare",
    "users",
    "valueAdded",
    "output",
    "numberEmployees",
    "deliveries",
    "averageDealSize",
    "energyProduction",
    "energyProductionGrowth",
    "growthFactor",
    "transactionValue",
    "revenuePerPharmacy",
    "scenarioRevenue",
    "premium",
    "freightTransported",
    "transportationEfficiency",
    "production",
    "onlineRevenueShare",
    "exportValue",
    "importValue",
    "grossProductionValue",
    "medications",
    "revenueIndustryShare",
    "realEstateVolume",
    "productionVolume",
    "revenueDesktopMobileShare",
    "revenueTherapeuticArea",
    "lease",
    "interestIncome",
    "deposits",
    "loans",
    "osShare",
    "averageLenghtOfStay",
    "averageTransactionSize",
    "numberOfTransactions",
    "atpu",
    "aum",
    "containerTransport",
    "shareEmissions",
    "transactionValueOutwardRemittances",
    "transactionValueInwardRemittances",
    "realEstateValue",
    "mobileSubsriptions",
    "advisors",
    "revenueGrowth",
    "revenuePerCapita",
    "revenueInApp",
    "downloads",
    "revenueRetailPlatform",
    "revenueRetailPlatformDesktopMobileShare",
    "marketCapitalization",
    "marketVolume",
    "numberOfTrades",
    "netWorth",
    "claimPayments",
    "atpuOutwardRemittances",
    "atpuInwardRemittances",
    "productionGrowth",
    "productionVolumeGrowth",
    "airlinesRevenue",
    "airportVolume",
    "cost",
    "costGrowth",
    "containerPortThroughput",
    "revenueOther",
    "revenueGrowthOther",
    "revenueBrand",
    "premiumGrowth",
    "reserves",
    "energyEmissionIntencity",
    "tradeNetVolume"
]


# Geographic priority lists for filtering and processing
regions = [
    "Asia",
    "Europe",
    "South America",
    "North America",
    "Africa",
    "Australia & Oceania"
]

countries = [
    "Australia",
    "Canada",
    "United States",
    "Saudi Arabia",
    "Brazil",
    "China",
    "India",
    "Japan",
    "Singapore",
    "South Korea",
    "France",
    "Germany",
    "Ireland",
    "Italy",
    "Spain",
    "Sweden",
    "Switzerland",
    "United Kingdom",
    "New Zealand",
    "Russia"
]

# Combined geographic priorities (regions + countries + global)
all_geographies = ["Worldwide"] + regions + countries

# Additional settings can be added here as needed
# For example:
# text_object_mappings = {...}
# chart_object_mappings = {...}
# formatting_settings = {...}