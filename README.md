# Market Insights Reports

A comprehensive data pipeline for generating market insights reports from multiple data sources, with automated PowerPoint generation.

## Architecture Overview

```
MySQL → RDS PostgreSQL → dbt → MongoDB → PowerPoint → S3
```

## Key Components

### 1. Data Pipeline
- **MySQL to RDS**: `mysql_to_rds.py` - Migrates data from MySQL to PostgreSQL RDS
- **dbt Transformations**: Processes and aggregates data in PostgreSQL
- **RDS to MongoDB**: `rds_to_mongodb.py` - Exports processed data to MongoDB
- **PowerPoint Generation**: `mongodb_to_powerpoint.py` - Creates reports from MongoDB data

### 2. Priority Management System

**Centralized priority management** ensures consistency across the entire pipeline:

#### Chart Priorities

#### dbt Models (SQL)
- **Macro**: `dbt/macros/get_chart_priorities.sql`
- **Usage**: `WHERE SPLIT_PART(chart_key, '_', 1) IN ({{ get_chart_priorities() }})`
- **Models using this**:
  - `core_pages_to_charts.sql`
  - Any model filtering charts by priority

#### PowerPoint Automation (Python)
- **Settings**: `powerpoint_settings.py`
- **List**: `chart_priority` (75 chart types)
- **Usage**: Determines chart selection and processing order

#### Geographic Priorities
- **Macro**: `dbt/macros/get_geo_priorities.sql`
- **Usage**: `WHERE geo_name IN ({{ get_geo_priorities() }})`
- **Primary Implementation**: `core_pages_to_charts.sql` - **Core model that filters the entire pipeline**
- **Models using this**:
  - **`core_pages_to_charts.sql`** - Primary geographic filtering point
  - `mart_mongodb.sql` - Final export mart (inherits from core model)
  - Any model filtering by geographic priority

#### Benefits
- ✅ **Single Source of Truth**: Priorities defined centrally
- ✅ **Consistency**: Same priorities across dbt and Python
- ✅ **Maintainability**: Update in one place, affects entire pipeline
- ✅ **Performance**: Only processes prioritized charts and geographies
- ✅ **Business Focus**: Ensures most important markets and metrics are processed first

### 3. Long-Running Model Protection

Models with 24-hour timeout protection:
- `core_array_agg_values.sql` - Array aggregation of KPI values
- `core_pages_to_agg_values.sql` - Page-level aggregations  
- `mart_verticals_to_charts.sql` - Chart data for verticals

**Timeout Settings**:
- `statement_timeout = '24h'`
- `idle_in_transaction_session_timeout = '24h'`
- `lock_timeout = '2h'`

## Database Configuration

### RDS Instance
- **Type**: db.t4g.medium (4GB RAM)
- **Engine**: PostgreSQL 17.4
- **Storage**: 449GB gp3 with 12,000 IOPS
- **Multi-AZ**: Enabled for high availability

### Memory Optimization
- **work_mem**: 64MB-256MB (depending on model complexity)
- **maintenance_work_mem**: 128MB-512MB
- **temp_buffers**: 32MB-64MB

## dbt Execution Scripts

All scripts run in background by default and log to `dbt_run.log`:

- `run_dbt.sh` - Run all models
- `run_dbt_long_models.sh` - Only long-running models
- `run_dbt_quick_models.sh` - Exclude long-running models
- `run_dbt_selective.sh` - Flexible model selection

**Usage Examples**:
```bash
# Run specific models
./run_dbt_selective.sh --select core_array_agg_values

# Run all except long models
./run_dbt_quick_models.sh

# Monitor progress
tail -f dbt_run.log
```

## PowerPoint Automation

### Object-Based Replacement
Uses PowerPoint object names (not text search) for precise updates:
- Text objects: `market`, `country`, `source`
- Chart objects: `chart_global_ind_1`, `chart_global_ind_2`, etc.

### Chart Data Integration
- Extracts chart data from MongoDB `chartData` arrays
- Updates PowerPoint charts with years (X-axis) and values (Y-axis)
- Uses `python-pptx` with `CategoryChartData` and `chart.replace_data()`

### S3 Integration
- Uploads generated reports to S3 bucket
- Creates public download URLs
- Bucket: `market-insights-reports-s3`

## File Structure

```
├── dbt/
│   ├── macros/
│   │   └── get_chart_priorities.sql    # Chart priority macro
│   ├── models/
│   │   ├── core/
│   │   │   ├── core_array_agg_values.sql
│   │   │   ├── core_pages_to_agg_values.sql
│   │   │   └── core_pages_to_charts.sql
│   │   └── marts/
│   │       ├── mart_verticals_to_charts.sql
│   │       └── mart_mongodb.sql             # Final export mart
│   └── run_dbt_*.sh                    # Execution scripts
├── mongodb_to_powerpoint.py            # PowerPoint generation
├── powerpoint_settings.py              # Chart priorities (Python)
├── rds_to_mongodb.py                   # Data export
├── mysql_to_rds.py                     # Data migration
└── template.pptx                       # PowerPoint template
```

## Environment Variables

Required in `.env` files:
```bash
RDS_USERNAME=postgres
RDS_PASSWORD=your_password
RDS_DATABASE=postgres
```

## Monitoring and Logs

- **dbt logs**: `dbt_run.log` (unified logging)
- **Process monitoring**: `ps aux | grep dbt`
- **Real-time monitoring**: `tail -f dbt_run.log`

## Performance Considerations

- **Chart filtering**: Only processes prioritized chart types
- **Incremental models**: Use `unique_key` for efficient updates
- **Memory optimization**: Tuned for db.t4g.medium instance
- **Connection management**: TCP keepalives and retry logic
- **Background execution**: All long-running processes use nohup
