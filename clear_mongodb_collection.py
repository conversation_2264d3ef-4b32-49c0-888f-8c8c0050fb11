#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to delete all documents from the market_insights collection in MongoDB.
Use with caution - this will permanently delete all data in the collection.
"""

import pymongo
from pymongo import MongoClient

def clear_market_insights_collection():
    """Delete all documents from the market_insights collection."""

    # MongoDB connection details (same as mongodb_to_powerpoint.py)
    MONGO_HOST = "*************"
    MONGO_PORT = 27017
    MONGO_USER = "admin"
    MONGO_PASSWORD = "statista123"
    MONGO_DB = "statista_insights"
    COLLECTION_NAME = "market_insights"

    try:
        # Connect to MongoDB (same method as mongodb_to_powerpoint.py)
        client = MongoClient(
            host=MONGO_HOST,
            port=MONGO_PORT,
            username=MONGO_USER,
            password=MONGO_PASSWORD,
            authSource="admin",
            serverSelectionTimeoutMS=5000
        )

        # Test connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful!")

        # Get database and collection
        db = client[MONGO_DB]
        collection = db[COLLECTION_NAME]
        
        # Count documents before deletion
        doc_count_before = collection.count_documents({})
        print(f"📊 Documents in market_insights collection: {doc_count_before}")
        
        if doc_count_before == 0:
            print("✅ Collection is already empty. Nothing to delete.")
            return
        
        # Confirm deletion
        confirm = input(f"⚠️  Are you sure you want to delete ALL {doc_count_before} documents from market_insights collection? (yes/no): ")
        
        if confirm.lower() != 'yes':
            print("❌ Operation cancelled.")
            return
        
        # Delete all documents
        print("🗑️  Deleting all documents...")
        result = collection.delete_many({})
        
        # Verify deletion
        doc_count_after = collection.count_documents({})
        
        print(f"✅ Successfully deleted {result.deleted_count} documents")
        print(f"📊 Documents remaining: {doc_count_after}")
        
        if doc_count_after == 0:
            print("🎉 Collection is now empty!")
        else:
            print("⚠️  Warning: Some documents may still remain")
            
    except pymongo.errors.ConnectionFailure as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
    except pymongo.errors.AuthenticationFailed as e:
        print(f"❌ Authentication failed: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        try:
            client.close()
            print("🔌 MongoDB connection closed")
        except:
            pass

def main():
    """Main function with safety checks."""
    print("🧹 MongoDB Collection Cleaner")
    print("=" * 40)
    print("Host: *************:27017")
    print("Database: statista_insights")
    print("Collection: market_insights")
    print("=" * 40)
    
    # Safety warning
    print("⚠️  WARNING: This will permanently delete ALL data in the collection!")
    print("⚠️  Make sure you have a backup if needed.")
    print()
    
    clear_market_insights_collection()

if __name__ == "__main__":
    main()
