#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gc
import os
import time
import urllib.parse
import re

from dotenv import load_dotenv
from sqlalchemy import create_engine, text

load_dotenv()  # Load .env file

# Tables to transfer - all tables
TABLES_TO_TRANSFER = [
    "chapters",
    "charts", 
    "chartsKpis",
    "chartsTypes",
    "geos",
    "kpis",
    "kpisValues", 
    "pages",
    "units",
    "verticalGraphs",
    "verticals"
]

# For testing individual tables, uncomment below:
# TABLES_TO_TRANSFER = ["verticals"]

CHUNK_SIZE = 1000
PARTITION_STEP = 100000
MAX_RETRIES = 3

# Load from .env
raw_password = os.getenv("MYSQL_RAW_PASSWORD")

# MySQL connection (source)
MYSQL_USER = "smi_user"
MYSQL_PASS = urllib.parse.quote_plus(raw_password)
MYSQL_HOST = "stat-hh-smi-outlook.corp.statista.com"
MYSQL_PORT = 3306
MYSQL_DB = "smi_outlook"

# PostgreSQL RDS connection (destination)
POSTGRES_USER = os.getenv("RDS_USERNAME", "postgres")
POSTGRES_PASS = os.getenv("RDS_PASSWORD")
POSTGRES_HOST = "smi-report-db.cisims9lnzeb.eu-central-1.rds.amazonaws.com"
POSTGRES_PORT = 5432
POSTGRES_DB = os.getenv("RDS_DATABASE", "postgres")

print(f"Connecting to MySQL: {MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
print(f"Connecting to RDS PostgreSQL: {POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}")

# Set up SQLAlchemy engines
mysql_url = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASS}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}"
postgres_url = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASS}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

try:
    mysql_engine = create_engine(mysql_url, pool_pre_ping=True, pool_recycle=3600)
    postgres_engine = create_engine(postgres_url, pool_pre_ping=True, pool_recycle=3600)
    
    # Test connections
    print("Testing MySQL connection...")
    with mysql_engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        print("MySQL connection successful")
    
    print("Testing PostgreSQL RDS connection...")
    with postgres_engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        print("PostgreSQL RDS connection successful")
        
except Exception as e:
    print(f"ERROR: Connection failed: {e}")
    exit(1)

def get_table_schema(table_name, mysql_conn):
    """Get the CREATE TABLE statement for a MySQL table"""
    result = mysql_conn.execute(text(f"SHOW CREATE TABLE {table_name}"))
    create_statement = result.fetchone()[1]
    return create_statement

def get_primary_key_column(table_name, mysql_conn):
    """Get the primary key column for a table"""
    try:
        result = mysql_conn.execute(text(f"SHOW COLUMNS FROM {table_name}"))
        columns = result.fetchall()
        
        for col in columns:
            if col[3] == 'PRI':  # Primary key
                return col[0]  # Column name
        
        # If no primary key found, return None (don't assume first column)
        return None
        
    except Exception as e:
        print(f"WARNING: Error getting primary key for {table_name}: {e}")
        return None

def mysql_to_postgres_schema(mysql_create):
    """Convert MySQL CREATE TABLE to PostgreSQL compatible"""
    import re
    
    # Basic type conversions
    postgres_create = mysql_create
    
    # Replace backticks with double quotes
    postgres_create = postgres_create.replace('`', '"')
    
    # Fix CREATE TABLE statement to quote table name properly
    # Look for CREATE TABLE pattern and ensure table name is quoted
    postgres_create = re.sub(r'CREATE TABLE\s+(\w+)', r'CREATE TABLE "\1"', postgres_create)
    
    # Fix MySQL data types with length specifications
    type_patterns = [
        (r'int\(\d+\)', 'integer'),
        (r'tinyint\(\d+\)', 'smallint'),
        (r'bigint\(\d+\)', 'bigint'),
        (r'smallint\(\d+\)', 'smallint'),
        (r'mediumint\(\d+\)', 'integer'),
        (r'tinyinteger\(\d+\)', 'smallint'),  # Handle tinyinteger
        (r'tinyinteger', 'smallint'),         # Handle tinyinteger without parentheses
        (r'float\(\d+,\d+\)', 'real'),
        (r'double\(\d+,\d+\)', 'double precision'),
        (r'double', 'double precision'),
        (r'biginteger', 'bigint'),            # Handle biginteger type
    ]
    
    for pattern, replacement in type_patterns:
        postgres_create = re.sub(pattern, replacement, postgres_create)
    
    # Other MySQL to PostgreSQL type mappings
    replacements = {
        'datetime': 'timestamp',
        'timestamp': 'timestamp',
        'AUTO_INCREMENT': '',
        'ENGINE=InnoDB': '',
        'DEFAULT CHARSET=utf8': '',
        'COLLATE=utf8_unicode_ci': '',
        'COLLATE utf8_unicode_ci': '',
        'CHARACTER SET utf8': '',
        'json': 'jsonb',  # Use jsonb for better performance
    }
    
    # Remove AUTO_INCREMENT with specific values (e.g., AUTO_INCREMENT=5595217)
    postgres_create = re.sub(r'AUTO_INCREMENT=\d+', '', postgres_create)
    
    for mysql_type, postgres_type in replacements.items():
        postgres_create = postgres_create.replace(mysql_type, postgres_type)
    
    # Remove MySQL-specific syntax
    lines = postgres_create.split('\n')
    cleaned_lines = []
    for line in lines:
        # Skip MySQL KEY definitions (but keep PRIMARY KEY)
        if 'KEY ' in line and 'PRIMARY KEY' not in line:
            continue
        # Skip foreign key constraints for now
        if 'CONSTRAINT' in line and 'FOREIGN KEY' in line:
            continue
        # Skip MySQL-specific options at end
        if any(keyword in line for keyword in ['ENGINE=', 'DEFAULT CHARSET=', 'COLLATE=', 'AUTO_INCREMENT=']):
            continue
        # Skip lines that are just closing parenthesis with equals sign (malformed)
        if re.match(r'^\s*\)\s*=', line):
            cleaned_lines.append(')')
            continue
        cleaned_lines.append(line)
    
    postgres_create = '\n'.join(cleaned_lines)
    
    # Clean up syntax issues
    postgres_create = postgres_create.replace(',,', ',')
    postgres_create = postgres_create.replace(',\n)', '\n)')
    postgres_create = postgres_create.replace(',\n\n)', '\n)')
    
    # Remove trailing commas before closing parenthesis
    postgres_create = re.sub(r',(\s*\))', r'\1', postgres_create)
    
    return postgres_create

def transfer_table_data(table_name):
    """Transfer data from MySQL to PostgreSQL using direct SQL"""
    print(f"\nStarting transfer for table: {table_name}")
    
    # PostgreSQL table name (quoted to preserve case)
    postgres_table_name = f'"{table_name}"'
    
    with mysql_engine.connect() as mysql_conn, postgres_engine.connect() as postgres_conn:
        # Get table schema and create table in PostgreSQL
        try:
            print(f"Getting schema for {table_name}...")
            mysql_schema = get_table_schema(table_name, mysql_conn)
            postgres_schema = mysql_to_postgres_schema(mysql_schema)
            
            # Drop table if exists and create new one using transaction
            with postgres_engine.begin() as trans_conn:
                # Use CASCADE to handle dbt view dependencies
                trans_conn.execute(text(f"DROP TABLE IF EXISTS {postgres_table_name} CASCADE"))
                trans_conn.execute(text(postgres_schema))
            print(f"Created table {table_name} in PostgreSQL")
            
        except Exception as e:
            print(f"ERROR: Failed to create table schema: {e}")
            print(f"   Schema was: {postgres_schema[:200]}...")
            return False
        
        # Get primary key column and table info
        try:
            primary_key = get_primary_key_column(table_name, mysql_conn)
            print(f"Primary key column: {primary_key}")
            
            # Get row count
            result = mysql_conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            row_count = result.fetchone()[0]
            
            if row_count == 0:
                print(f"WARNING: Table {table_name} is empty, skipping...")
                return True
            
            # Try to get min/max for chunking (only if primary key exists and is numeric)
            use_chunking = False
            if primary_key:
                try:
                    result = mysql_conn.execute(text(f"SELECT MIN({primary_key}), MAX({primary_key}) FROM {table_name}"))
                    min_key, max_key = result.fetchone()
                    
                    # Check if keys are numeric
                    if isinstance(min_key, (int, float)) and isinstance(max_key, (int, float)):
                            print(f"Table {table_name}: {row_count:,} rows, {primary_key} range: {min_key} to {max_key}")
                            use_chunking = row_count > CHUNK_SIZE
                    else:
                            print(f"Table {table_name}: {row_count:,} rows, non-numeric primary key - will transfer all at once")
                            use_chunking = False
                            
                except Exception as e:
                    print(f"Table {table_name}: {row_count:,} rows, error getting key range: {e}")
                    use_chunking = False
            else:
                print(f"Table {table_name}: {row_count:,} rows, no primary key - will transfer all at once")
                use_chunking = False
                
        except Exception as e:
            print(f"ERROR: Failed to get table info: {e}")
            return False
        
        # Transfer data
        rows_transferred = 0
        start_time = time.time()
        
        if use_chunking:
            # Transfer in chunks using primary key
            for start_key in range(int(min_key), int(max_key) + 1, PARTITION_STEP):
                end_key = min(start_key + PARTITION_STEP, int(max_key) + 1)
                
                # Get column names
                result = mysql_conn.execute(text(f"SHOW COLUMNS FROM {table_name}"))
                columns = [row[0] for row in result.fetchall()]
                column_list = ', '.join([f'"{col}"' for col in columns])  # Quote for PostgreSQL
                
                # Fetch data chunk
                query = f"SELECT * FROM {table_name} WHERE {primary_key} >= {start_key} AND {primary_key} < {end_key}"
                result = mysql_conn.execute(text(query))
                rows = result.fetchall()
                
                if not rows:
                    continue
                    
                # Insert into PostgreSQL
                placeholders = ', '.join([f':param{i}' for i in range(len(columns))])
                insert_query = f"INSERT INTO {postgres_table_name} ({column_list}) VALUES ({placeholders})"
                
                try:
                    # Use transaction for bulk insert
                    with postgres_engine.begin() as trans_conn:
                        for row_data in rows:
                            # Create parameter dictionary
                            params = {f'param{i}': value for i, value in enumerate(row_data)}
                            trans_conn.execute(text(insert_query), params)
                    
                    rows_in_chunk = len(rows)
                    rows_transferred += rows_in_chunk
                    print(f"  Inserted {rows_in_chunk:,} rows ({primary_key} range {start_key}-{end_key-1})")
                    
                except Exception as e:
                    print(f"  ERROR: Failed to insert chunk {start_key}-{end_key-1}: {e}")
                    continue
        else:
            # Transfer all data at once (for small tables or non-numeric keys)
            try:
                # Get column names
                result = mysql_conn.execute(text(f"SHOW COLUMNS FROM {table_name}"))
                columns = [row[0] for row in result.fetchall()]
                column_list = ', '.join([f'"{col}"' for col in columns])
                
                # Fetch all data
                result = mysql_conn.execute(text(f"SELECT * FROM {table_name}"))
                rows = result.fetchall()
                
                if rows:
                    # Insert into PostgreSQL
                    placeholders = ', '.join([f':param{i}' for i in range(len(columns))])
                    insert_query = f"INSERT INTO {postgres_table_name} ({column_list}) VALUES ({placeholders})"
                    
                    with postgres_engine.begin() as trans_conn:
                        for row_data in rows:
                            params = {f'param{i}': value for i, value in enumerate(row_data)}
                            trans_conn.execute(text(insert_query), params)
                    
                    rows_transferred = len(rows)
                    print(f"  Inserted all {rows_transferred:,} rows")
                    
            except Exception as e:
                print(f"  ERROR: Failed to transfer data: {e}")
                return False
        
        end_time = time.time()
        duration = end_time - start_time
        print(f"Completed {table_name}: {rows_transferred:,} rows in {duration:.2f} seconds")
        
        return True

# Main execution
total_start_time = time.time()

for table in TABLES_TO_TRANSFER:
    success = transfer_table_data(table)
    if not success:
        print(f"ERROR: Failed to transfer {table}")
        continue

total_end_time = time.time()
total_duration = total_end_time - total_start_time
print(f"\nAll transfers completed!")
print(f"Total time: {total_duration:.2f} seconds ({total_duration/60:.2f} minutes)")

#run it in background
#   nohup python improved_mysql_to_rds_transfer.py > transfer_improved.log 2>&1 &
#monitor progress
#   tail -f transfer_improved.log
#check status
#   ps aux | grep improved_mysql_to_rds_transfer