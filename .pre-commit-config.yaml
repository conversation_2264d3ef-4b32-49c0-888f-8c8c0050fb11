# .pre-commit-config.yaml
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    -   id: trailing-whitespace
        stages: [pre-commit]
    -   id: end-of-file-fixer
        stages: [pre-commit]
    -   id: check-yaml
        stages: [pre-commit, pre-push]
-   repo: https://github.com/psf/black-pre-commit-mirror
    rev: 24.1.1
    hooks:
    -   id: black
        language_version: python3.9
        args: ["--line-length=88"]
        stages: [pre-commit]
    -   id: black
        language_version: python3.9
        args:
        - --check
        - --line-length=88
        stages: [pre-push]
-   repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
    -   id: isort
        args: ["--profile", "black"]
        stages: [pre-commit]
    -   id: isort
        args:
        - --profile
        - black
        - --check-only
        stages: [pre-push]
-   repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
    -   id: flake8
        args:
        - --max-line-length=88
        - --ignore=E722,E501,W605,W503,E203,E712
        stages: [pre-commit, pre-push]
