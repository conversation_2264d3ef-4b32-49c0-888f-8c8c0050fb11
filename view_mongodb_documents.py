#!/usr/bin/env python3
"""
MongoDB Document Viewer
Pretty prints MongoDB documents in a readable format
"""

import json
from pymongo import MongoClient
from pprint import pprint

def connect_to_mongodb():
    """Connect to MongoDB"""
    try:
        client = MongoClient(
            host="*************",
            port=27017,
            username="admin",
            password="statista123",
            authSource="admin",
            serverSelectionTimeoutMS=5000
        )
        
        # Test connection
        client.admin.command('ping')
        print("✅ Connected to MongoDB")
        
        db = client['statista_insights']
        collection = db['market_insights']
        return collection
        
    except Exception as e:
        print(f"❌ Error connecting to MongoDB: {e}")
        return None

def view_documents(collection, limit=5, chart_name=None, geo_name=None):
    """View documents with pretty formatting"""
    
    # Build query filter
    query = {}
    if chart_name:
        query["unifiedContentMetaData.metaData.chartData.chartName"] = {"$regex": chart_name, "$options": "i"}
    if geo_name:
        query["chartGeos.chartGeo"] = geo_name
    
    print(f"\n📊 Viewing documents (limit: {limit})")
    if query:
        print(f"🔍 Filter: {query}")
    print("=" * 80)
    
    try:
        documents = collection.find(query).limit(limit)
        
        for i, doc in enumerate(documents, 1):
            print(f"\n📈 Document {i}:")
            print("-" * 40)
            
            # Extract key information
            chart_data = doc.get("unifiedContentMetaData", {}).get("metaData", {}).get("chartData", {})
            chart_geos = doc.get("chartGeos", [])
            
            print(f"🏷️  Chart Name: {chart_data.get('chartName', 'N/A')}")
            print(f"🔑 Chart Key: {chart_data.get('chartKey', 'N/A')}")
            print(f"📊 Chart Type: {chart_data.get('chartType', 'N/A')}")
            print(f"📄 Page: {chart_data.get('pageName', 'N/A')}")
            print(f"📖 Chapter: {chart_data.get('chapterName', 'N/A')}")
            print(f"📅 Years: {chart_data.get('minYear', 'N/A')} - {chart_data.get('maxYear', 'N/A')}")
            print(f"📏 Unit: {chart_data.get('unit', 'N/A')}")
            
            print(f"\n🌍 Geographic Data ({len(chart_geos)} regions):")
            for geo in chart_geos:
                geo_name = geo.get('chartGeo', 'Unknown')
                kpis = geo.get('kpis', [])
                print(f"  📍 {geo_name}: {len(kpis)} KPIs")
                
                for kpi in kpis[:2]:  # Show first 2 KPIs
                    kpi_name = kpi.get('kpiName', 'Unknown')
                    years = kpi.get('years', [])
                    values = kpi.get('values', [])
                    print(f"    📈 {kpi_name}")
                    print(f"       Years: {len(years)} data points ({years[:3] if years else []}...)")
                    print(f"       Values: {len(values)} data points ({values[:3] if values else []}...)")
                
                if len(kpis) > 2:
                    print(f"    ... and {len(kpis) - 2} more KPIs")
            
            print("\n" + "=" * 80)
    
    except Exception as e:
        print(f"❌ Error viewing documents: {e}")

def view_full_document(collection, chart_id):
    """View a complete document in pretty JSON format"""
    try:
        doc = collection.find_one({"unifiedContentMetaData.id": int(chart_id)})
        if doc:
            print(f"\n📄 Full Document for Chart ID: {chart_id}")
            print("=" * 80)
            # Remove MongoDB ObjectId for cleaner display
            if '_id' in doc:
                del doc['_id']
            print(json.dumps(doc, indent=2, default=str))
        else:
            print(f"❌ No document found with chart ID: {chart_id}")
    except Exception as e:
        print(f"❌ Error viewing document: {e}")

def get_collection_stats(collection):
    """Get basic statistics about the collection"""
    try:
        total_docs = collection.count_documents({})
        
        # Get unique chart types
        chart_types = collection.distinct("unifiedContentMetaData.metaData.chartData.chartType")
        
        # Get unique geos
        geos = collection.distinct("chartGeos.chartGeo")
        
        # Get unique chart names
        chart_names = collection.distinct("unifiedContentMetaData.metaData.chartData.chartName")
        
        print(f"\n📊 Collection Statistics:")
        print(f"📄 Total Documents: {total_docs}")
        print(f"📈 Unique Chart Types: {len(chart_types)}")
        print(f"🌍 Unique Geographies: {len(geos)}")
        print(f"🏷️  Unique Chart Names: {len(chart_names)}")
        
        print(f"\n📈 Chart Types: {', '.join(chart_types[:10])}{'...' if len(chart_types) > 10 else ''}")
        print(f"🌍 Geographies: {', '.join(geos[:10])}{'...' if len(geos) > 10 else ''}")
        
    except Exception as e:
        print(f"❌ Error getting stats: {e}")

def main():
    """Main interactive function"""
    collection = connect_to_mongodb()
    if collection is None:
        return
    
    while True:
        print("\n🔍 MongoDB Document Viewer")
        print("1. View collection statistics")
        print("2. View documents (summary)")
        print("3. View full document by chart ID")
        print("4. Search by chart name")
        print("5. Search by geography")
        print("6. Exit")
        
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == "1":
            get_collection_stats(collection)
        
        elif choice == "2":
            limit = input("How many documents to view? (default: 5): ").strip()
            limit = int(limit) if limit.isdigit() else 5
            view_documents(collection, limit=limit)
        
        elif choice == "3":
            chart_id = input("Enter chart ID: ").strip()
            if chart_id.isdigit():
                view_full_document(collection, chart_id)
            else:
                print("❌ Please enter a valid chart ID (number)")
        
        elif choice == "4":
            chart_name = input("Enter chart name (partial match): ").strip()
            limit = input("How many documents to view? (default: 5): ").strip()
            limit = int(limit) if limit.isdigit() else 5
            view_documents(collection, limit=limit, chart_name=chart_name)
        
        elif choice == "5":
            geo_name = input("Enter geography name: ").strip()
            limit = input("How many documents to view? (default: 5): ").strip()
            limit = int(limit) if limit.isdigit() else 5
            view_documents(collection, limit=limit, geo_name=geo_name)
        
        elif choice == "6":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please enter 1-6.")

if __name__ == "__main__":
    main()
