#!/bin/bash

# Function to run in background
run_dbt_background() {
    # Load environment variables
    source .env

    # Export the variables so they're available to dbt
    export RDS_USERNAME
    export RDS_PASSWORD
    export RDS_DATABASE

    # Set additional environment variables for long-running queries
    export PGCONNECT_TIMEOUT=60
    export PGCOMMAND_TIMEOUT=14400  # 4 hours in seconds
    export PGSSLMODE=prefer
    export PGKEEPALIVES_IDLE=7200
    export PGKEEPALIVES_INTERVAL=30
    export PGKEEPALIVES_COUNT=9

    # Log start time
    echo "$(date): Starting dbt run for ALL models..."

    # Run dbt with verbose logging and retry logic
    MAX_RETRIES=3
    RETRY_COUNT=0

    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        echo "$(date): Starting dbt run attempt $((RETRY_COUNT + 1))..."

        if dbt run --profiles-dir . --log-level info; then
            echo "$(date): dbt run completed successfully."
            exit 0
        else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                echo "$(date): dbt run failed, retrying in 60 seconds... (attempt $RETRY_COUNT/$MAX_RETRIES)"
                sleep 60
            else
                echo "$(date): dbt run failed after $MAX_RETRIES attempts."
                exit 1
            fi
        fi
    done
}

# Run in background with logging
LOG_FILE="dbt_all_models_$(date +%Y%m%d_%H%M%S).log"
echo "Starting ALL models in background. Log file: $LOG_FILE"
echo "Monitor with: tail -f $LOG_FILE"

nohup bash -c "$(declare -f run_dbt_background); run_dbt_background" > "$LOG_FILE" 2>&1 &

echo "Background process started with PID: $!"
