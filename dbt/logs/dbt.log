[0m09:40:53.810017 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fce37d13df0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fce36c93190>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fce36c93160>]}


============================== 09:40:53.813592 | 10d61fa3-8f1d-44ff-b81c-b75a41a08b80 ==============================
[0m09:40:53.813592 [info ] [MainThread]: Running with dbt=1.10.4
[0m09:40:53.814249 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '.', 'version_check': 'True', 'debug': 'False', 'log_path': '/home/<USER>/market-insights-reports/dbt/logs', 'warn_error': 'None', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'log_format': 'default', 'static_parser': 'True', 'introspect': 'True', 'invocation_command': 'dbt run --profiles-dir .', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'send_anonymous_usage_stats': 'True'}
[0m09:40:53.820034 [error] [MainThread]: Encountered an error:
Parsing Error
  Env var required but not provided: 'RDS_USERNAME'
[0m09:40:53.821947 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 0.07377091, "process_in_blocks": "120", "process_kernel_time": 0.070043, "process_mem_max_rss": "95328", "process_out_blocks": "32", "process_user_time": 1.290782}
[0m09:40:53.822474 [debug] [MainThread]: Command `dbt run` failed at 09:40:53.822353 after 0.07 seconds
[0m09:40:53.822891 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fce37d13df0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fce374d4a00>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fce36cb96d0>]}
[0m09:40:53.823323 [debug] [MainThread]: Flushing usage events
[0m09:40:54.210986 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:42:09.061471 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ff8b5853df0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ff8b47d3190>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ff8b47d3130>]}


============================== 09:42:09.065168 | 275612b6-78b3-43be-a60e-c99b3f086d94 ==============================
[0m09:42:09.065168 [info ] [MainThread]: Running with dbt=1.10.4
[0m09:42:09.065832 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'debug': 'False', 'log_path': '/home/<USER>/market-insights-reports/dbt/logs', 'profiles_dir': '.', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'invocation_command': 'dbt run --profiles-dir .', 'log_format': 'default', 'static_parser': 'True', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m09:42:09.071695 [error] [MainThread]: Encountered an error:
Parsing Error
  Env var required but not provided: 'RDS_USERNAME'
[0m09:42:09.072661 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 0.07264307, "process_in_blocks": "0", "process_kernel_time": 0.079922, "process_mem_max_rss": "95464", "process_out_blocks": "24", "process_user_time": 1.368681}
[0m09:42:09.073209 [debug] [MainThread]: Command `dbt run` failed at 09:42:09.073084 after 0.07 seconds
[0m09:42:09.073636 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ff8b5853df0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ff8b5014a00>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7ff8b47f96d0>]}
[0m09:42:09.074070 [debug] [MainThread]: Flushing usage events
[0m09:42:09.455516 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m09:45:11.007397 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe0494eb0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdf413160>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdf413100>]}


============================== 09:45:11.011115 | 8c3d3efa-f582-4b6b-b70c-399a578b3dfe ==============================
[0m09:45:11.011115 [info ] [MainThread]: Running with dbt=1.10.4
[0m09:45:11.011823 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '.', 'version_check': 'True', 'debug': 'False', 'log_path': '/home/<USER>/market-insights-reports/dbt/logs', 'fail_fast': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'introspect': 'True', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'invocation_command': 'dbt run --profiles-dir .', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m09:45:11.182015 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe1e43220>]}
[0m09:45:11.247721 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdf44df70>]}
[0m09:45:11.248665 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m09:45:11.347639 [debug] [MainThread]: checksum: 073cb5a296270136b24f9c040fdebd0642ef9c263903ef463bfe7e190c538da2, vars: {}, profile: , target: , version: 1.10.4
[0m09:45:11.348494 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m09:45:11.349032 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdecc74f0>]}
[0m09:45:12.656159 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m09:45:12.662082 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdd9c0130>]}
[0m09:45:12.758194 [debug] [MainThread]: Wrote artifact WritableManifest to /home/<USER>/market-insights-reports/dbt/target/manifest.json
[0m09:45:12.760806 [debug] [MainThread]: Wrote artifact SemanticManifest to /home/<USER>/market-insights-reports/dbt/target/semantic_manifest.json
[0m09:45:12.776975 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdd9c22b0>]}
[0m09:45:12.777567 [info ] [MainThread]: Found 17 models, 2 data tests, 434 macros
[0m09:45:12.778013 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdd942be0>]}
[0m09:45:12.779944 [info ] [MainThread]: 
[0m09:45:12.780395 [info ] [MainThread]: Concurrency: 4 threads (target='dev')
[0m09:45:12.780785 [info ] [MainThread]: 
[0m09:45:12.781378 [debug] [MainThread]: Acquiring new postgres connection 'master'
[0m09:45:12.786766 [debug] [ThreadPool]: Acquiring new postgres connection 'list_postgres'
[0m09:45:12.874303 [debug] [ThreadPool]: Using postgres connection "list_postgres"
[0m09:45:12.874812 [debug] [ThreadPool]: On list_postgres: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "connection_name": "list_postgres"} */

    select distinct nspname from pg_namespace
  
[0m09:45:12.875199 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:45:12.899420 [debug] [ThreadPool]: SQL status: SELECT 6 in 0.024 seconds
[0m09:45:12.900713 [debug] [ThreadPool]: On list_postgres: Close
[0m09:45:12.903256 [debug] [ThreadPool]: Re-using an available connection from the pool (formerly list_postgres, now list_postgres_public)
[0m09:45:12.903903 [debug] [ThreadPool]: Acquiring new postgres connection 'list_postgres_public_dbt_test__audit'
[0m09:45:12.910285 [debug] [ThreadPool]: Using postgres connection "list_postgres_public"
[0m09:45:12.912647 [debug] [ThreadPool]: Using postgres connection "list_postgres_public_dbt_test__audit"
[0m09:45:12.913291 [debug] [ThreadPool]: On list_postgres_public: BEGIN
[0m09:45:12.913747 [debug] [ThreadPool]: On list_postgres_public_dbt_test__audit: BEGIN
[0m09:45:12.914162 [debug] [ThreadPool]: Opening a new connection, currently in state closed
[0m09:45:12.914552 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m09:45:12.936979 [debug] [ThreadPool]: SQL status: BEGIN in 0.022 seconds
[0m09:45:12.937343 [debug] [ThreadPool]: SQL status: BEGIN in 0.023 seconds
[0m09:45:12.937734 [debug] [ThreadPool]: Using postgres connection "list_postgres_public_dbt_test__audit"
[0m09:45:12.938131 [debug] [ThreadPool]: Using postgres connection "list_postgres_public"
[0m09:45:12.938547 [debug] [ThreadPool]: On list_postgres_public_dbt_test__audit: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "connection_name": "list_postgres_public_dbt_test__audit"} */
select
      'postgres' as database,
      tablename as name,
      schemaname as schema,
      'table' as type
    from pg_tables
    where schemaname ilike 'public_dbt_test__audit'
    union all
    select
      'postgres' as database,
      viewname as name,
      schemaname as schema,
      'view' as type
    from pg_views
    where schemaname ilike 'public_dbt_test__audit'
    union all
    select
      'postgres' as database,
      matviewname as name,
      schemaname as schema,
      'materialized_view' as type
    from pg_matviews
    where schemaname ilike 'public_dbt_test__audit'
  
[0m09:45:12.939010 [debug] [ThreadPool]: On list_postgres_public: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "connection_name": "list_postgres_public"} */
select
      'postgres' as database,
      tablename as name,
      schemaname as schema,
      'table' as type
    from pg_tables
    where schemaname ilike 'public'
    union all
    select
      'postgres' as database,
      viewname as name,
      schemaname as schema,
      'view' as type
    from pg_views
    where schemaname ilike 'public'
    union all
    select
      'postgres' as database,
      matviewname as name,
      schemaname as schema,
      'materialized_view' as type
    from pg_matviews
    where schemaname ilike 'public'
  
[0m09:45:12.952605 [debug] [ThreadPool]: SQL status: SELECT 27 in 0.013 seconds
[0m09:45:12.954417 [debug] [ThreadPool]: On list_postgres_public: ROLLBACK
[0m09:45:12.954849 [debug] [ThreadPool]: SQL status: SELECT 0 in 0.015 seconds
[0m09:45:12.956082 [debug] [ThreadPool]: On list_postgres_public_dbt_test__audit: ROLLBACK
[0m09:45:12.956441 [debug] [ThreadPool]: On list_postgres_public: Close
[0m09:45:12.957140 [debug] [ThreadPool]: On list_postgres_public_dbt_test__audit: Close
[0m09:45:12.966606 [debug] [MainThread]: Using postgres connection "master"
[0m09:45:12.967018 [debug] [MainThread]: On master: BEGIN
[0m09:45:12.967380 [debug] [MainThread]: Opening a new connection, currently in state init
[0m09:45:12.986161 [debug] [MainThread]: SQL status: BEGIN in 0.019 seconds
[0m09:45:12.986557 [debug] [MainThread]: Using postgres connection "master"
[0m09:45:12.987012 [debug] [MainThread]: On master: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "connection_name": "master"} */
with relation as (
        select
            pg_rewrite.ev_class as class,
            pg_rewrite.oid as id
        from pg_rewrite
    ),
    class as (
        select
            oid as id,
            relname as name,
            relnamespace as schema,
            relkind as kind
        from pg_class
    ),
    dependency as (
        select distinct
            pg_depend.objid as id,
            pg_depend.refobjid as ref
        from pg_depend
    ),
    schema as (
        select
            pg_namespace.oid as id,
            pg_namespace.nspname as name
        from pg_namespace
        where nspname != 'information_schema' and nspname not like 'pg\_%'
    ),
    referenced as (
        select
            relation.id AS id,
            referenced_class.name ,
            referenced_class.schema ,
            referenced_class.kind
        from relation
        join class as referenced_class on relation.class=referenced_class.id
        where referenced_class.kind in ('r', 'v', 'm')
    ),
    relationships as (
        select
            referenced.name as referenced_name,
            referenced.schema as referenced_schema_id,
            dependent_class.name as dependent_name,
            dependent_class.schema as dependent_schema_id,
            referenced.kind as kind
        from referenced
        join dependency on referenced.id=dependency.id
        join class as dependent_class on dependency.ref=dependent_class.id
        where
            (referenced.name != dependent_class.name or
             referenced.schema != dependent_class.schema)
    )

    select
        referenced_schema.name as referenced_schema,
        relationships.referenced_name as referenced_name,
        dependent_schema.name as dependent_schema,
        relationships.dependent_name as dependent_name
    from relationships
    join schema as dependent_schema on relationships.dependent_schema_id=dependent_schema.id
    join schema as referenced_schema on relationships.referenced_schema_id=referenced_schema.id
    group by referenced_schema, referenced_name, dependent_schema, dependent_name
    order by referenced_schema, referenced_name, dependent_schema, dependent_name;
[0m09:45:13.001652 [debug] [MainThread]: SQL status: SELECT 19 in 0.014 seconds
[0m09:45:13.006449 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdd9db250>]}
[0m09:45:13.006957 [debug] [MainThread]: On master: ROLLBACK
[0m09:45:13.007724 [debug] [MainThread]: Using postgres connection "master"
[0m09:45:13.008095 [debug] [MainThread]: On master: BEGIN
[0m09:45:13.009183 [debug] [MainThread]: SQL status: BEGIN in 0.001 seconds
[0m09:45:13.009564 [debug] [MainThread]: On master: COMMIT
[0m09:45:13.009933 [debug] [MainThread]: Using postgres connection "master"
[0m09:45:13.010349 [debug] [MainThread]: On master: COMMIT
[0m09:45:13.011049 [debug] [MainThread]: SQL status: COMMIT in 0.000 seconds
[0m09:45:13.011430 [debug] [MainThread]: On master: Close
[0m09:45:13.015902 [debug] [Thread-1  ]: Began running node model.smi_report.stg_chapters
[0m09:45:13.016417 [debug] [Thread-2  ]: Began running node model.smi_report.stg_chart_kpis
[0m09:45:13.017377 [debug] [Thread-3  ]: Began running node model.smi_report.stg_chart_types
[0m09:45:13.016981 [info ] [Thread-1  ]: 1 of 17 START sql view model public.stg_chapters ............................... [RUN]
[0m09:45:13.017903 [debug] [Thread-4  ]: Began running node model.smi_report.stg_charts
[0m09:45:13.018484 [info ] [Thread-2  ]: 2 of 17 START sql view model public.stg_chart_kpis ............................. [RUN]
[0m09:45:13.019800 [debug] [Thread-1  ]: Re-using an available connection from the pool (formerly list_postgres_public_dbt_test__audit, now model.smi_report.stg_chapters)
[0m09:45:13.019081 [info ] [Thread-3  ]: 3 of 17 START sql view model public.stg_chart_types ............................ [RUN]
[0m09:45:13.020475 [info ] [Thread-4  ]: 4 of 17 START sql view model public.stg_charts ................................. [RUN]
[0m09:45:13.021056 [debug] [Thread-2  ]: Re-using an available connection from the pool (formerly list_postgres_public, now model.smi_report.stg_chart_kpis)
[0m09:45:13.021522 [debug] [Thread-1  ]: Began compiling node model.smi_report.stg_chapters
[0m09:45:13.022095 [debug] [Thread-3  ]: Acquiring new postgres connection 'model.smi_report.stg_chart_types'
[0m09:45:13.022672 [debug] [Thread-4  ]: Acquiring new postgres connection 'model.smi_report.stg_charts'
[0m09:45:13.023138 [debug] [Thread-2  ]: Began compiling node model.smi_report.stg_chart_kpis
[0m09:45:13.031034 [debug] [Thread-1  ]: Writing injected SQL for node "model.smi_report.stg_chapters"
[0m09:45:13.031522 [debug] [Thread-3  ]: Began compiling node model.smi_report.stg_chart_types
[0m09:45:13.031975 [debug] [Thread-4  ]: Began compiling node model.smi_report.stg_charts
[0m09:45:13.034138 [debug] [Thread-2  ]: Writing injected SQL for node "model.smi_report.stg_chart_kpis"
[0m09:45:13.036583 [debug] [Thread-3  ]: Writing injected SQL for node "model.smi_report.stg_chart_types"
[0m09:45:13.038767 [debug] [Thread-4  ]: Writing injected SQL for node "model.smi_report.stg_charts"
[0m09:45:13.039760 [debug] [Thread-4  ]: Began executing node model.smi_report.stg_charts
[0m09:45:13.040443 [debug] [Thread-2  ]: Began executing node model.smi_report.stg_chart_kpis
[0m09:45:13.052461 [debug] [Thread-3  ]: Began executing node model.smi_report.stg_chart_types
[0m09:45:13.052959 [debug] [Thread-1  ]: Began executing node model.smi_report.stg_chapters
[0m09:45:13.089906 [debug] [Thread-2  ]: Writing runtime sql for node "model.smi_report.stg_chart_kpis"
[0m09:45:13.091264 [debug] [Thread-4  ]: Writing runtime sql for node "model.smi_report.stg_charts"
[0m09:45:13.095824 [debug] [Thread-3  ]: Writing runtime sql for node "model.smi_report.stg_chart_types"
[0m09:45:13.100440 [debug] [Thread-1  ]: Writing runtime sql for node "model.smi_report.stg_chapters"
[0m09:45:13.101710 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_chart_kpis"
[0m09:45:13.102197 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_charts"
[0m09:45:13.102682 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_chart_types"
[0m09:45:13.103312 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: BEGIN
[0m09:45:13.103778 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_chapters"
[0m09:45:13.104227 [debug] [Thread-4  ]: On model.smi_report.stg_charts: BEGIN
[0m09:45:13.104683 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: BEGIN
[0m09:45:13.105137 [debug] [Thread-2  ]: Opening a new connection, currently in state closed
[0m09:45:13.105594 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: BEGIN
[0m09:45:13.106043 [debug] [Thread-4  ]: Opening a new connection, currently in state init
[0m09:45:13.106489 [debug] [Thread-3  ]: Opening a new connection, currently in state init
[0m09:45:13.107155 [debug] [Thread-1  ]: Opening a new connection, currently in state closed
[0m09:45:13.145645 [debug] [Thread-4  ]: SQL status: BEGIN in 0.039 seconds
[0m09:45:13.146366 [debug] [Thread-3  ]: SQL status: BEGIN in 0.040 seconds
[0m09:45:13.146870 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_charts"
[0m09:45:13.147288 [debug] [Thread-2  ]: SQL status: BEGIN in 0.042 seconds
[0m09:45:13.147778 [debug] [Thread-1  ]: SQL status: BEGIN in 0.041 seconds
[0m09:45:13.148175 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_chart_types"
[0m09:45:13.148652 [debug] [Thread-4  ]: On model.smi_report.stg_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_charts"} */

  create view "postgres"."public"."stg_charts__dbt_tmp"
    
    
  as (
    -- models/staging/stg_charts.sql

with source as (

    select * from public.charts

),

cleaned as (

    select
        id,
        "idParent" as parent_id,
        "idChapter" as chapter_id,
        "chartType" as chart_type,
        "chartKey" as chart_key,
        "orderChart" as order_chart,
        "minYear" as min_year,
        "maxYear" as max_year,
        "idUnit" as unit_id,
        scale,
        "numberDecimal" as number_decimal,
        "mainTitle" as main_title,
        description,
        info,
        "timeUpdated" as time_updated
    from source

)

select * from cleaned
  );
[0m09:45:13.149163 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_chart_kpis"
[0m09:45:13.149618 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_chapters"
[0m09:45:13.150083 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_types"} */

  create view "postgres"."public"."stg_chart_types__dbt_tmp"
    
    
  as (
    -- models/staging/stg_chart_types.sql

with source as (

    select * from public."chartsTypes"

),

cleaned as (

    select
        id,
        "chartType" as chart_type
    from source

)

select * from cleaned
  );
[0m09:45:13.150660 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_kpis"} */

  create view "postgres"."public"."stg_chart_kpis__dbt_tmp"
    
    
  as (
    -- models/staging/stg_chart_kpis.sql

with source as (

    select * from public."chartsKpis"

),

cleaned as (

    select
        id,
        "idChart" as chart_id,
        "idKpi" as kpi_id,
        "aggregationType" as aggregation_type,
        "orderChart" as order_chart
    from source

)

select * from cleaned
  );
[0m09:45:13.151160 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chapters"} */

  create view "postgres"."public"."stg_chapters__dbt_tmp"
    
    
  as (
    -- models/staging/stg_chapters.sql

with source as (

    select * from public.chapters

),

cleaned as (

    select
        id,
        "idPage" as page_id,
        name,
        "orderChapter" as order_chapter
    from source

)

select * from cleaned
  );
[0m09:45:13.176362 [debug] [Thread-1  ]: SQL status: CREATE VIEW in 0.025 seconds
[0m09:45:13.176769 [debug] [Thread-3  ]: SQL status: CREATE VIEW in 0.025 seconds
[0m09:45:13.177146 [debug] [Thread-4  ]: SQL status: CREATE VIEW in 0.027 seconds
[0m09:45:13.177526 [debug] [Thread-2  ]: SQL status: CREATE VIEW in 0.026 seconds
[0m09:45:13.185398 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_chapters"
[0m09:45:13.189325 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_chart_types"
[0m09:45:13.192980 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_charts"
[0m09:45:13.197991 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_chart_kpis"
[0m09:45:13.198471 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chapters"} */
alter table "postgres"."public"."stg_chapters" rename to "stg_chapters__dbt_backup"
[0m09:45:13.198941 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_types"} */
alter table "postgres"."public"."stg_chart_types" rename to "stg_chart_types__dbt_backup"
[0m09:45:13.199443 [debug] [Thread-4  ]: On model.smi_report.stg_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_charts"} */
alter table "postgres"."public"."stg_charts" rename to "stg_charts__dbt_backup"
[0m09:45:13.199920 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_kpis"} */
alter table "postgres"."public"."stg_chart_kpis" rename to "stg_chart_kpis__dbt_backup"
[0m09:45:13.201798 [debug] [Thread-3  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.202193 [debug] [Thread-2  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.202639 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.205909 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_chart_types"
[0m09:45:13.206314 [debug] [Thread-4  ]: SQL status: ALTER TABLE in 0.006 seconds
[0m09:45:13.209489 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_chart_kpis"
[0m09:45:13.212679 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_chapters"
[0m09:45:13.213157 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_types"} */
alter table "postgres"."public"."stg_chart_types__dbt_tmp" rename to "stg_chart_types"
[0m09:45:13.216303 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_charts"
[0m09:45:13.216782 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_kpis"} */
alter table "postgres"."public"."stg_chart_kpis__dbt_tmp" rename to "stg_chart_kpis"
[0m09:45:13.217242 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chapters"} */
alter table "postgres"."public"."stg_chapters__dbt_tmp" rename to "stg_chapters"
[0m09:45:13.217787 [debug] [Thread-4  ]: On model.smi_report.stg_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_charts"} */
alter table "postgres"."public"."stg_charts__dbt_tmp" rename to "stg_charts"
[0m09:45:13.218340 [debug] [Thread-3  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.218903 [debug] [Thread-2  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.219360 [debug] [Thread-4  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.244756 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: COMMIT
[0m09:45:13.246300 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: COMMIT
[0m09:45:13.246717 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.028 seconds
[0m09:45:13.248282 [debug] [Thread-4  ]: On model.smi_report.stg_charts: COMMIT
[0m09:45:13.248755 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_chart_kpis"
[0m09:45:13.249241 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_chart_types"
[0m09:45:13.250853 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: COMMIT
[0m09:45:13.251330 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_charts"
[0m09:45:13.251792 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: COMMIT
[0m09:45:13.252248 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: COMMIT
[0m09:45:13.252699 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_chapters"
[0m09:45:13.253148 [debug] [Thread-4  ]: On model.smi_report.stg_charts: COMMIT
[0m09:45:13.253760 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: COMMIT
[0m09:45:13.254841 [debug] [Thread-2  ]: SQL status: COMMIT in 0.001 seconds
[0m09:45:13.255263 [debug] [Thread-3  ]: SQL status: COMMIT in 0.002 seconds
[0m09:45:13.255666 [debug] [Thread-1  ]: SQL status: COMMIT in 0.001 seconds
[0m09:45:13.256032 [debug] [Thread-4  ]: SQL status: COMMIT in 0.002 seconds
[0m09:45:13.262432 [debug] [Thread-2  ]: Applying DROP to: "postgres"."public"."stg_chart_kpis__dbt_backup"
[0m09:45:13.266253 [debug] [Thread-3  ]: Applying DROP to: "postgres"."public"."stg_chart_types__dbt_backup"
[0m09:45:13.268981 [debug] [Thread-1  ]: Applying DROP to: "postgres"."public"."stg_chapters__dbt_backup"
[0m09:45:13.271639 [debug] [Thread-4  ]: Applying DROP to: "postgres"."public"."stg_charts__dbt_backup"
[0m09:45:13.276136 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_chart_kpis"
[0m09:45:13.277029 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_chart_types"
[0m09:45:13.277895 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_chapters"
[0m09:45:13.278753 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_charts"
[0m09:45:13.279242 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_kpis"} */
drop view if exists "postgres"."public"."stg_chart_kpis__dbt_backup" cascade
[0m09:45:13.279716 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chart_types"} */
drop view if exists "postgres"."public"."stg_chart_types__dbt_backup" cascade
[0m09:45:13.280179 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_chapters"} */
drop view if exists "postgres"."public"."stg_chapters__dbt_backup" cascade
[0m09:45:13.280643 [debug] [Thread-4  ]: On model.smi_report.stg_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_charts"} */
drop view if exists "postgres"."public"."stg_charts__dbt_backup" cascade
[0m09:45:13.288222 [debug] [Thread-2  ]: SQL status: DROP VIEW in 0.007 seconds
[0m09:45:13.291045 [debug] [Thread-2  ]: On model.smi_report.stg_chart_kpis: Close
[0m09:45:13.291579 [debug] [Thread-1  ]: SQL status: DROP VIEW in 0.010 seconds
[0m09:45:13.292044 [debug] [Thread-4  ]: SQL status: DROP VIEW in 0.011 seconds
[0m09:45:13.292436 [debug] [Thread-3  ]: SQL status: DROP VIEW in 0.011 seconds
[0m09:45:13.293849 [debug] [Thread-1  ]: On model.smi_report.stg_chapters: Close
[0m09:45:13.295688 [debug] [Thread-2  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdf482460>]}
[0m09:45:13.296999 [debug] [Thread-4  ]: On model.smi_report.stg_charts: Close
[0m09:45:13.298312 [debug] [Thread-3  ]: On model.smi_report.stg_chart_types: Close
[0m09:45:13.299111 [debug] [Thread-1  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe1b97c70>]}
[0m09:45:13.300552 [debug] [Thread-4  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe1028130>]}
[0m09:45:13.299828 [info ] [Thread-2  ]: 2 of 17 OK created sql view model public.stg_chart_kpis ........................ [[32mCREATE VIEW[0m in 0.27s]
[0m09:45:13.301316 [debug] [Thread-3  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdfcf2040>]}
[0m09:45:13.302039 [info ] [Thread-1  ]: 1 of 17 OK created sql view model public.stg_chapters .......................... [[32mCREATE VIEW[0m in 0.28s]
[0m09:45:13.302801 [info ] [Thread-4  ]: 4 of 17 OK created sql view model public.stg_charts ............................ [[32mCREATE VIEW[0m in 0.28s]
[0m09:45:13.303513 [debug] [Thread-2  ]: Finished running node model.smi_report.stg_chart_kpis
[0m09:45:13.304183 [info ] [Thread-3  ]: 3 of 17 OK created sql view model public.stg_chart_types ....................... [[32mCREATE VIEW[0m in 0.28s]
[0m09:45:13.304885 [debug] [Thread-1  ]: Finished running node model.smi_report.stg_chapters
[0m09:45:13.305486 [debug] [Thread-4  ]: Finished running node model.smi_report.stg_charts
[0m09:45:13.305990 [debug] [Thread-2  ]: Began running node model.smi_report.stg_geos
[0m09:45:13.306691 [debug] [Thread-3  ]: Finished running node model.smi_report.stg_chart_types
[0m09:45:13.307169 [debug] [Thread-1  ]: Began running node model.smi_report.stg_kpis
[0m09:45:13.307709 [debug] [Thread-4  ]: Began running node model.smi_report.stg_kpis_values
[0m09:45:13.308309 [info ] [Thread-2  ]: 5 of 17 START sql view model public.stg_geos ................................... [RUN]
[0m09:45:13.308881 [debug] [Thread-3  ]: Began running node model.smi_report.stg_pages
[0m09:45:13.309515 [info ] [Thread-1  ]: 6 of 17 START sql view model public.stg_kpis ................................... [RUN]
[0m09:45:13.310169 [info ] [Thread-4  ]: 7 of 17 START sql view model public.stg_kpis_values ............................ [RUN]
[0m09:45:13.310748 [debug] [Thread-2  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_chart_kpis, now model.smi_report.stg_geos)
[0m09:45:13.311317 [info ] [Thread-3  ]: 8 of 17 START sql view model public.stg_pages .................................. [RUN]
[0m09:45:13.311891 [debug] [Thread-1  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_chapters, now model.smi_report.stg_kpis)
[0m09:45:13.312391 [debug] [Thread-4  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_charts, now model.smi_report.stg_kpis_values)
[0m09:45:13.312849 [debug] [Thread-2  ]: Began compiling node model.smi_report.stg_geos
[0m09:45:13.313336 [debug] [Thread-3  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_chart_types, now model.smi_report.stg_pages)
[0m09:45:13.313790 [debug] [Thread-1  ]: Began compiling node model.smi_report.stg_kpis
[0m09:45:13.314249 [debug] [Thread-4  ]: Began compiling node model.smi_report.stg_kpis_values
[0m09:45:13.316569 [debug] [Thread-2  ]: Writing injected SQL for node "model.smi_report.stg_geos"
[0m09:45:13.317040 [debug] [Thread-3  ]: Began compiling node model.smi_report.stg_pages
[0m09:45:13.319156 [debug] [Thread-1  ]: Writing injected SQL for node "model.smi_report.stg_kpis"
[0m09:45:13.321298 [debug] [Thread-4  ]: Writing injected SQL for node "model.smi_report.stg_kpis_values"
[0m09:45:13.321983 [debug] [Thread-2  ]: Began executing node model.smi_report.stg_geos
[0m09:45:13.324047 [debug] [Thread-3  ]: Writing injected SQL for node "model.smi_report.stg_pages"
[0m09:45:13.324786 [debug] [Thread-1  ]: Began executing node model.smi_report.stg_kpis
[0m09:45:13.330481 [debug] [Thread-2  ]: Writing runtime sql for node "model.smi_report.stg_geos"
[0m09:45:13.330966 [debug] [Thread-4  ]: Began executing node model.smi_report.stg_kpis_values
[0m09:45:13.335547 [debug] [Thread-1  ]: Writing runtime sql for node "model.smi_report.stg_kpis"
[0m09:45:13.336245 [debug] [Thread-3  ]: Began executing node model.smi_report.stg_pages
[0m09:45:13.336812 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_geos"
[0m09:45:13.341037 [debug] [Thread-4  ]: Writing runtime sql for node "model.smi_report.stg_kpis_values"
[0m09:45:13.341710 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_kpis"
[0m09:45:13.345963 [debug] [Thread-3  ]: Writing runtime sql for node "model.smi_report.stg_pages"
[0m09:45:13.346430 [debug] [Thread-2  ]: On model.smi_report.stg_geos: BEGIN
[0m09:45:13.347057 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: BEGIN
[0m09:45:13.347482 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_kpis_values"
[0m09:45:13.348084 [debug] [Thread-2  ]: Opening a new connection, currently in state closed
[0m09:45:13.348590 [debug] [Thread-1  ]: Opening a new connection, currently in state closed
[0m09:45:13.349027 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_pages"
[0m09:45:13.349484 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: BEGIN
[0m09:45:13.350322 [debug] [Thread-3  ]: On model.smi_report.stg_pages: BEGIN
[0m09:45:13.350923 [debug] [Thread-4  ]: Opening a new connection, currently in state closed
[0m09:45:13.351510 [debug] [Thread-3  ]: Opening a new connection, currently in state closed
[0m09:45:13.387579 [debug] [Thread-2  ]: SQL status: BEGIN in 0.039 seconds
[0m09:45:13.388048 [debug] [Thread-3  ]: SQL status: BEGIN in 0.037 seconds
[0m09:45:13.388503 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_geos"
[0m09:45:13.389125 [debug] [Thread-4  ]: SQL status: BEGIN in 0.038 seconds
[0m09:45:13.389619 [debug] [Thread-1  ]: SQL status: BEGIN in 0.041 seconds
[0m09:45:13.390005 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_pages"
[0m09:45:13.390462 [debug] [Thread-2  ]: On model.smi_report.stg_geos: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_geos"} */

  create view "postgres"."public"."stg_geos__dbt_tmp"
    
    
  as (
    -- models/staging/stg_geos.sql

with source as (

    select * from public.geos

),

cleaned as (

    select
        id,
        name,
        "isoCode3L" as iso
    from source

)

select * from cleaned
  );
[0m09:45:13.390924 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_kpis_values"
[0m09:45:13.391364 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_kpis"
[0m09:45:13.391853 [debug] [Thread-3  ]: On model.smi_report.stg_pages: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_pages"} */

  create view "postgres"."public"."stg_pages__dbt_tmp"
    
    
  as (
    -- models/staging/stg_pages.sql

with source as (

    select * from public.pages

),

cleaned as (

    select
        id,
        "idVerticalGraph" as vertical_graph_id,
        "idPlatform" as platform_id,
        "idGeo" as geo_id,
        name,
        "outlookName" as outlook_name,
        definition,
        "keyTakeAway" as key_take_away,
        "inScope" as in_scope,
        "outOfScope" as out_scope,
        "searchTags" as search_tags,
        "placeholderScript" as placeholder_script,
        "metaDescription" as meta_description
    from source
    where "idPlatform" = 2
    and "isActive" = 1
)

select * from cleaned
  );
[0m09:45:13.392448 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis_values"} */

  create view "postgres"."public"."stg_kpis_values__dbt_tmp"
    
    
  as (
    -- models/staging/stg_kpis_values.sql

with source as (

    select * from public."kpisValues"

),

cleaned as (

    select
        id,
        "idGeo" as geo_id,
        "idUnit" as unit_id,
        "idKpi" as kpi_id,
        extract(year from "valueTime") as value_time,
        "timeUpdated" as time_updated,
        "idResponsible" as responsible_id,
        value
    from source

)

select * from cleaned
  );
[0m09:45:13.392955 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis"} */

  create view "postgres"."public"."stg_kpis__dbt_tmp"
    
    
  as (
    -- models/staging/stg_kpis.sql

with source as (

    select * from public.kpis

),

cleaned as (

    select
        id,
        "kpiKey" as kpi_key,
        "idBrand" as brand_id,
        "idVerticalGraph" as vertical_graph_id,
        name,
        "kpiType" as kpi_type,
        "isSummable" as is_summable,
        "isCurrency" as is_currency
    from source

)

select * from cleaned
  );
[0m09:45:13.394714 [debug] [Thread-2  ]: SQL status: CREATE VIEW in 0.002 seconds
[0m09:45:13.398486 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_geos"
[0m09:45:13.398959 [debug] [Thread-1  ]: SQL status: CREATE VIEW in 0.005 seconds
[0m09:45:13.399376 [debug] [Thread-2  ]: On model.smi_report.stg_geos: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_geos"} */
alter table "postgres"."public"."stg_geos" rename to "stg_geos__dbt_backup"
[0m09:45:13.399774 [debug] [Thread-3  ]: SQL status: CREATE VIEW in 0.006 seconds
[0m09:45:13.403402 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_kpis"
[0m09:45:13.403851 [debug] [Thread-4  ]: SQL status: CREATE VIEW in 0.010 seconds
[0m09:45:13.407549 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_pages"
[0m09:45:13.407948 [debug] [Thread-2  ]: SQL status: ALTER TABLE in 0.004 seconds
[0m09:45:13.408386 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis"} */
alter table "postgres"."public"."stg_kpis" rename to "stg_kpis__dbt_backup"
[0m09:45:13.412608 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_kpis_values"
[0m09:45:13.413098 [debug] [Thread-3  ]: On model.smi_report.stg_pages: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_pages"} */
alter table "postgres"."public"."stg_pages" rename to "stg_pages__dbt_backup"
[0m09:45:13.416315 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_geos"
[0m09:45:13.417001 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis_values"} */
alter table "postgres"."public"."stg_kpis_values" rename to "stg_kpis_values__dbt_backup"
[0m09:45:13.417688 [debug] [Thread-2  ]: On model.smi_report.stg_geos: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_geos"} */
alter table "postgres"."public"."stg_geos__dbt_tmp" rename to "stg_geos"
[0m09:45:13.418445 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.418987 [debug] [Thread-2  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.419383 [debug] [Thread-3  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.419831 [debug] [Thread-4  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.422949 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_kpis"
[0m09:45:13.424874 [debug] [Thread-2  ]: On model.smi_report.stg_geos: COMMIT
[0m09:45:13.428124 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_pages"
[0m09:45:13.431294 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_kpis_values"
[0m09:45:13.431779 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis"} */
alter table "postgres"."public"."stg_kpis__dbt_tmp" rename to "stg_kpis"
[0m09:45:13.432242 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_geos"
[0m09:45:13.432696 [debug] [Thread-3  ]: On model.smi_report.stg_pages: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_pages"} */
alter table "postgres"."public"."stg_pages__dbt_tmp" rename to "stg_pages"
[0m09:45:13.433157 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis_values"} */
alter table "postgres"."public"."stg_kpis_values__dbt_tmp" rename to "stg_kpis_values"
[0m09:45:13.433700 [debug] [Thread-2  ]: On model.smi_report.stg_geos: COMMIT
[0m09:45:13.434349 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.436015 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: COMMIT
[0m09:45:13.436448 [debug] [Thread-3  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.436877 [debug] [Thread-2  ]: SQL status: COMMIT in 0.003 seconds
[0m09:45:13.437268 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_kpis"
[0m09:45:13.437640 [debug] [Thread-4  ]: SQL status: ALTER TABLE in 0.003 seconds
[0m09:45:13.439269 [debug] [Thread-3  ]: On model.smi_report.stg_pages: COMMIT
[0m09:45:13.441973 [debug] [Thread-2  ]: Applying DROP to: "postgres"."public"."stg_geos__dbt_backup"
[0m09:45:13.442439 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: COMMIT
[0m09:45:13.444068 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: COMMIT
[0m09:45:13.444540 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_pages"
[0m09:45:13.445389 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_geos"
[0m09:45:13.445948 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_kpis_values"
[0m09:45:13.446400 [debug] [Thread-3  ]: On model.smi_report.stg_pages: COMMIT
[0m09:45:13.446862 [debug] [Thread-2  ]: On model.smi_report.stg_geos: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_geos"} */
drop view if exists "postgres"."public"."stg_geos__dbt_backup" cascade
[0m09:45:13.447270 [debug] [Thread-1  ]: SQL status: COMMIT in 0.001 seconds
[0m09:45:13.447722 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: COMMIT
[0m09:45:13.451880 [debug] [Thread-3  ]: SQL status: COMMIT in 0.004 seconds
[0m09:45:13.453347 [debug] [Thread-4  ]: SQL status: COMMIT in 0.002 seconds
[0m09:45:13.462603 [debug] [Thread-4  ]: Applying DROP to: "postgres"."public"."stg_kpis_values__dbt_backup"
[0m09:45:13.451128 [debug] [Thread-1  ]: Applying DROP to: "postgres"."public"."stg_kpis__dbt_backup"
[0m09:45:13.465324 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_kpis"
[0m09:45:13.465927 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis"} */
drop view if exists "postgres"."public"."stg_kpis__dbt_backup" cascade
[0m09:45:13.452628 [debug] [Thread-2  ]: SQL status: DROP VIEW in 0.004 seconds
[0m09:45:13.468140 [debug] [Thread-2  ]: On model.smi_report.stg_geos: Close
[0m09:45:13.469347 [debug] [Thread-2  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe06529d0>]}
[0m09:45:13.470086 [debug] [Thread-1  ]: SQL status: DROP VIEW in 0.004 seconds
[0m09:45:13.473134 [debug] [Thread-1  ]: On model.smi_report.stg_kpis: Close
[0m09:45:13.474350 [debug] [Thread-1  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbde07b8b0>]}
[0m09:45:13.471079 [info ] [Thread-2  ]: 5 of 17 OK created sql view model public.stg_geos .............................. [[32mCREATE VIEW[0m in 0.16s]
[0m09:45:13.463968 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_kpis_values"
[0m09:45:13.476886 [debug] [Thread-2  ]: Finished running node model.smi_report.stg_geos
[0m09:45:13.477588 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_kpis_values"} */
drop view if exists "postgres"."public"."stg_kpis_values__dbt_backup" cascade
[0m09:45:13.478021 [debug] [Thread-3  ]: Applying DROP to: "postgres"."public"."stg_pages__dbt_backup"
[0m09:45:13.478982 [debug] [Thread-2  ]: Began running node model.smi_report.stg_units
[0m09:45:13.480434 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.stg_pages"
[0m09:45:13.482052 [debug] [Thread-3  ]: On model.smi_report.stg_pages: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_pages"} */
drop view if exists "postgres"."public"."stg_pages__dbt_backup" cascade
[0m09:45:13.483006 [debug] [Thread-4  ]: SQL status: DROP VIEW in 0.003 seconds
[0m09:45:13.485035 [debug] [Thread-4  ]: On model.smi_report.stg_kpis_values: Close
[0m09:45:13.486015 [debug] [Thread-4  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbddd8f490>]}
[0m09:45:13.487668 [debug] [Thread-3  ]: SQL status: DROP VIEW in 0.005 seconds
[0m09:45:13.489754 [debug] [Thread-3  ]: On model.smi_report.stg_pages: Close
[0m09:45:13.490815 [debug] [Thread-3  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdecb5f70>]}
[0m09:45:13.481456 [info ] [Thread-2  ]: 9 of 17 START sql view model public.stg_units .................................. [RUN]
[0m09:45:13.492725 [debug] [Thread-2  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_geos, now model.smi_report.stg_units)
[0m09:45:13.493436 [debug] [Thread-2  ]: Began compiling node model.smi_report.stg_units
[0m09:45:13.497169 [debug] [Thread-2  ]: Writing injected SQL for node "model.smi_report.stg_units"
[0m09:45:13.487007 [info ] [Thread-4  ]: 7 of 17 OK created sql view model public.stg_kpis_values ....................... [[32mCREATE VIEW[0m in 0.17s]
[0m09:45:13.498475 [debug] [Thread-4  ]: Finished running node model.smi_report.stg_kpis_values
[0m09:45:13.499253 [debug] [Thread-4  ]: Began running node model.smi_report.stg_vertical_graphs
[0m09:45:13.475420 [info ] [Thread-1  ]: 6 of 17 OK created sql view model public.stg_kpis .............................. [[32mCREATE VIEW[0m in 0.16s]
[0m09:45:13.501049 [debug] [Thread-1  ]: Finished running node model.smi_report.stg_kpis
[0m09:45:13.501791 [debug] [Thread-1  ]: Began running node model.smi_report.stg_verticals
[0m09:45:13.492014 [info ] [Thread-3  ]: 8 of 17 OK created sql view model public.stg_pages ............................. [[32mCREATE VIEW[0m in 0.18s]
[0m09:45:13.504322 [debug] [Thread-3  ]: Finished running node model.smi_report.stg_pages
[0m09:45:13.502745 [debug] [Thread-2  ]: Began executing node model.smi_report.stg_units
[0m09:45:13.505004 [debug] [Thread-3  ]: Began running node model.smi_report.core_kpis_to_values
[0m09:45:13.513807 [info ] [Thread-3  ]: 12 of 17 START sql incremental model public.core_kpis_to_values ................ [RUN]
[0m09:45:13.514563 [debug] [Thread-3  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_pages, now model.smi_report.core_kpis_to_values)
[0m09:45:13.515222 [debug] [Thread-3  ]: Began compiling node model.smi_report.core_kpis_to_values
[0m09:45:13.500224 [info ] [Thread-4  ]: 10 of 17 START sql view model public.stg_vertical_graphs ....................... [RUN]
[0m09:45:13.522022 [debug] [Thread-4  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_kpis_values, now model.smi_report.stg_vertical_graphs)
[0m09:45:13.522667 [debug] [Thread-4  ]: Began compiling node model.smi_report.stg_vertical_graphs
[0m09:45:13.526174 [debug] [Thread-4  ]: Writing injected SQL for node "model.smi_report.stg_vertical_graphs"
[0m09:45:13.527011 [debug] [Thread-4  ]: Began executing node model.smi_report.stg_vertical_graphs
[0m09:45:13.503510 [info ] [Thread-1  ]: 11 of 17 START sql view model public.stg_verticals ............................. [RUN]
[0m09:45:13.534408 [debug] [Thread-1  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_kpis, now model.smi_report.stg_verticals)
[0m09:45:13.535074 [debug] [Thread-1  ]: Began compiling node model.smi_report.stg_verticals
[0m09:45:13.521284 [debug] [Thread-3  ]: Writing injected SQL for node "model.smi_report.core_kpis_to_values"
[0m09:45:13.541042 [debug] [Thread-3  ]: Began executing node model.smi_report.core_kpis_to_values
[0m09:45:13.512834 [debug] [Thread-2  ]: Writing runtime sql for node "model.smi_report.stg_units"
[0m09:45:13.554606 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_units"
[0m09:45:13.555228 [debug] [Thread-2  ]: On model.smi_report.stg_units: BEGIN
[0m09:45:13.555863 [debug] [Thread-2  ]: Opening a new connection, currently in state closed
[0m09:45:13.556954 [debug] [Thread-4  ]: Writing runtime sql for node "model.smi_report.stg_vertical_graphs"
[0m09:45:13.557795 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_vertical_graphs"
[0m09:45:13.558437 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: BEGIN
[0m09:45:13.559087 [debug] [Thread-4  ]: Opening a new connection, currently in state closed
[0m09:45:13.539904 [debug] [Thread-1  ]: Writing injected SQL for node "model.smi_report.stg_verticals"
[0m09:45:13.560175 [debug] [Thread-1  ]: Began executing node model.smi_report.stg_verticals
[0m09:45:13.567439 [debug] [Thread-1  ]: Writing runtime sql for node "model.smi_report.stg_verticals"
[0m09:45:13.568276 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_verticals"
[0m09:45:13.568926 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: BEGIN
[0m09:45:13.609619 [debug] [Thread-1  ]: Opening a new connection, currently in state closed
[0m09:45:13.610339 [debug] [Thread-2  ]: SQL status: BEGIN in 0.054 seconds
[0m09:45:13.615666 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_units"
[0m09:45:13.616333 [debug] [Thread-2  ]: On model.smi_report.stg_units: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_units"} */

  create view "postgres"."public"."stg_units__dbt_tmp"
    
    
  as (
    -- models/staging/stg_units.sql

with source as (

    select * from public.units

),

cleaned as (

    select
        id,
        name
        magnitude,
        "typeUnit" as unit_type
    from source

)

select * from cleaned
  );
[0m09:45:13.618921 [debug] [Thread-2  ]: SQL status: CREATE VIEW in 0.002 seconds
[0m09:45:13.641346 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_units"
[0m09:45:13.684542 [debug] [Thread-2  ]: On model.smi_report.stg_units: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_units"} */
alter table "postgres"."public"."stg_units" rename to "stg_units__dbt_backup"
[0m09:45:13.712791 [debug] [Thread-1  ]: SQL status: BEGIN in 0.103 seconds
[0m09:45:13.713544 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_verticals"
[0m09:45:13.714219 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_verticals"} */

  create view "postgres"."public"."stg_verticals__dbt_tmp"
    
    
  as (
    -- models/staging/stg_verticals.sql

with source as (

    select * from public.verticals

),

cleaned as (

    select
        "idVertical" as id,
        "nameVertical" as name_vertical
    from source

)

select * from cleaned
  );
[0m09:45:13.720761 [debug] [Thread-4  ]: SQL status: BEGIN in 0.162 seconds
[0m09:45:13.721437 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_vertical_graphs"
[0m09:45:13.722126 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_vertical_graphs"} */

  create view "postgres"."public"."stg_vertical_graphs__dbt_tmp"
    
    
  as (
    -- models/staging/stg_vertical_graphs.sql

with source as (

    select * from public."verticalGraphs"

),

cleaned as (

    select
        id,
        "idVertical" as vertical_id,
        "idVerticalParent" as vertical_parent_id,
        "marketName" as market_name,
        "typeVertical" as type_vertical
    from source
    where "typeVertical" in ('outlook', 'market')
)

select * from cleaned
  );
[0m09:45:13.722937 [debug] [Thread-2  ]: SQL status: ALTER TABLE in 0.010 seconds
[0m09:45:13.728884 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_units"
[0m09:45:13.744389 [debug] [Thread-2  ]: On model.smi_report.stg_units: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_units"} */
alter table "postgres"."public"."stg_units__dbt_tmp" rename to "stg_units"
[0m09:45:13.729618 [debug] [Thread-4  ]: SQL status: CREATE VIEW in 0.007 seconds
[0m09:45:13.730220 [debug] [Thread-1  ]: SQL status: CREATE VIEW in 0.015 seconds
[0m09:45:13.758080 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_verticals"
[0m09:45:13.751994 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_vertical_graphs"
[0m09:45:13.759637 [debug] [Thread-2  ]: SQL status: ALTER TABLE in 0.015 seconds
[0m09:45:13.771750 [debug] [Thread-2  ]: On model.smi_report.stg_units: COMMIT
[0m09:45:13.772396 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_units"
[0m09:45:13.773017 [debug] [Thread-2  ]: On model.smi_report.stg_units: COMMIT
[0m09:45:13.758736 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_verticals"} */
alter table "postgres"."public"."stg_verticals" rename to "stg_verticals__dbt_backup"
[0m09:45:13.768736 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_vertical_graphs"} */
alter table "postgres"."public"."stg_vertical_graphs" rename to "stg_vertical_graphs__dbt_backup"
[0m09:45:13.775100 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.core_kpis_to_values"
[0m09:45:13.775732 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.776337 [debug] [Thread-4  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.788272 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_vertical_graphs"
[0m09:45:13.776911 [debug] [Thread-2  ]: SQL status: COMMIT in 0.003 seconds
[0m09:45:13.783333 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_verticals"
[0m09:45:13.794006 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_verticals"} */
alter table "postgres"."public"."stg_verticals__dbt_tmp" rename to "stg_verticals"
[0m09:45:13.777692 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_kpis_to_values"} */

    
  
    

  create temporary table "core_kpis_to_values__dbt_tmp094513687941"
  
  
    as
  
  (
    -- models/core/core_kpis_to_values.sql

-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table



with kpis as (
    select * from "postgres"."public"."stg_kpis"
),

kpis_values as (
    select * from "postgres"."public"."stg_kpis_values"
),

kpis_to_values as (
    select
        kpis.id as kpi_id,
        kpis.name,
        kpis.kpi_key,
        kpis_values.geo_id,
        kpis_values.unit_id,
        kpis_values.value_time,
        kpis_values.time_updated,
        kpis_values.responsible_id,
        kpis_values.value
    from kpis
    left join kpis_values on kpis.id = kpis_values.kpi_id
    where kpis_values.value is not null
)

select * from kpis_to_values
  );
  
  
[0m09:45:13.794890 [debug] [Thread-3  ]: Opening a new connection, currently in state closed
[0m09:45:13.788924 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_vertical_graphs"} */
alter table "postgres"."public"."stg_vertical_graphs__dbt_tmp" rename to "stg_vertical_graphs"
[0m09:45:13.795669 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.793271 [debug] [Thread-2  ]: Applying DROP to: "postgres"."public"."stg_units__dbt_backup"
[0m09:45:13.801493 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.stg_units"
[0m09:45:13.802466 [debug] [Thread-2  ]: On model.smi_report.stg_units: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_units"} */
drop view if exists "postgres"."public"."stg_units__dbt_backup" cascade
[0m09:45:13.800081 [debug] [Thread-4  ]: SQL status: ALTER TABLE in 0.003 seconds
[0m09:45:13.805566 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: COMMIT
[0m09:45:13.806342 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_vertical_graphs"
[0m09:45:13.807047 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: COMMIT
[0m09:45:13.798896 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: COMMIT
[0m09:45:13.807934 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_verticals"
[0m09:45:13.809295 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: COMMIT
[0m09:45:13.808660 [debug] [Thread-2  ]: SQL status: DROP VIEW in 0.005 seconds
[0m09:45:13.813020 [debug] [Thread-2  ]: On model.smi_report.stg_units: Close
[0m09:45:13.820399 [debug] [Thread-2  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdc8ec670>]}
[0m09:45:13.813817 [debug] [Thread-1  ]: SQL status: COMMIT in 0.003 seconds
[0m09:45:13.810000 [debug] [Thread-4  ]: SQL status: COMMIT in 0.002 seconds
[0m09:45:13.832712 [debug] [Thread-4  ]: Applying DROP to: "postgres"."public"."stg_vertical_graphs__dbt_backup"
[0m09:45:13.825863 [debug] [Thread-1  ]: Applying DROP to: "postgres"."public"."stg_verticals__dbt_backup"
[0m09:45:13.821491 [info ] [Thread-2  ]: 9 of 17 OK created sql view model public.stg_units ............................. [[32mCREATE VIEW[0m in 0.33s]
[0m09:45:13.837115 [debug] [Thread-2  ]: Finished running node model.smi_report.stg_units
[0m09:45:13.838251 [debug] [Thread-2  ]: Began running node model.smi_report.core_pages_to_charts
[0m09:45:13.839171 [info ] [Thread-2  ]: 13 of 17 START sql view model public.core_pages_to_charts ...................... [RUN]
[0m09:45:13.840082 [debug] [Thread-2  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_units, now model.smi_report.core_pages_to_charts)
[0m09:45:13.840812 [debug] [Thread-2  ]: Began compiling node model.smi_report.core_pages_to_charts
[0m09:45:13.834570 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.stg_vertical_graphs"
[0m09:45:13.835910 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.stg_verticals"
[0m09:45:13.846905 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_vertical_graphs"} */
drop view if exists "postgres"."public"."stg_vertical_graphs__dbt_backup" cascade
[0m09:45:13.850340 [debug] [Thread-2  ]: Writing injected SQL for node "model.smi_report.core_pages_to_charts"
[0m09:45:13.850897 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.stg_verticals"} */
drop view if exists "postgres"."public"."stg_verticals__dbt_backup" cascade
[0m09:45:13.852366 [debug] [Thread-2  ]: Began executing node model.smi_report.core_pages_to_charts
[0m09:45:13.857244 [debug] [Thread-2  ]: Writing runtime sql for node "model.smi_report.core_pages_to_charts"
[0m09:45:13.857668 [debug] [Thread-4  ]: SQL status: DROP VIEW in 0.006 seconds
[0m09:45:13.858051 [debug] [Thread-1  ]: SQL status: DROP VIEW in 0.006 seconds
[0m09:45:13.859628 [debug] [Thread-4  ]: On model.smi_report.stg_vertical_graphs: Close
[0m09:45:13.861005 [debug] [Thread-1  ]: On model.smi_report.stg_verticals: Close
[0m09:45:13.861492 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.core_pages_to_charts"
[0m09:45:13.862306 [debug] [Thread-4  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbddd8f490>]}
[0m09:45:13.862886 [debug] [Thread-1  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe1028130>]}
[0m09:45:13.863348 [debug] [Thread-2  ]: On model.smi_report.core_pages_to_charts: BEGIN
[0m09:45:13.864089 [info ] [Thread-4  ]: 10 of 17 OK created sql view model public.stg_vertical_graphs .................. [[32mCREATE VIEW[0m in 0.34s]
[0m09:45:13.864746 [info ] [Thread-1  ]: 11 of 17 OK created sql view model public.stg_verticals ........................ [[32mCREATE VIEW[0m in 0.33s]
[0m09:45:13.865417 [debug] [Thread-2  ]: Opening a new connection, currently in state closed
[0m09:45:13.866040 [debug] [Thread-4  ]: Finished running node model.smi_report.stg_vertical_graphs
[0m09:45:13.866684 [debug] [Thread-1  ]: Finished running node model.smi_report.stg_verticals
[0m09:45:13.867747 [debug] [Thread-4  ]: Began running node model.smi_report.core_vertical_graphs_verticals
[0m09:45:13.868455 [info ] [Thread-4  ]: 14 of 17 START sql view model public.core_vertical_graphs_verticals ............ [RUN]
[0m09:45:13.869029 [debug] [Thread-4  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_vertical_graphs, now model.smi_report.core_vertical_graphs_verticals)
[0m09:45:13.869474 [debug] [Thread-4  ]: Began compiling node model.smi_report.core_vertical_graphs_verticals
[0m09:45:13.873667 [debug] [Thread-4  ]: Writing injected SQL for node "model.smi_report.core_vertical_graphs_verticals"
[0m09:45:13.874791 [debug] [Thread-4  ]: Began executing node model.smi_report.core_vertical_graphs_verticals
[0m09:45:13.882321 [debug] [Thread-4  ]: Writing runtime sql for node "model.smi_report.core_vertical_graphs_verticals"
[0m09:45:13.883214 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.core_vertical_graphs_verticals"
[0m09:45:13.883898 [debug] [Thread-4  ]: On model.smi_report.core_vertical_graphs_verticals: BEGIN
[0m09:45:13.884529 [debug] [Thread-4  ]: Opening a new connection, currently in state closed
[0m09:45:13.898465 [debug] [Thread-2  ]: SQL status: BEGIN in 0.033 seconds
[0m09:45:13.898954 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.core_pages_to_charts"
[0m09:45:13.899684 [debug] [Thread-2  ]: On model.smi_report.core_pages_to_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_pages_to_charts"} */

  create view "postgres"."public"."core_pages_to_charts__dbt_tmp"
    
    
  as (
    -- models/core/core_pages_to_charts.sql

with pages as (
    select * from "postgres"."public"."stg_pages"
),

chapters as (
    select * from "postgres"."public"."stg_chapters"
),

charts as (
    select * from "postgres"."public"."stg_charts"
),

chart_types as (
    select * from "postgres"."public"."stg_chart_types"
),

units as (
    select * from "postgres"."public"."stg_units"
),

geos as (
    select * from "postgres"."public"."stg_geos"
),

pages_to_charts as (
    select
        pages.id as page_id,
        vertical_graph_id,
        pages.name as page_name,
        pages.geo_id,
        geos.name as geo_name,
        geos.iso as geo_iso,
        outlook_name,
        definition,
        key_take_away,
        in_scope,
        out_scope,
        chapters.id as chapter_id,
        chapters.name as chapter_name,
        order_chapter,
        charts.id as chart_id,
        chart_key,
        chart_types.chart_type,
        order_chart,
        charts.min_year,
        charts.max_year,
        units.magnitude as unit,
        units.unit_type,
        scale,
        number_decimal,
        charts.main_title as chart_title,
        charts.description,
        charts.info,
        charts.time_updated
    from pages
    left join geos on pages.geo_id = geos.id
    left join chapters on pages.id = chapters.page_id
    left join charts on chapters.id = charts.chapter_id
    left join chart_types on charts.chart_type = chart_types.id
    left join units on charts.unit_id = units.id
)

select * from pages_to_charts
  );
[0m09:45:13.906152 [debug] [Thread-2  ]: SQL status: CREATE VIEW in 0.006 seconds
[0m09:45:13.909618 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.core_pages_to_charts"
[0m09:45:13.910158 [debug] [Thread-2  ]: On model.smi_report.core_pages_to_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_pages_to_charts"} */
alter table "postgres"."public"."core_pages_to_charts__dbt_tmp" rename to "core_pages_to_charts"
[0m09:45:13.910871 [debug] [Thread-4  ]: SQL status: BEGIN in 0.026 seconds
[0m09:45:13.911297 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.core_vertical_graphs_verticals"
[0m09:45:13.911787 [debug] [Thread-4  ]: On model.smi_report.core_vertical_graphs_verticals: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_vertical_graphs_verticals"} */

  create view "postgres"."public"."core_vertical_graphs_verticals__dbt_tmp"
    
    
  as (
    -- models/core/core_vertical_graphs_verticals.sql

with verticals as (
    select * from "postgres"."public"."stg_verticals"
),

vertical_graphs as (
    select * from "postgres"."public"."stg_vertical_graphs"
),

vertical_graphs_verticals as (
    select
        vertical_graphs.id as vertical_graph_id,
        vertical_graphs.vertical_id,
        vertical_graphs.vertical_parent_id,
        verticals.name_vertical,
        vertical_graphs.market_name
    from vertical_graphs
    left join verticals on verticals.id = vertical_graphs.vertical_id
)

select * from vertical_graphs_verticals
  );
[0m09:45:13.912339 [debug] [Thread-2  ]: SQL status: ALTER TABLE in 0.002 seconds
[0m09:45:13.914235 [debug] [Thread-2  ]: On model.smi_report.core_pages_to_charts: COMMIT
[0m09:45:13.914880 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.core_pages_to_charts"
[0m09:45:13.915461 [debug] [Thread-2  ]: On model.smi_report.core_pages_to_charts: COMMIT
[0m09:45:13.916173 [debug] [Thread-4  ]: SQL status: CREATE VIEW in 0.003 seconds
[0m09:45:13.921474 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.core_vertical_graphs_verticals"
[0m09:45:13.922163 [debug] [Thread-4  ]: On model.smi_report.core_vertical_graphs_verticals: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_vertical_graphs_verticals"} */
alter table "postgres"."public"."core_vertical_graphs_verticals__dbt_tmp" rename to "core_vertical_graphs_verticals"
[0m09:45:13.923689 [debug] [Thread-4  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:45:13.924296 [debug] [Thread-2  ]: SQL status: COMMIT in 0.007 seconds
[0m09:45:13.930981 [debug] [Thread-2  ]: Applying DROP to: "postgres"."public"."core_pages_to_charts__dbt_backup"
[0m09:45:13.933623 [debug] [Thread-2  ]: Using postgres connection "model.smi_report.core_pages_to_charts"
[0m09:45:13.934293 [debug] [Thread-2  ]: On model.smi_report.core_pages_to_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_pages_to_charts"} */
drop view if exists "postgres"."public"."core_pages_to_charts__dbt_backup" cascade
[0m09:45:13.926600 [debug] [Thread-4  ]: On model.smi_report.core_vertical_graphs_verticals: COMMIT
[0m09:45:13.935089 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.core_vertical_graphs_verticals"
[0m09:45:13.935759 [debug] [Thread-4  ]: On model.smi_report.core_vertical_graphs_verticals: COMMIT
[0m09:45:13.936472 [debug] [Thread-2  ]: SQL status: DROP VIEW in 0.002 seconds
[0m09:45:13.938500 [debug] [Thread-2  ]: On model.smi_report.core_pages_to_charts: Close
[0m09:45:13.939627 [debug] [Thread-2  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdc828be0>]}
[0m09:45:13.940688 [info ] [Thread-2  ]: 13 of 17 OK created sql view model public.core_pages_to_charts ................. [[32mCREATE VIEW[0m in 0.10s]
[0m09:45:13.941680 [debug] [Thread-2  ]: Finished running node model.smi_report.core_pages_to_charts
[0m09:45:13.942491 [debug] [Thread-4  ]: SQL status: COMMIT in 0.006 seconds
[0m09:45:14.022350 [debug] [Thread-4  ]: Applying DROP to: "postgres"."public"."core_vertical_graphs_verticals__dbt_backup"
[0m09:45:14.023615 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.core_vertical_graphs_verticals"
[0m09:45:14.024092 [debug] [Thread-4  ]: On model.smi_report.core_vertical_graphs_verticals: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_vertical_graphs_verticals"} */
drop view if exists "postgres"."public"."core_vertical_graphs_verticals__dbt_backup" cascade
[0m09:45:14.025099 [debug] [Thread-4  ]: SQL status: DROP VIEW in 0.001 seconds
[0m09:45:14.026560 [debug] [Thread-4  ]: On model.smi_report.core_vertical_graphs_verticals: Close
[0m09:45:14.027350 [debug] [Thread-4  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdc77ab20>]}
[0m09:45:14.028101 [info ] [Thread-4  ]: 14 of 17 OK created sql view model public.core_vertical_graphs_verticals ....... [[32mCREATE VIEW[0m in 0.16s]
[0m09:45:14.029115 [debug] [Thread-4  ]: Finished running node model.smi_report.core_vertical_graphs_verticals
[0m09:45:14.030048 [debug] [Thread-1  ]: Began running node model.smi_report.mart_verticals_to_charts
[0m09:45:14.030885 [info ] [Thread-1  ]: 15 of 17 START sql table model public.mart_verticals_to_charts ................. [RUN]
[0m09:45:14.031647 [debug] [Thread-1  ]: Re-using an available connection from the pool (formerly model.smi_report.stg_verticals, now model.smi_report.mart_verticals_to_charts)
[0m09:45:14.032272 [debug] [Thread-1  ]: Began compiling node model.smi_report.mart_verticals_to_charts
[0m09:45:14.037011 [debug] [Thread-1  ]: Writing injected SQL for node "model.smi_report.mart_verticals_to_charts"
[0m09:45:14.037727 [debug] [Thread-1  ]: Began executing node model.smi_report.mart_verticals_to_charts
[0m09:45:14.052359 [debug] [Thread-1  ]: Writing runtime sql for node "model.smi_report.mart_verticals_to_charts"
[0m09:45:14.053069 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.mart_verticals_to_charts"
[0m09:45:14.053523 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: BEGIN
[0m09:45:14.053948 [debug] [Thread-1  ]: Opening a new connection, currently in state closed
[0m09:45:14.074465 [debug] [Thread-1  ]: SQL status: BEGIN in 0.020 seconds
[0m09:45:14.074912 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.mart_verticals_to_charts"
[0m09:45:14.075402 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.mart_verticals_to_charts"} */

  
    

  create  table "postgres"."public"."mart_verticals_to_charts__dbt_tmp"
  
  
    as
  
  (
    -- models/marts/mart_verticals_to_charts.sql

with vertical_graphs_verticals as (
    select * from "postgres"."public"."core_vertical_graphs_verticals"
),

-- this will be for the parents
vertical_graphs_verticals2 as (
    select * from "postgres"."public"."core_vertical_graphs_verticals"
),

pages_to_charts as (
    select * from "postgres"."public"."core_pages_to_charts"
),

verticals_to_charts as (
    select
        vgv.market_name as market_insights_name,
        vgv.vertical_parent_id,
        vgv2.name_vertical as parent_name,
        vgv.vertical_id,
        vgv.name_vertical as market_name,
        ptc.geo_id,
        ptc.geo_name,
        ptc.order_chapter,
        ptc.chapter_name,
        ptc.order_chart,
        ptc.chart_title as chart_name,
        split_part(ptc.chart_key, '_', 1) as chart_key,
        ptc.chart_type,
        ptc.min_year,
        ptc.max_year,
        ptc.unit,
        ptc.unit_type,
        ptc.time_updated
    from vertical_graphs_verticals as vgv
    inner join vertical_graphs_verticals2 as vgv2 on vgv2.vertical_id = vgv.vertical_parent_id
    inner join pages_to_charts as ptc on ptc.vertical_graph_id = vgv.vertical_graph_id
    where LOWER(vgv.market_name) not in (LOWER('digital market outlook'), LOWER('market outlook highlights'), LOWER('trash outlook'))
    and ptc.chapter_name not in ('Analyst Opinion', 'Methodology', 'Key Market Indicators')
    order by vgv.market_name, parent_name, vgv.name_vertical, ptc.geo_id, ptc.order_chapter, ptc.order_chart
)


select * from verticals_to_charts
  );
  
[0m09:46:48.095571 [debug] [Thread-1  ]: SQL status: SELECT 2095693 in 94.020 seconds
[0m09:46:48.103641 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.mart_verticals_to_charts"
[0m09:46:48.104164 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.mart_verticals_to_charts"} */
alter table "postgres"."public"."mart_verticals_to_charts" rename to "mart_verticals_to_charts__dbt_backup"
[0m09:46:48.110524 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.006 seconds
[0m09:46:48.113986 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.mart_verticals_to_charts"
[0m09:46:48.114439 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.mart_verticals_to_charts"} */
alter table "postgres"."public"."mart_verticals_to_charts__dbt_tmp" rename to "mart_verticals_to_charts"
[0m09:46:48.115604 [debug] [Thread-1  ]: SQL status: ALTER TABLE in 0.001 seconds
[0m09:46:48.117250 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: COMMIT
[0m09:46:48.117693 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.mart_verticals_to_charts"
[0m09:46:48.118116 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: COMMIT
[0m09:46:48.119971 [debug] [Thread-1  ]: SQL status: COMMIT in 0.001 seconds
[0m09:46:48.122606 [debug] [Thread-1  ]: Applying DROP to: "postgres"."public"."mart_verticals_to_charts__dbt_backup"
[0m09:46:48.125359 [debug] [Thread-1  ]: Using postgres connection "model.smi_report.mart_verticals_to_charts"
[0m09:46:48.125813 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.mart_verticals_to_charts"} */
drop table if exists "postgres"."public"."mart_verticals_to_charts__dbt_backup" cascade
[0m09:46:48.147628 [debug] [Thread-1  ]: SQL status: DROP TABLE in 0.021 seconds
[0m09:46:48.149194 [debug] [Thread-1  ]: On model.smi_report.mart_verticals_to_charts: Close
[0m09:46:48.150037 [debug] [Thread-1  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdc892df0>]}
[0m09:46:48.150804 [info ] [Thread-1  ]: 15 of 17 OK created sql table model public.mart_verticals_to_charts ............ [[32mSELECT 2095693[0m in 94.12s]
[0m09:46:48.151548 [debug] [Thread-1  ]: Finished running node model.smi_report.mart_verticals_to_charts
[0m09:47:23.048774 [debug] [Thread-3  ]: SQL status: SELECT 59445068 in 129.254 seconds
[0m09:47:23.065548 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.core_kpis_to_values"
[0m09:47:23.066098 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: BEGIN
[0m09:47:23.068108 [debug] [Thread-3  ]: SQL status: BEGIN in 0.002 seconds
[0m09:47:23.068544 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.core_kpis_to_values"
[0m09:47:23.068986 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_kpis_to_values"} */

      select
          column_name,
          data_type,
          character_maximum_length,
          numeric_precision,
          numeric_scale

      from INFORMATION_SCHEMA.columns
      where table_name = 'core_kpis_to_values__dbt_tmp094513687941'
        
      order by ordinal_position

  
[0m09:47:23.110116 [debug] [Thread-3  ]: SQL status: SELECT 9 in 0.041 seconds
[0m09:47:23.116191 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.core_kpis_to_values"
[0m09:47:23.116690 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_kpis_to_values"} */

      select
          column_name,
          data_type,
          character_maximum_length,
          numeric_precision,
          numeric_scale

      from "postgres".INFORMATION_SCHEMA.columns
      where table_name = 'core_kpis_to_values'
        
        and table_schema = 'public'
        
      order by ordinal_position

  
[0m09:47:23.120512 [debug] [Thread-3  ]: SQL status: SELECT 9 in 0.003 seconds
[0m09:47:23.128746 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.core_kpis_to_values"
[0m09:47:23.129247 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_kpis_to_values"} */

      select
          column_name,
          data_type,
          character_maximum_length,
          numeric_precision,
          numeric_scale

      from "postgres".INFORMATION_SCHEMA.columns
      where table_name = 'core_kpis_to_values'
        
        and table_schema = 'public'
        
      order by ordinal_position

  
[0m09:47:23.132552 [debug] [Thread-3  ]: SQL status: SELECT 9 in 0.003 seconds
[0m09:47:23.147422 [debug] [Thread-3  ]: Writing runtime sql for node "model.smi_report.core_kpis_to_values"
[0m09:47:23.148118 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.core_kpis_to_values"
[0m09:47:23.148576 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_kpis_to_values"} */

      
        
        
        delete from "postgres"."public"."core_kpis_to_values" as DBT_INTERNAL_DEST
        where (kpi_id) in (
            select distinct kpi_id
            from "core_kpis_to_values__dbt_tmp094513687941" as DBT_INTERNAL_SOURCE
        );

    

    insert into "postgres"."public"."core_kpis_to_values" ("kpi_id", "name", "kpi_key", "geo_id", "unit_id", "value_time", "time_updated", "responsible_id", "value")
    (
        select "kpi_id", "name", "kpi_key", "geo_id", "unit_id", "value_time", "time_updated", "responsible_id", "value"
        from "core_kpis_to_values__dbt_tmp094513687941"
    )
  
[0m10:01:05.317832 [debug] [Thread-3  ]: SQL status: INSERT 0 59445068 in 822.169 seconds
[0m10:01:05.319899 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: COMMIT
[0m10:01:05.320411 [debug] [Thread-3  ]: Using postgres connection "model.smi_report.core_kpis_to_values"
[0m10:01:05.320842 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: COMMIT
[0m10:01:05.422383 [debug] [Thread-3  ]: SQL status: COMMIT in 0.101 seconds
[0m10:01:05.423419 [debug] [Thread-3  ]: On model.smi_report.core_kpis_to_values: Close
[0m10:01:05.424262 [debug] [Thread-3  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdecb5f70>]}
[0m10:01:05.425032 [info ] [Thread-3  ]: 12 of 17 OK created sql incremental model public.core_kpis_to_values ........... [[32mINSERT 0 59445068[0m in 951.91s]
[0m10:01:05.425735 [debug] [Thread-3  ]: Finished running node model.smi_report.core_kpis_to_values
[0m10:01:05.426655 [debug] [Thread-4  ]: Began running node model.smi_report.core_array_agg_values
[0m10:01:05.427348 [info ] [Thread-4  ]: 16 of 17 START sql incremental model public.core_array_agg_values .............. [RUN]
[0m10:01:05.428112 [debug] [Thread-4  ]: Re-using an available connection from the pool (formerly model.smi_report.core_vertical_graphs_verticals, now model.smi_report.core_array_agg_values)
[0m10:01:05.428749 [debug] [Thread-4  ]: Began compiling node model.smi_report.core_array_agg_values
[0m10:01:05.433549 [debug] [Thread-4  ]: Writing injected SQL for node "model.smi_report.core_array_agg_values"
[0m10:01:05.434217 [debug] [Thread-4  ]: Began executing node model.smi_report.core_array_agg_values
[0m10:01:05.439377 [debug] [Thread-4  ]: Using postgres connection "model.smi_report.core_array_agg_values"
[0m10:01:05.439869 [debug] [Thread-4  ]: On model.smi_report.core_array_agg_values: /* {"app": "dbt", "dbt_version": "1.10.4", "profile_name": "smi_report", "target_name": "dev", "node_id": "model.smi_report.core_array_agg_values"} */

    
  
    

  create temporary table "core_array_agg_values__dbt_tmp100105437106"
  
  
    as
  
  (
    -- models/core/core_array_agg_values.sql

-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table



with kv as (
    select * from "postgres"."public"."core_kpis_to_values"
),

aav as (
    SELECT
    kv.kpi_id,
    kv.name,
    kv.kpi_key,
    kv.geo_id,
    kv.unit_id,
    array_agg(kv.value_time ORDER BY kv.value_time) AS value_time_list,
    array_agg(kv.value ORDER BY kv.value_time) AS value_list
FROM
    kv
GROUP BY
    kv.kpi_id, kv.name, kv.kpi_key, kv.geo_id, kv.unit_id
HAVING
    array_length(array_agg(kv.value) FILTER (WHERE kv.value IS NOT NULL), 1) > 0
)

SELECT * FROM aav
  );
  
  
[0m10:01:05.440314 [debug] [Thread-4  ]: Opening a new connection, currently in state closed
[0m12:02:56.600399 [debug] [Thread-4  ]: Postgres adapter: Postgres error: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.
server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

[0m12:02:56.601294 [debug] [Thread-4  ]: On model.smi_report.core_array_agg_values: Close
[0m12:02:56.604315 [debug] [Thread-4  ]: Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request.
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request.
[0m12:02:56.604959 [debug] [Thread-4  ]: Sending event: {'category': 'dbt', 'action': 'run_model', 'label': '8c3d3efa-f582-4b6b-b70c-399a578b3dfe', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdecb59d0>]}
[0m12:02:56.605741 [error] [Thread-4  ]: 16 of 17 ERROR creating sql incremental model public.core_array_agg_values ..... [[31mERROR[0m in 7311.18s]
[0m12:02:56.606448 [debug] [Thread-4  ]: Finished running node model.smi_report.core_array_agg_values
[0m12:02:56.607051 [debug] [Thread-7  ]: Marking all children of 'model.smi_report.core_array_agg_values' to be skipped because of status 'error'.  Reason: Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request.
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request..
[0m12:02:56.607950 [debug] [Thread-1  ]: Began running node model.smi_report.core_pages_to_agg_values
[0m12:02:56.608453 [info ] [Thread-1  ]: 17 of 17 SKIP relation public.core_pages_to_agg_values ......................... [[33mSKIP[0m]
[0m12:02:56.608975 [debug] [Thread-1  ]: Finished running node model.smi_report.core_pages_to_agg_values
[0m12:02:56.610353 [debug] [MainThread]: Using postgres connection "master"
[0m12:02:56.610738 [debug] [MainThread]: On master: BEGIN
[0m12:02:56.611092 [debug] [MainThread]: Opening a new connection, currently in state closed
[0m12:02:56.646727 [debug] [MainThread]: SQL status: BEGIN in 0.036 seconds
[0m12:02:56.647165 [debug] [MainThread]: On master: COMMIT
[0m12:02:56.647564 [debug] [MainThread]: Using postgres connection "master"
[0m12:02:56.647928 [debug] [MainThread]: On master: COMMIT
[0m12:02:56.648649 [debug] [MainThread]: SQL status: COMMIT in 0.000 seconds
[0m12:02:56.649047 [debug] [MainThread]: On master: Close
[0m12:02:56.649655 [debug] [MainThread]: Connection 'master' was properly closed.
[0m12:02:56.650045 [debug] [MainThread]: Connection 'model.smi_report.core_pages_to_charts' was properly closed.
[0m12:02:56.650379 [debug] [MainThread]: Connection 'model.smi_report.mart_verticals_to_charts' was properly closed.
[0m12:02:56.650704 [debug] [MainThread]: Connection 'model.smi_report.core_kpis_to_values' was properly closed.
[0m12:02:56.651029 [debug] [MainThread]: Connection 'model.smi_report.core_array_agg_values' was properly closed.
[0m12:02:56.651524 [info ] [MainThread]: 
[0m12:02:56.651964 [info ] [MainThread]: Finished running 3 incremental models, 1 table model, 13 view models in 2 hours 17 minutes and 43.87 seconds (8263.87s).
[0m12:02:56.654643 [debug] [MainThread]: Command end result
[0m12:02:56.684754 [debug] [MainThread]: Wrote artifact WritableManifest to /home/<USER>/market-insights-reports/dbt/target/manifest.json
[0m12:02:56.686465 [debug] [MainThread]: Wrote artifact SemanticManifest to /home/<USER>/market-insights-reports/dbt/target/semantic_manifest.json
[0m12:02:56.693803 [debug] [MainThread]: Wrote artifact RunExecutionResult to /home/<USER>/market-insights-reports/dbt/target/run_results.json
[0m12:02:56.694196 [info ] [MainThread]: 
[0m12:02:56.694660 [info ] [MainThread]: [31mCompleted with 1 error, 0 partial successes, and 0 warnings:[0m
[0m12:02:56.695079 [info ] [MainThread]: 
[0m12:02:56.695576 [error] [MainThread]: [31mFailure in model core_array_agg_values (models/core/core_array_agg_values.sql)[0m
[0m12:02:56.696065 [error] [MainThread]:   Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request.
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request.
[0m12:02:56.696463 [info ] [MainThread]: 
[0m12:02:56.696919 [info ] [MainThread]:   compiled code at target/compiled/smi_report/models/core/core_array_agg_values.sql
[0m12:02:56.697337 [info ] [MainThread]: 
[0m12:02:56.697766 [info ] [MainThread]: Done. PASS=15 WARN=0 ERROR=1 SKIP=1 NO-OP=0 TOTAL=17
[0m12:02:56.698750 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 8265.752, "process_in_blocks": "1416", "process_kernel_time": 0.17037, "process_mem_max_rss": "131260", "process_out_blocks": "3472", "process_user_time": 4.339411}
[0m12:02:56.699280 [debug] [MainThread]: Command `dbt run` failed at 12:02:56.699163 after 8265.75 seconds
[0m12:02:56.699745 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe0494eb0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbe10892e0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fcbdc95abb0>]}
[0m12:02:56.700210 [debug] [MainThread]: Flushing usage events
[0m12:02:57.179449 [debug] [MainThread]: An error was encountered while trying to flush usage events


============================== 15:28:33.475108 | 4d395003-d404-47cd-b7ec-2067f372b7ea ==============================
[0m15:28:33.475108 [info ] [MainThread]: Running with dbt=1.10.4
[0m15:28:33.476438 [warn ] [MainThread]: [[33mWARNING[0m]: Deprecated functionality
Usage of `--models`, `--model`, and `-m` is deprecated in favor of `--select` or
`-s`.
[0m15:28:33.712335 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m15:28:33.923395 [info ] [MainThread]: Unable to do partial parsing because profile has changed
[0m15:28:35.198489 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m15:28:35.361001 [info ] [MainThread]: Found 17 models, 2 data tests, 434 macros
[0m15:28:35.362369 [warn ] [MainThread]: The selection criterion 'core_array_agg_values,core_pages_to_agg_values' does not match any enabled nodes
[0m15:28:35.363401 [warn ] [MainThread]: Nothing to do. Try checking your model configs and model specification args
[0m15:28:35.408327 [warn ] [MainThread]: [[33mWARNING[0m][DeprecationsSummary]: Deprecated functionality
Summary of encountered deprecations:
- ModelParamUsageDeprecation: 1 occurrence
To see all deprecation instances instead of just the first occurrence of each,
run command again with the `--show-all-deprecations` flag. You may also need to
run with `--no-partial-parse` as some deprecations are only encountered during
parsing.
[0m15:30:07.608864 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f85e4713e20>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f85e36931f0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f85e3693190>]}


============================== 15:30:07.612697 | 8852ba6b-abdf-455a-824f-643480c89dc1 ==============================
[0m15:30:07.612697 [info ] [MainThread]: Running with dbt=1.10.4
[0m15:30:07.613384 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'profiles_dir': '.', 'log_path': '/home/<USER>/market-insights-reports/dbt/logs', 'fail_fast': 'False', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'introspect': 'True', 'log_format': 'default', 'target_path': 'None', 'invocation_command': 'dbt list --profiles-dir .', 'send_anonymous_usage_stats': 'True'}
[0m15:30:07.619287 [error] [MainThread]: Encountered an error:
Parsing Error
  Env var required but not provided: 'RDS_USERNAME'
[0m15:30:07.620383 [debug] [MainThread]: Resource report: {"command_name": "list", "command_success": false, "command_wall_clock_time": 0.072345585, "process_in_blocks": "0", "process_kernel_time": 0.079421, "process_mem_max_rss": "95536", "process_out_blocks": "16", "process_user_time": 1.270744}
[0m15:30:07.620915 [debug] [MainThread]: Command `dbt list` failed at 15:30:07.620794 after 0.07 seconds
[0m15:30:07.621343 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f85e4713e20>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f85e37196a0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f85e3719400>]}
[0m15:30:07.621786 [debug] [MainThread]: Flushing usage events
[0m15:30:08.007653 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m15:30:20.923047 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f63206c1f10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631f643160>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631f643100>]}


============================== 15:30:20.926849 | 0589c3b3-a562-452d-bb00-63be0e56ebc4 ==============================
[0m15:30:20.926849 [info ] [MainThread]: Running with dbt=1.10.4
[0m15:30:20.927542 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '.', 'debug': 'False', 'fail_fast': 'False', 'log_path': '/home/<USER>/market-insights-reports/dbt/logs', 'warn_error': 'None', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'invocation_command': 'dbt list --profiles-dir .', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m15:30:21.098514 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '0589c3b3-a562-452d-bb00-63be0e56ebc4', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631f37e3a0>]}
[0m15:30:21.162562 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '0589c3b3-a562-452d-bb00-63be0e56ebc4', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631f86c0a0>]}
[0m15:30:21.163509 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m15:30:21.261518 [debug] [MainThread]: checksum: 073cb5a296270136b24f9c040fdebd0642ef9c263903ef463bfe7e190c538da2, vars: {}, profile: , target: , version: 1.10.4
[0m15:30:21.391900 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m15:30:21.392391 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m15:30:21.399513 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m15:30:21.444918 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '0589c3b3-a562-452d-bb00-63be0e56ebc4', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631e358130>]}
[0m15:30:21.536534 [debug] [MainThread]: Wrote artifact WritableManifest to /home/<USER>/market-insights-reports/dbt/target/manifest.json
[0m15:30:21.538595 [debug] [MainThread]: Wrote artifact SemanticManifest to /home/<USER>/market-insights-reports/dbt/target/semantic_manifest.json
[0m15:30:21.598885 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '0589c3b3-a562-452d-bb00-63be0e56ebc4', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631e3f1160>]}
[0m15:30:21.600326 [info ] [MainThread]: smi_report.core.core_array_agg_values
[0m15:30:21.601043 [info ] [MainThread]: smi_report.core.core_kpis_to_values
[0m15:30:21.601599 [info ] [MainThread]: smi_report.core.core_pages_to_agg_values
[0m15:30:21.602168 [info ] [MainThread]: smi_report.core.core_pages_to_charts
[0m15:30:21.602776 [info ] [MainThread]: smi_report.core.core_vertical_graphs_verticals
[0m15:30:21.603304 [info ] [MainThread]: smi_report.marts.mart_verticals_to_charts
[0m15:30:21.603804 [info ] [MainThread]: smi_report.staging.stg_chapters
[0m15:30:21.604362 [info ] [MainThread]: smi_report.staging.stg_chart_kpis
[0m15:30:21.605055 [info ] [MainThread]: smi_report.staging.stg_chart_types
[0m15:30:21.605594 [info ] [MainThread]: smi_report.staging.stg_charts
[0m15:30:21.606163 [info ] [MainThread]: smi_report.staging.stg_geos
[0m15:30:21.606686 [info ] [MainThread]: smi_report.staging.stg_kpis
[0m15:30:21.607220 [info ] [MainThread]: smi_report.staging.stg_kpis_values
[0m15:30:21.607794 [info ] [MainThread]: smi_report.staging.stg_pages
[0m15:30:21.608316 [info ] [MainThread]: smi_report.staging.stg_units
[0m15:30:21.608876 [info ] [MainThread]: smi_report.staging.stg_vertical_graphs
[0m15:30:21.609532 [info ] [MainThread]: smi_report.staging.stg_verticals
[0m15:30:21.610171 [info ] [MainThread]: smi_report.core.not_null_core_kpis_to_values_kpi_id
[0m15:30:21.610798 [info ] [MainThread]: smi_report.core.not_null_core_kpis_to_values_value
[0m15:30:21.611867 [debug] [MainThread]: Resource report: {"command_name": "list", "command_success": true, "command_wall_clock_time": 0.7493978, "process_in_blocks": "0", "process_kernel_time": 0.109439, "process_mem_max_rss": "109540", "process_out_blocks": "1040", "process_user_time": 1.920161}
[0m15:30:21.612448 [debug] [MainThread]: Command `dbt list` succeeded at 15:30:21.612287 after 0.75 seconds
[0m15:30:21.612937 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f63206c1f10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631e3f1160>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7f631e315c70>]}
[0m15:30:21.613396 [debug] [MainThread]: Flushing usage events
[0m15:30:21.993537 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m15:31:41.132383 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c5fd2fa0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c4f54190>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c4f54130>]}


============================== 15:31:41.136110 | b3a1f902-78f4-484c-b893-bce1c2e5bdea ==============================
[0m15:31:41.136110 [info ] [MainThread]: Running with dbt=1.10.4
[0m15:31:41.136812 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'version_check': 'True', 'log_path': '/home/<USER>/market-insights-reports/dbt/logs', 'fail_fast': 'False', 'profiles_dir': '.', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'log_format': 'default', 'invocation_command': 'dbt list --select core_array_agg_values --profiles-dir .', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m15:31:41.307777 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'b3a1f902-78f4-484c-b893-bce1c2e5bdea', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c4fb21f0>]}
[0m15:31:41.371741 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'b3a1f902-78f4-484c-b893-bce1c2e5bdea', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c57593d0>]}
[0m15:31:41.372668 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m15:31:41.495202 [debug] [MainThread]: checksum: 073cb5a296270136b24f9c040fdebd0642ef9c263903ef463bfe7e190c538da2, vars: {}, profile: , target: , version: 1.10.4
[0m15:31:41.646687 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m15:31:41.647179 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m15:31:41.655115 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m15:31:41.697383 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'b3a1f902-78f4-484c-b893-bce1c2e5bdea', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c3c98130>]}
[0m15:31:41.797955 [debug] [MainThread]: Wrote artifact WritableManifest to /home/<USER>/market-insights-reports/dbt/target/manifest.json
[0m15:31:41.800958 [debug] [MainThread]: Wrote artifact SemanticManifest to /home/<USER>/market-insights-reports/dbt/target/semantic_manifest.json
[0m15:31:41.880796 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'b3a1f902-78f4-484c-b893-bce1c2e5bdea', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c454aee0>]}
[0m15:31:41.882154 [info ] [MainThread]: smi_report.core.core_array_agg_values
[0m15:31:41.883504 [debug] [MainThread]: Resource report: {"command_name": "list", "command_success": true, "command_wall_clock_time": 0.81179196, "process_in_blocks": "0", "process_kernel_time": 0.109817, "process_mem_max_rss": "109700", "process_out_blocks": "1048", "process_user_time": 1.976703}
[0m15:31:41.884251 [debug] [MainThread]: Command `dbt list` succeeded at 15:31:41.884084 after 0.81 seconds
[0m15:31:41.884914 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c5fd2fa0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c454aee0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x7fd3c3c946d0>]}
[0m15:31:41.885573 [debug] [MainThread]: Flushing usage events
[0m15:31:42.269184 [debug] [MainThread]: An error was encountered while trying to flush usage events


============================== 15:39:46.081923 | 23955220-5d20-48b1-a43b-1cfb8f4a562e ==============================
[0m15:39:46.081923 [info ] [MainThread]: Running with dbt=1.10.4
[0m15:39:46.317970 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m15:39:46.555246 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m15:39:46.760856 [info ] [MainThread]: Found 17 models, 2 data tests, 434 macros
[0m15:39:46.762733 [info ] [MainThread]: 
[0m15:39:46.763221 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m15:39:46.763662 [info ] [MainThread]: 
[0m15:39:46.962504 [info ] [Thread-1  ]: 1 of 1 START sql incremental model public.core_array_agg_values ................ [RUN]
[0m15:39:47.082189 [error] [Thread-1  ]: 1 of 1 ERROR creating sql incremental model public.core_array_agg_values ....... [[31mERROR[0m in 0.12s]
[0m15:39:47.107942 [info ] [MainThread]: 
[0m15:39:47.108459 [info ] [MainThread]: Finished running 1 incremental model in 0 hours 0 minutes and 0.34 seconds (0.34s).
[0m15:39:47.148105 [info ] [MainThread]: 
[0m15:39:47.148655 [info ] [MainThread]: [31mCompleted with 1 error, 0 partial successes, and 0 warnings:[0m
[0m15:39:47.149183 [info ] [MainThread]: 
[0m15:39:47.149723 [error] [MainThread]: [31mFailure in model core_array_agg_values (models/core/core_array_agg_values.sql)[0m
[0m15:39:47.150246 [error] [MainThread]:   Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)
  aggregate functions are not allowed in WHERE
  LINE 23:         where time_updated > (select max(time_updated) from ...
                                                ^
[0m15:39:47.150683 [info ] [MainThread]: 
[0m15:39:47.151184 [info ] [MainThread]:   compiled code at target/compiled/smi_report/models/core/core_array_agg_values.sql
[0m15:39:47.151627 [info ] [MainThread]: 
[0m15:39:47.152096 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=1 SKIP=0 NO-OP=0 TOTAL=1


============================== 15:41:20.196156 | 5b66de22-42c1-4bf9-b604-72164be35cbd ==============================
[0m15:41:20.196156 [info ] [MainThread]: Running with dbt=1.10.4
[0m15:41:20.474283 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m15:41:21.149095 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m15:41:21.260325 [info ] [MainThread]: Found 17 models, 2 data tests, 434 macros
[0m15:41:21.262162 [info ] [MainThread]: 
[0m15:41:21.262651 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m15:41:21.263083 [info ] [MainThread]: 
[0m15:41:21.435872 [info ] [Thread-1  ]: 1 of 1 START sql incremental model public.core_array_agg_values ................ [RUN]
[0m17:53:03.965389 [error] [Thread-1  ]: 1 of 1 ERROR creating sql incremental model public.core_array_agg_values ....... [[31mERROR[0m in 7902.53s]
[0m17:53:04.014306 [info ] [MainThread]: 
[0m17:53:04.014863 [info ] [MainThread]: Finished running 1 incremental model in 2 hours 11 minutes and 42.75 seconds (7902.75s).
[0m17:53:04.073070 [info ] [MainThread]: 
[0m17:53:04.073990 [info ] [MainThread]: [31mCompleted with 1 error, 0 partial successes, and 0 warnings:[0m
[0m17:53:04.074782 [info ] [MainThread]: 
[0m17:53:04.075683 [error] [MainThread]: [31mFailure in model core_array_agg_values (models/core/core_array_agg_values.sql)[0m
[0m17:53:04.076589 [error] [MainThread]:   Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)
  could not receive data from server: Connection timed out
  SSL SYSCALL error: Connection timed out
[0m17:53:04.077359 [info ] [MainThread]: 
[0m17:53:04.078226 [info ] [MainThread]:   compiled code at target/compiled/smi_report/models/core/core_array_agg_values.sql
[0m17:53:04.079049 [info ] [MainThread]: 
[0m17:53:04.079923 [info ] [MainThread]: Done. PASS=0 WARN=0 ERROR=1 SKIP=0 NO-OP=0 TOTAL=1


============================== 19:19:22.909800 | 429944ca-34be-4888-8a8e-76c0e5f3fd79 ==============================
[0m19:19:22.909800 [info ] [MainThread]: Running with dbt=1.10.4
[0m19:19:23.149724 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m19:19:23.361487 [info ] [MainThread]: Unable to do partial parsing because a project config has changed
[0m19:19:24.749644 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m19:19:24.912394 [info ] [MainThread]: Found 18 models, 2 data tests, 436 macros
[0m19:19:24.914821 [info ] [MainThread]: 
[0m19:19:24.915327 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m19:19:24.915791 [info ] [MainThread]: 
[0m19:19:25.136711 [info ] [Thread-1  ]: 1 of 18 START sql view model public.stg_chapters ............................... [RUN]
[0m19:19:25.290923 [info ] [Thread-1  ]: 1 of 18 OK created sql view model public.stg_chapters .......................... [[32mCREATE VIEW[0m in 0.15s]
[0m19:19:25.292580 [info ] [Thread-1  ]: 2 of 18 START sql view model public.stg_chart_kpis ............................. [RUN]
[0m19:19:25.352001 [info ] [Thread-1  ]: 2 of 18 OK created sql view model public.stg_chart_kpis ........................ [[32mCREATE VIEW[0m in 0.06s]
[0m19:19:25.353601 [info ] [Thread-1  ]: 3 of 18 START sql view model public.stg_chart_types ............................ [RUN]
[0m19:19:25.406765 [info ] [Thread-1  ]: 3 of 18 OK created sql view model public.stg_chart_types ....................... [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.408352 [info ] [Thread-1  ]: 4 of 18 START sql view model public.stg_charts ................................. [RUN]
[0m19:19:25.464566 [info ] [Thread-1  ]: 4 of 18 OK created sql view model public.stg_charts ............................ [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.466145 [info ] [Thread-1  ]: 5 of 18 START sql view model public.stg_geos ................................... [RUN]
[0m19:19:25.520178 [info ] [Thread-1  ]: 5 of 18 OK created sql view model public.stg_geos .............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.521758 [info ] [Thread-1  ]: 6 of 18 START sql view model public.stg_kpis ................................... [RUN]
[0m19:19:25.576018 [info ] [Thread-1  ]: 6 of 18 OK created sql view model public.stg_kpis .............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.577589 [info ] [Thread-1  ]: 7 of 18 START sql view model public.stg_kpis_values ............................ [RUN]
[0m19:19:25.636753 [info ] [Thread-1  ]: 7 of 18 OK created sql view model public.stg_kpis_values ....................... [[32mCREATE VIEW[0m in 0.06s]
[0m19:19:25.638350 [info ] [Thread-1  ]: 8 of 18 START sql view model public.stg_pages .................................. [RUN]
[0m19:19:25.692685 [info ] [Thread-1  ]: 8 of 18 OK created sql view model public.stg_pages ............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.694258 [info ] [Thread-1  ]: 9 of 18 START sql view model public.stg_units .................................. [RUN]
[0m19:19:25.747995 [info ] [Thread-1  ]: 9 of 18 OK created sql view model public.stg_units ............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.749656 [info ] [Thread-1  ]: 10 of 18 START sql view model public.stg_vertical_graphs ....................... [RUN]
[0m19:19:25.804041 [info ] [Thread-1  ]: 10 of 18 OK created sql view model public.stg_vertical_graphs .................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.805631 [info ] [Thread-1  ]: 11 of 18 START sql view model public.stg_verticals ............................. [RUN]
[0m19:19:25.857439 [info ] [Thread-1  ]: 11 of 18 OK created sql view model public.stg_verticals ........................ [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25.859051 [info ] [Thread-1  ]: 12 of 18 START sql incremental model public.core_kpis_to_values ................ [RUN]
[0m19:29:18.212862 [info ] [Thread-1  ]: 12 of 18 OK created sql incremental model public.core_kpis_to_values ........... [[32mINSERT 0 59445068[0m in 592.35s]
[0m19:29:18.214673 [info ] [Thread-1  ]: 13 of 18 START sql view model public.core_pages_to_charts ...................... [RUN]
[0m19:29:18.343314 [info ] [Thread-1  ]: 13 of 18 OK created sql view model public.core_pages_to_charts ................. [[32mCREATE VIEW[0m in 0.13s]
[0m19:29:18.344976 [info ] [Thread-1  ]: 14 of 18 START sql view model public.core_vertical_graphs_verticals ............ [RUN]
[0m19:29:18.401050 [info ] [Thread-1  ]: 14 of 18 OK created sql view model public.core_vertical_graphs_verticals ....... [[32mCREATE VIEW[0m in 0.05s]
[0m19:29:18.402648 [info ] [Thread-1  ]: 15 of 18 START sql incremental model public.core_array_agg_values .............. [RUN]
[0m19:39:03.222165 [info ] [Thread-1  ]: 15 of 18 OK created sql incremental model public.core_array_agg_values ......... [[32mINSERT 0 4854651[0m in 584.82s]
[0m19:39:03.223986 [info ] [Thread-1  ]: 16 of 18 START sql table model public.mart_verticals_to_charts ................. [RUN]
[0m19:39:53.785609 [info ] [Thread-1  ]: 16 of 18 OK created sql table model public.mart_verticals_to_charts ............ [[32mSELECT 1528978[0m in 50.56s]
[0m19:39:53.787472 [info ] [Thread-1  ]: 17 of 18 START sql incremental model public.core_pages_to_agg_values ........... [RUN]
[0m19:51:06.947740 [info ] [Thread-1  ]: 17 of 18 OK created sql incremental model public.core_pages_to_agg_values ...... [[32mSELECT 3026720[0m in 673.16s]
[0m19:51:06.949994 [info ] [Thread-1  ]: 18 of 18 START sql table model public.mart_mongodb ............................. [RUN]
[0m19:52:50.678569 [info ] [Thread-1  ]: 18 of 18 OK created sql table model public.mart_mongodb ........................ [[32mSELECT 478350[0m in 103.73s]
[0m19:52:50.751985 [info ] [MainThread]: 
[0m19:52:50.752512 [info ] [MainThread]: Finished running 3 incremental models, 2 table models, 13 view models in 0 hours 33 minutes and 25.84 seconds (2005.84s).
[0m19:52:50.802479 [info ] [MainThread]: 
[0m19:52:50.803024 [info ] [MainThread]: [32mCompleted successfully[0m
[0m19:52:50.803566 [info ] [MainThread]: 
[0m19:52:50.804076 [info ] [MainThread]: Done. PASS=18 WARN=0 ERROR=0 SKIP=0 NO-OP=0 TOTAL=18


============================== 19:57:33.275738 | f10ea801-a0ef-420d-bd7a-507cc946e552 ==============================
[0m19:57:33.275738 [info ] [MainThread]: Running with dbt=1.10.4
[0m19:57:33.562831 [info ] [MainThread]: Registered adapter: postgres=1.9.0
[0m19:57:34.119906 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m19:57:34.234679 [info ] [MainThread]: Found 18 models, 2 data tests, 436 macros
[0m19:57:34.237352 [info ] [MainThread]: 
[0m19:57:34.237863 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m19:57:34.238320 [info ] [MainThread]: 
[0m19:57:34.574516 [info ] [Thread-1  ]: 1 of 1 START sql table model public.mart_mongodb ............................... [RUN]
[0m19:58:43.680342 [info ] [Thread-1  ]: 1 of 1 OK created sql table model public.mart_mongodb .......................... [[32mSELECT 478350[0m in 69.10s]
[0m19:58:43.715117 [info ] [MainThread]: 
[0m19:58:43.715646 [info ] [MainThread]: Finished running 1 table model in 0 hours 1 minutes and 9.48 seconds (69.48s).
[0m19:58:43.755856 [info ] [MainThread]: 
[0m19:58:43.756463 [info ] [MainThread]: [32mCompleted successfully[0m
[0m19:58:43.757039 [info ] [MainThread]: 
[0m19:58:43.757639 [info ] [MainThread]: Done. PASS=1 WARN=0 ERROR=0 SKIP=0 NO-OP=0 TOTAL=1
