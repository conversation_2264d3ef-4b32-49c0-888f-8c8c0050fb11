{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/manifest/v12.json", "dbt_version": "1.10.4", "generated_at": "2025-07-19T15:41:20.687604Z", "invocation_id": "5b66de22-42c1-4bf9-b604-72164be35cbd", "invocation_started_at": "2025-07-19T15:28:33.467119+00:00", "env": {}, "project_name": "smi_report", "project_id": "d1291f82216959ae8cbe80ce07b56090", "user_id": "afc20822-c091-4084-931e-9a8233d2ce19", "send_anonymous_usage_stats": true, "adapter_type": "postgres", "quoting": {"database": true, "schema": true, "identifier": true, "column": null}}, "nodes": {"model.smi_report.mart_verticals_to_charts": {"database": "postgres", "schema": "public", "name": "mart_verticals_to_charts", "resource_type": "model", "package_name": "smi_report", "path": "marts/mart_verticals_to_charts.sql", "original_file_path": "models/marts/mart_verticals_to_charts.sql", "unique_id": "model.smi_report.mart_verticals_to_charts", "fqn": ["smi_report", "marts", "mart_verticals_to_charts"], "alias": "mart_verticals_to_charts", "checksum": {"name": "sha256", "checksum": "c2b5dc6ace769371945172449879a2d00e7cbb43f87ffad4d82f3b1ee3b0c195"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "table", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [{"sql": "SET statement_timeout = '4h';", "transaction": true, "index": null}, {"sql": "SET lock_timeout = '30min';", "transaction": true, "index": null}, {"sql": "SET idle_in_transaction_session_timeout = '4h';", "transaction": true, "index": null}, {"sql": "SET work_mem = '256MB';", "transaction": true, "index": null}, {"sql": "SET maintenance_work_mem = '1GB';", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_idle = 7200;", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_interval = 30;", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_count = 9;", "transaction": true, "index": null}], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "table", "pre-hook": ["SET statement_timeout = '4h';", "SET lock_timeout = '30min';", "SET idle_in_transaction_session_timeout = '4h';", "SET work_mem = '256MB';", "SET maintenance_work_mem = '1GB';", "SET tcp_keepalives_idle = 7200;", "SET tcp_keepalives_interval = 30;", "SET tcp_keepalives_count = 9;"]}, "created_at": 1752938914.8347483, "relation_name": "\"postgres\".\"public\".\"mart_verticals_to_charts\"", "raw_code": "-- models/marts/mart_verticals_to_charts.sql\n\n{{ config(\n    materialized='table',\n    pre_hook=[\n        \"SET statement_timeout = '4h';\",\n        \"SET lock_timeout = '30min';\",\n        \"SET idle_in_transaction_session_timeout = '4h';\",\n        \"SET work_mem = '256MB';\",\n        \"SET maintenance_work_mem = '1GB';\",\n        \"SET tcp_keepalives_idle = 7200;\",\n        \"SET tcp_keepalives_interval = 30;\",\n        \"SET tcp_keepalives_count = 9;\"\n    ]\n) }}\n\nwith vertical_graphs_verticals as (\n    select * from {{ ref('core_vertical_graphs_verticals') }}\n),\n\n-- this will be for the parents\nvertical_graphs_verticals2 as (\n    select * from {{ ref('core_vertical_graphs_verticals') }}\n),\n\npages_to_charts as (\n    select * from {{ ref('core_pages_to_charts') }}\n),\n\nverticals_to_charts as (\n    select\n        vgv.market_name as market_insights_name,\n        vgv.vertical_parent_id,\n        vgv2.name_vertical as parent_name,\n        vgv.vertical_id,\n        vgv.name_vertical as market_name,\n        ptc.geo_id,\n        ptc.geo_name,\n        ptc.order_chapter,\n        ptc.chapter_name,\n        ptc.order_chart,\n        ptc.chart_title as chart_name,\n        split_part(ptc.chart_key, '_', 1) as chart_key,\n        ptc.chart_type,\n        ptc.min_year,\n        ptc.max_year,\n        ptc.unit,\n        ptc.unit_type,\n        ptc.time_updated\n    from vertical_graphs_verticals as vgv\n    inner join vertical_graphs_verticals2 as vgv2 on vgv2.vertical_id = vgv.vertical_parent_id\n    inner join pages_to_charts as ptc on ptc.vertical_graph_id = vgv.vertical_graph_id\n    where LOWER(vgv.market_name) not in (LOWER('digital market outlook'), LOWER('market outlook highlights'), LOWER('trash outlook'))\n    and ptc.chapter_name not in ('Analyst Opinion', 'Methodology', 'Key Market Indicators')\n    order by vgv.market_name, parent_name, vgv.name_vertical, ptc.geo_id, ptc.order_chapter, ptc.order_chart\n)\n\n\nselect * from verticals_to_charts", "doc_blocks": [], "language": "sql", "refs": [{"name": "core_vertical_graphs_verticals", "package": null, "version": null}, {"name": "core_vertical_graphs_verticals", "package": null, "version": null}, {"name": "core_pages_to_charts", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": ["model.smi_report.core_vertical_graphs_verticals", "model.smi_report.core_pages_to_charts"]}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_chapters": {"database": "postgres", "schema": "public", "name": "stg_chapters", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_chapters.sql", "original_file_path": "models/staging/stg_chapters.sql", "unique_id": "model.smi_report.stg_chapters", "fqn": ["smi_report", "staging", "stg_chapters"], "alias": "stg_chapters", "checksum": {"name": "sha256", "checksum": "d0fd126fb4788e1075dbe01d79f6dcc5178bd1f8c1193c886784523857948cb3"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8730567, "relation_name": "\"postgres\".\"public\".\"stg_chapters\"", "raw_code": "-- models/staging/stg_chapters.sql\n\nwith source as (\n\n    select * from public.chapters\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"idPage\" as page_id,\n        name,\n        \"orderChapter\" as order_chapter\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_charts": {"database": "postgres", "schema": "public", "name": "stg_charts", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_charts.sql", "original_file_path": "models/staging/stg_charts.sql", "unique_id": "model.smi_report.stg_charts", "fqn": ["smi_report", "staging", "stg_charts"], "alias": "stg_charts", "checksum": {"name": "sha256", "checksum": "6bfa8312d45af95c7edb002ba3e0aa3f555eac473a125287af31a337a1c0d7ba"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8758562, "relation_name": "\"postgres\".\"public\".\"stg_charts\"", "raw_code": "-- models/staging/stg_charts.sql\n\nwith source as (\n\n    select * from public.charts\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"idParent\" as parent_id,\n        \"idChapter\" as chapter_id,\n        \"chartType\" as chart_type,\n        \"chartKey\" as chart_key,\n        \"orderChart\" as order_chart,\n        \"minYear\" as min_year,\n        \"maxYear\" as max_year,\n        \"idUnit\" as unit_id,\n        scale,\n        \"numberDecimal\" as number_decimal,\n        \"mainTitle\" as main_title,\n        description,\n        info,\n        \"timeUpdated\" as time_updated\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_geos": {"database": "postgres", "schema": "public", "name": "stg_geos", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_geos.sql", "original_file_path": "models/staging/stg_geos.sql", "unique_id": "model.smi_report.stg_geos", "fqn": ["smi_report", "staging", "stg_geos"], "alias": "stg_geos", "checksum": {"name": "sha256", "checksum": "9afc979b95df48ab2892b29d6f3494a1d63c8f5e0ac4a5f38cdd23f0f417337d"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8785806, "relation_name": "\"postgres\".\"public\".\"stg_geos\"", "raw_code": "-- models/staging/stg_geos.sql\n\nwith source as (\n\n    select * from public.geos\n\n),\n\ncleaned as (\n\n    select\n        id,\n        name,\n        \"isoCode3L\" as iso\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_chart_types": {"database": "postgres", "schema": "public", "name": "stg_chart_types", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_chart_types.sql", "original_file_path": "models/staging/stg_chart_types.sql", "unique_id": "model.smi_report.stg_chart_types", "fqn": ["smi_report", "staging", "stg_chart_types"], "alias": "stg_chart_types", "checksum": {"name": "sha256", "checksum": "60b9b41dea05981d1cab21c2ff914544019192210f191c956ec142637390c3e6"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8813484, "relation_name": "\"postgres\".\"public\".\"stg_chart_types\"", "raw_code": "-- models/staging/stg_chart_types.sql\n\nwith source as (\n\n    select * from public.\"chartsTypes\"\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"chartType\" as chart_type\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_chart_kpis": {"database": "postgres", "schema": "public", "name": "stg_chart_kpis", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_chart_kpis.sql", "original_file_path": "models/staging/stg_chart_kpis.sql", "unique_id": "model.smi_report.stg_chart_kpis", "fqn": ["smi_report", "staging", "stg_chart_kpis"], "alias": "stg_chart_kpis", "checksum": {"name": "sha256", "checksum": "d6bf8ae10c648e8f1faad1e155668e8aad0df1bb59bf97b49fb787f1b08675a2"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.884367, "relation_name": "\"postgres\".\"public\".\"stg_chart_kpis\"", "raw_code": "-- models/staging/stg_chart_kpis.sql\n\nwith source as (\n\n    select * from public.\"chartsKpis\"\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"idChart\" as chart_id,\n        \"idKpi\" as kpi_id,\n        \"aggregationType\" as aggregation_type,\n        \"orderChart\" as order_chart\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_kpis": {"database": "postgres", "schema": "public", "name": "stg_kpis", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_kpis.sql", "original_file_path": "models/staging/stg_kpis.sql", "unique_id": "model.smi_report.stg_kpis", "fqn": ["smi_report", "staging", "stg_kpis"], "alias": "stg_kpis", "checksum": {"name": "sha256", "checksum": "f9292b39f102fd9b0706f10502c29bf9a9e22da23f5f246c90e5a3060a4b774c"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8870876, "relation_name": "\"postgres\".\"public\".\"stg_kpis\"", "raw_code": "-- models/staging/stg_kpis.sql\n\nwith source as (\n\n    select * from public.kpis\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"kpiKey\" as kpi_key,\n        \"idBrand\" as brand_id,\n        \"idVerticalGraph\" as vertical_graph_id,\n        name,\n        \"kpiType\" as kpi_type,\n        \"isSummable\" as is_summable,\n        \"isCurrency\" as is_currency\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_units": {"database": "postgres", "schema": "public", "name": "stg_units", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_units.sql", "original_file_path": "models/staging/stg_units.sql", "unique_id": "model.smi_report.stg_units", "fqn": ["smi_report", "staging", "stg_units"], "alias": "stg_units", "checksum": {"name": "sha256", "checksum": "dd6f88637187370f2a5c31264ca86a3f612f9eb68b3ef9e32ec3163aafa1e067"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8898354, "relation_name": "\"postgres\".\"public\".\"stg_units\"", "raw_code": "-- models/staging/stg_units.sql\n\nwith source as (\n\n    select * from public.units\n\n),\n\ncleaned as (\n\n    select\n        id,\n        name\n        magnitude,\n        \"typeUnit\" as unit_type\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_pages": {"database": "postgres", "schema": "public", "name": "stg_pages", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_pages.sql", "original_file_path": "models/staging/stg_pages.sql", "unique_id": "model.smi_report.stg_pages", "fqn": ["smi_report", "staging", "stg_pages"], "alias": "stg_pages", "checksum": {"name": "sha256", "checksum": "ba9a99ec515845f8d836a8a56fc4388611e6dc3d0ddc9ea56373efa14f966b6b"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8926165, "relation_name": "\"postgres\".\"public\".\"stg_pages\"", "raw_code": "-- models/staging/stg_pages.sql\n\nwith source as (\n\n    select * from public.pages\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"idVerticalGraph\" as vertical_graph_id,\n        \"idPlatform\" as platform_id,\n        \"idGeo\" as geo_id,\n        name,\n        \"outlookName\" as outlook_name,\n        definition,\n        \"keyTakeAway\" as key_take_away,\n        \"inScope\" as in_scope,\n        \"outOfScope\" as out_scope,\n        \"searchTags\" as search_tags,\n        \"placeholderScript\" as placeholder_script,\n        \"metaDescription\" as meta_description\n    from source\n    where \"idPlatform\" = 2\n    and \"isActive\" = 1\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_verticals": {"database": "postgres", "schema": "public", "name": "stg_verticals", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_verticals.sql", "original_file_path": "models/staging/stg_verticals.sql", "unique_id": "model.smi_report.stg_verticals", "fqn": ["smi_report", "staging", "stg_verticals"], "alias": "stg_verticals", "checksum": {"name": "sha256", "checksum": "dce87334d912bb09a1c7e24b0db6541e8b50367d96472aba8e850aaa60667bc5"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.895414, "relation_name": "\"postgres\".\"public\".\"stg_verticals\"", "raw_code": "-- models/staging/stg_verticals.sql\n\nwith source as (\n\n    select * from public.verticals\n\n),\n\ncleaned as (\n\n    select\n        \"idVertical\" as id,\n        \"nameVertical\" as name_vertical\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_kpis_values": {"database": "postgres", "schema": "public", "name": "stg_kpis_values", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_kpis_values.sql", "original_file_path": "models/staging/stg_kpis_values.sql", "unique_id": "model.smi_report.stg_kpis_values", "fqn": ["smi_report", "staging", "stg_kpis_values"], "alias": "stg_kpis_values", "checksum": {"name": "sha256", "checksum": "e37d24f2c32fce5c5b0e21171bbb927eac79d61ac7bae4a3f13c957179c5ebb4"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.8981323, "relation_name": "\"postgres\".\"public\".\"stg_kpis_values\"", "raw_code": "-- models/staging/stg_kpis_values.sql\n\nwith source as (\n\n    select * from public.\"kpisValues\"\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"idGeo\" as geo_id,\n        \"idUnit\" as unit_id,\n        \"idKpi\" as kpi_id,\n        extract(year from \"valueTime\") as value_time,\n        \"timeUpdated\" as time_updated,\n        \"idResponsible\" as responsible_id,\n        value\n    from source\n\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.stg_vertical_graphs": {"database": "postgres", "schema": "public", "name": "stg_vertical_graphs", "resource_type": "model", "package_name": "smi_report", "path": "staging/stg_vertical_graphs.sql", "original_file_path": "models/staging/stg_vertical_graphs.sql", "unique_id": "model.smi_report.stg_vertical_graphs", "fqn": ["smi_report", "staging", "stg_vertical_graphs"], "alias": "stg_vertical_graphs", "checksum": {"name": "sha256", "checksum": "c1bc09a9d3e28db2516ad883b0504344bd38573b1f3074f8dfaeab184a181487"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"materialized": "view"}, "created_at": 1752938914.9008992, "relation_name": "\"postgres\".\"public\".\"stg_vertical_graphs\"", "raw_code": "-- models/staging/stg_vertical_graphs.sql\n\nwith source as (\n\n    select * from public.\"verticalGraphs\"\n\n),\n\ncleaned as (\n\n    select\n        id,\n        \"idVertical\" as vertical_id,\n        \"idVerticalParent\" as vertical_parent_id,\n        \"marketName\" as market_name,\n        \"typeVertical\" as type_vertical\n    from source\n    where \"typeVertical\" in ('outlook', 'market')\n)\n\nselect * from cleaned", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.core_kpis_to_values": {"database": "postgres", "schema": "public", "name": "core_kpis_to_values", "resource_type": "model", "package_name": "smi_report", "path": "core/core_kpis_to_values.sql", "original_file_path": "models/core/core_kpis_to_values.sql", "unique_id": "model.smi_report.core_kpis_to_values", "fqn": ["smi_report", "core", "core_kpis_to_values"], "alias": "core_kpis_to_values", "checksum": {"name": "sha256", "checksum": "918e91629172596049a7a4745767958f2dcfd11acb37fbb6f9d7ee7c21aa7096"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "incremental", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": "kpi_id", "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "This model joins KPI and KPI value data to provide a comprehensive view of key performance indicators (KPIs) across different geographical areas and timeframes.\n", "columns": {"kpi_id": {"name": "kpi_id", "description": "The unique identifier for each KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "name": {"name": "name", "description": "The name of the KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "kpi_key": {"name": "kpi_key", "description": "The key representing the KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "geo_id": {"name": "geo_id", "description": "The unique identifier for the geographical region.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "unit_id": {"name": "unit_id", "description": "The unique identifier for the unit of measurement for the KPI value.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "value_time": {"name": "value_time", "description": "The time period the KPI value refers to.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "time_updated": {"name": "time_updated", "description": "The time when the KPI value was last updated.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "responsible_id": {"name": "responsible_id", "description": "The ID of the person responsible for the KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "value": {"name": "value", "description": "The value of the KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": "smi_report://models/core/schema.yml", "build_path": null, "unrendered_config": {"materialized": "incremental", "unique_key": "kpi_id"}, "created_at": 1752938915.0156114, "relation_name": "\"postgres\".\"public\".\"core_kpis_to_values\"", "raw_code": "-- models/core/core_kpis_to_values.sql\n\n-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table\n{{ config(materialized='incremental', unique_key='kpi_id') }}\n\n\nwith kpis as (\n    select * from {{ ref('stg_kpis') }}\n),\n\nkpis_values as (\n    select * from {{ ref('stg_kpis_values') }}\n),\n\nkpis_to_values as (\n    select\n        kpis.id as kpi_id,\n        kpis.name,\n        kpis.kpi_key,\n        kpis_values.geo_id,\n        kpis_values.unit_id,\n        kpis_values.value_time,\n        kpis_values.time_updated,\n        kpis_values.responsible_id,\n        kpis_values.value\n    from kpis\n    left join kpis_values on kpis.id = kpis_values.kpi_id\n    where kpis_values.value is not null\n)\n\nselect * from kpis_to_values", "doc_blocks": [], "language": "sql", "refs": [{"name": "stg_kpis", "package": null, "version": null}, {"name": "stg_kpis_values", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": ["model.smi_report.stg_kpis", "model.smi_report.stg_kpis_values"]}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.core_pages_to_agg_values": {"database": "postgres", "schema": "public", "name": "core_pages_to_agg_values", "resource_type": "model", "package_name": "smi_report", "path": "core/core_pages_to_agg_values.sql", "original_file_path": "models/core/core_pages_to_agg_values.sql", "unique_id": "model.smi_report.core_pages_to_agg_values", "fqn": ["smi_report", "core", "core_pages_to_agg_values"], "alias": "core_pages_to_agg_values", "checksum": {"name": "sha256", "checksum": "4c87dd045f72e13df0ad8f9f65a596adebaeffdfff06b5ec354e73d63e9c829d"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "incremental", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [{"sql": "SET statement_timeout = '4h';", "transaction": true, "index": null}, {"sql": "SET lock_timeout = '30min';", "transaction": true, "index": null}, {"sql": "SET idle_in_transaction_session_timeout = '4h';", "transaction": true, "index": null}, {"sql": "SET work_mem = '256MB';", "transaction": true, "index": null}, {"sql": "SET maintenance_work_mem = '1GB';", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_idle = 7200;", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_interval = 30;", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_count = 9;", "transaction": true, "index": null}], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": ["kpi_id", "geo_id"], "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "This model joins page, chart, and aggregated KPI values for preparation into MongoDB.\n", "columns": {"page_id": {"name": "page_id", "description": "The unique identifier for each page.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "kpi_key": {"name": "kpi_key", "description": "The key representing the KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "geo_id": {"name": "geo_id", "description": "The unique identifier for the geographical region.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "unit_id": {"name": "unit_id", "description": "The unique identifier for the unit of measurement.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "value_time_list": {"name": "value_time_list", "description": "Array of value_time values for the KPI and geo.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "value_list": {"name": "value_list", "description": "Array of KPI values for the KPI and geo.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": "smi_report://models/core/schema.yml", "build_path": null, "unrendered_config": {"materialized": "incremental", "unique_key": ["kpi_id", "geo_id"], "pre-hook": ["SET statement_timeout = '4h';", "SET lock_timeout = '30min';", "SET idle_in_transaction_session_timeout = '4h';", "SET work_mem = '256MB';", "SET maintenance_work_mem = '1GB';", "SET tcp_keepalives_idle = 7200;", "SET tcp_keepalives_interval = 30;", "SET tcp_keepalives_count = 9;"]}, "created_at": 1752938915.0284626, "relation_name": "\"postgres\".\"public\".\"core_pages_to_agg_values\"", "raw_code": "-- models/core/core_pages_to_agg_values.sql\n\n-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table\n{{ config(\n    materialized='incremental',\n    unique_key=['kpi_id', 'geo_id'],\n    pre_hook=[\n        \"SET statement_timeout = '4h';\",\n        \"SET lock_timeout = '30min';\",\n        \"SET idle_in_transaction_session_timeout = '4h';\",\n        \"SET work_mem = '256MB';\",\n        \"SET maintenance_work_mem = '1GB';\",\n        \"SET tcp_keepalives_idle = 7200;\",\n        \"SET tcp_keepalives_interval = 30;\",\n        \"SET tcp_keepalives_count = 9;\"\n    ]\n) }}\n\n\nwith pages_to_charts as (\n    select * from {{ ref('core_pages_to_charts') }}\n    -- where geo_id in (100)\n),\n\ncharts_to_kpis as (\n    select * from {{ ref('stg_chart_kpis') }}\n),\n\narray_agg_values as (\n    select * from {{ ref('core_array_agg_values') }}\n),\n\npages_to_values as (\n    select\n        ptc.page_id,\n        ptc.vertical_graph_id,\n        ptc.page_name,\n        ptc.geo_id as ptc_geo_id,\n        ptc.geo_name,\n        ptc.geo_iso,\n        ptc.outlook_name,\n        ptc.definition,\n        ptc.key_take_away,\n        ptc.in_scope,\n        ptc.out_scope,\n        ptc.chapter_id,\n        ptc.chapter_name,\n        ptc.chart_id,\n        ptc.chart_key,\n        ptc.chart_type,\n        ptc.order_chart,\n        ptc.min_year,\n        ptc.max_year,\n        ptc.unit,\n        ptc.unit_type,\n        ptc.scale,\n        ptc.number_decimal,\n        ptc.chart_title,\n        ptc.info,\n        ctk.id as ctk_id,  -- This would be the unique identifier for the chart-kpi relationship\n        ctk.chart_id as ctk_chart_id,\n        ctk.kpi_id as ctk_kpi_id,\n        ctk.aggregation_type as ctk_aggregation_type,\n        ctk.order_chart as ctk_order_chart,\n        aav.kpi_id,\n        aav.name,\n        aav.kpi_key,\n        aav.geo_id,\n        aav.unit_id,\n        aav.value_time_list,\n        aav.value_list\n    from pages_to_charts as ptc\n    inner join charts_to_kpis as ctk on ptc.chart_id = ctk.chart_id\n    inner join array_agg_values as aav on ctk.kpi_id = aav.kpi_id and ptc.geo_id = aav.geo_id\n)\n\nselect * from pages_to_values", "doc_blocks": [], "language": "sql", "refs": [{"name": "core_pages_to_charts", "package": null, "version": null}, {"name": "stg_chart_kpis", "package": null, "version": null}, {"name": "core_array_agg_values", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": ["model.smi_report.core_pages_to_charts", "model.smi_report.stg_chart_kpis", "model.smi_report.core_array_agg_values"]}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.core_pages_to_charts": {"database": "postgres", "schema": "public", "name": "core_pages_to_charts", "resource_type": "model", "package_name": "smi_report", "path": "core/core_pages_to_charts.sql", "original_file_path": "models/core/core_pages_to_charts.sql", "unique_id": "model.smi_report.core_pages_to_charts", "fqn": ["smi_report", "core", "core_pages_to_charts"], "alias": "core_pages_to_charts", "checksum": {"name": "sha256", "checksum": "e5134e9416eca4c2481ee3d160650b4b41ff4f0f0f606238e9c34cda6d7c5c71"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "This model creates a comprehensive mapping between pages, charts, chapters, units, and geographical regions to link all aspects of the reporting data.\n", "columns": {"page_id": {"name": "page_id", "description": "The unique identifier for each page.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "vertical_graph_id": {"name": "vertical_graph_id", "description": "The ID of the vertical graph associated with the page.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "page_name": {"name": "page_name", "description": "The name of the page.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "geo_id": {"name": "geo_id", "description": "The ID of the geographical region the chart pertains to.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "geo_name": {"name": "geo_name", "description": "The name of the geographical region.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "outlook_name": {"name": "outlook_name", "description": "The name of the outlook associated with the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "definition": {"name": "definition", "description": "The definition provided for the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "key_take_away": {"name": "key_take_away", "description": "The key takeaway message from the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "in_scope": {"name": "in_scope", "description": "Whether the chart is within the defined scope.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "out_scope": {"name": "out_scope", "description": "Whether the chart is out of scope.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "chapter_id": {"name": "chapter_id", "description": "The ID of the chapter associated with the page.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "chapter_name": {"name": "chapter_name", "description": "The name of the chapter.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "order_chapter": {"name": "order_chapter", "description": "The order of the chapter in the report.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "chart_id": {"name": "chart_id", "description": "The unique identifier for the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "chart_key": {"name": "chart_key", "description": "The key representing the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "chart_type": {"name": "chart_type", "description": "The type of the chart (e.g., bar, pie).", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "order_chart": {"name": "order_chart", "description": "The order of the chart in the chapter.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "unit": {"name": "unit", "description": "The unit of measurement used in the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "unit_type": {"name": "unit_type", "description": "The type of unit measurement (e.g., percentage, currency).", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "scale": {"name": "scale", "description": "The scale applied to the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "number_decimal": {"name": "number_decimal", "description": "The number of decimal places for the chart’s values.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "chart_title": {"name": "chart_title", "description": "The title of the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "info": {"name": "info", "description": "Additional information about the chart.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "time_updated": {"name": "time_updated", "description": "The last time the chart was updated.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": "smi_report://models/core/schema.yml", "build_path": null, "unrendered_config": {}, "created_at": 1752938915.0241637, "relation_name": "\"postgres\".\"public\".\"core_pages_to_charts\"", "raw_code": "-- models/core/core_pages_to_charts.sql\n\nwith pages as (\n    select * from {{ ref('stg_pages') }}\n),\n\nchapters as (\n    select * from {{ ref('stg_chapters') }}\n),\n\ncharts as (\n    select * from {{ ref('stg_charts') }}\n),\n\nchart_types as (\n    select * from {{ ref('stg_chart_types') }}\n),\n\nunits as (\n    select * from {{ ref('stg_units') }}\n),\n\ngeos as (\n    select * from {{ ref('stg_geos') }}\n),\n\npages_to_charts as (\n    select\n        pages.id as page_id,\n        vertical_graph_id,\n        pages.name as page_name,\n        pages.geo_id,\n        geos.name as geo_name,\n        geos.iso as geo_iso,\n        outlook_name,\n        definition,\n        key_take_away,\n        in_scope,\n        out_scope,\n        chapters.id as chapter_id,\n        chapters.name as chapter_name,\n        order_chapter,\n        charts.id as chart_id,\n        chart_key,\n        chart_types.chart_type,\n        order_chart,\n        charts.min_year,\n        charts.max_year,\n        units.magnitude as unit,\n        units.unit_type,\n        scale,\n        number_decimal,\n        charts.main_title as chart_title,\n        charts.description,\n        charts.info,\n        charts.time_updated\n    from pages\n    left join geos on pages.geo_id = geos.id\n    left join chapters on pages.id = chapters.page_id\n    left join charts on chapters.id = charts.chapter_id\n    left join chart_types on charts.chart_type = chart_types.id\n    left join units on charts.unit_id = units.id\n)\n\nselect * from pages_to_charts", "doc_blocks": [], "language": "sql", "refs": [{"name": "stg_pages", "package": null, "version": null}, {"name": "stg_chapters", "package": null, "version": null}, {"name": "stg_charts", "package": null, "version": null}, {"name": "stg_chart_types", "package": null, "version": null}, {"name": "stg_units", "package": null, "version": null}, {"name": "stg_geos", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": ["model.smi_report.stg_pages", "model.smi_report.stg_chapters", "model.smi_report.stg_charts", "model.smi_report.stg_chart_types", "model.smi_report.stg_units", "model.smi_report.stg_geos"]}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "model.smi_report.core_vertical_graphs_verticals": {"database": "postgres", "schema": "public", "name": "core_vertical_graphs_verticals", "resource_type": "model", "package_name": "smi_report", "path": "core/core_vertical_graphs_verticals.sql", "original_file_path": "models/core/core_vertical_graphs_verticals.sql", "unique_id": "model.smi_report.core_vertical_graphs_verticals", "fqn": ["smi_report", "core", "core_vertical_graphs_verticals"], "alias": "core_vertical_graphs_verticals", "checksum": {"name": "sha256", "checksum": "80d944ea5b585cbbdbdd030d7b37eb7d132388d803af419b95aedbb739c08b27"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "This model maps vertical graph IDs to vertical IDs and their respective parent vertical IDs, providing a comprehensive view of the relationships between verticals and graphs.\n", "columns": {"vertical_graph_id": {"name": "vertical_graph_id", "description": "The ID of the vertical graph.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "vertical_id": {"name": "vertical_id", "description": "The ID of the vertical within the graph.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "vertical_parent_id": {"name": "vertical_parent_id", "description": "The ID of the parent vertical.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "name_vertical": {"name": "name_vertical", "description": "The name of the vertical.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "market_name": {"name": "market_name", "description": "The market name associated with the vertical graph.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": "smi_report://models/core/schema.yml", "build_path": null, "unrendered_config": {}, "created_at": 1752938915.0261354, "relation_name": "\"postgres\".\"public\".\"core_vertical_graphs_verticals\"", "raw_code": "-- models/core/core_vertical_graphs_verticals.sql\n\nwith verticals as (\n    select * from {{ ref('stg_verticals') }}\n),\n\nvertical_graphs as (\n    select * from {{ ref('stg_vertical_graphs') }}\n),\n\nvertical_graphs_verticals as (\n    select\n        vertical_graphs.id as vertical_graph_id,\n        vertical_graphs.vertical_id,\n        vertical_graphs.vertical_parent_id,\n        verticals.name_vertical,\n        vertical_graphs.market_name\n    from vertical_graphs\n    left join verticals on verticals.id = vertical_graphs.vertical_id\n)\n\nselect * from vertical_graphs_verticals", "doc_blocks": [], "language": "sql", "refs": [{"name": "stg_verticals", "package": null, "version": null}, {"name": "stg_vertical_graphs", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": ["model.smi_report.stg_verticals", "model.smi_report.stg_vertical_graphs"]}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}, "test.smi_report.not_null_core_kpis_to_values_kpi_id.8ab724756d": {"database": "postgres", "schema": "public_dbt_test__audit", "name": "not_null_core_kpis_to_values_kpi_id", "resource_type": "test", "package_name": "smi_report", "path": "not_null_core_kpis_to_values_kpi_id.sql", "original_file_path": "models/core/schema.yml", "unique_id": "test.smi_report.not_null_core_kpis_to_values_kpi_id.8ab724756d", "fqn": ["smi_report", "core", "not_null_core_kpis_to_values_kpi_id"], "alias": "not_null_core_kpis_to_values_kpi_id", "checksum": {"name": "none", "checksum": ""}, "config": {"enabled": true, "alias": null, "schema": "dbt_test__audit", "database": null, "tags": [], "meta": {}, "group": null, "materialized": "test", "severity": "ERROR", "store_failures": true, "store_failures_as": "table", "where": null, "limit": null, "fail_calc": "count(*)", "warn_if": "!= 0", "error_if": "!= 0"}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"store_failures": true}, "created_at": 1752938915.1109674, "relation_name": "\"postgres\".\"public_dbt_test__audit\".\"not_null_core_kpis_to_values_kpi_id\"", "raw_code": "{{ test_not_null(**_dbt_generic_test_kwargs) }}", "doc_blocks": [], "language": "sql", "refs": [{"name": "core_kpis_to_values", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": ["macro.dbt.test_not_null"], "nodes": ["model.smi_report.core_kpis_to_values"]}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "column_name": "kpi_id", "file_key_name": "models.core_kpis_to_values", "attached_node": "model.smi_report.core_kpis_to_values", "test_metadata": {"name": "not_null", "kwargs": {"column_name": "kpi_id", "model": "{{ get_where_subquery(ref('core_kpis_to_values')) }}"}, "namespace": null}}, "test.smi_report.not_null_core_kpis_to_values_value.ee02c5f8c6": {"database": "postgres", "schema": "public_dbt_test__audit", "name": "not_null_core_kpis_to_values_value", "resource_type": "test", "package_name": "smi_report", "path": "not_null_core_kpis_to_values_value.sql", "original_file_path": "models/core/schema.yml", "unique_id": "test.smi_report.not_null_core_kpis_to_values_value.ee02c5f8c6", "fqn": ["smi_report", "core", "not_null_core_kpis_to_values_value"], "alias": "not_null_core_kpis_to_values_value", "checksum": {"name": "none", "checksum": ""}, "config": {"enabled": true, "alias": null, "schema": "dbt_test__audit", "database": null, "tags": [], "meta": {}, "group": null, "materialized": "test", "severity": "ERROR", "store_failures": true, "store_failures_as": "table", "where": null, "limit": null, "fail_calc": "count(*)", "warn_if": "!= 0", "error_if": "!= 0"}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {"store_failures": true}, "created_at": 1752938915.113337, "relation_name": "\"postgres\".\"public_dbt_test__audit\".\"not_null_core_kpis_to_values_value\"", "raw_code": "{{ test_not_null(**_dbt_generic_test_kwargs) }}", "doc_blocks": [], "language": "sql", "refs": [{"name": "core_kpis_to_values", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": ["macro.dbt.test_not_null"], "nodes": ["model.smi_report.core_kpis_to_values"]}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "column_name": "value", "file_key_name": "models.core_kpis_to_values", "attached_node": "model.smi_report.core_kpis_to_values", "test_metadata": {"name": "not_null", "kwargs": {"column_name": "value", "model": "{{ get_where_subquery(ref('core_kpis_to_values')) }}"}, "namespace": null}}, "model.smi_report.core_array_agg_values": {"database": "postgres", "schema": "public", "name": "core_array_agg_values", "resource_type": "model", "package_name": "smi_report", "path": "core/core_array_agg_values.sql", "original_file_path": "models/core/core_array_agg_values.sql", "unique_id": "model.smi_report.core_array_agg_values", "fqn": ["smi_report", "core", "core_array_agg_values"], "alias": "core_array_agg_values", "checksum": {"name": "sha256", "checksum": "ed9317ab90faf74000bbd5d75711c0c4b180c42a37b00a81ddb97959283d46b1"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "incremental", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [{"sql": "SET statement_timeout = '4h';", "transaction": true, "index": null}, {"sql": "SET lock_timeout = '30min';", "transaction": true, "index": null}, {"sql": "SET idle_in_transaction_session_timeout = '4h';", "transaction": true, "index": null}, {"sql": "SET work_mem = '256MB';", "transaction": true, "index": null}, {"sql": "SET maintenance_work_mem = '1GB';", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_idle = 7200;", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_interval = 30;", "transaction": true, "index": null}, {"sql": "SET tcp_keepalives_count = 9;", "transaction": true, "index": null}], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": ["kpi_id", "geo_id"], "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "This model aggregates KPI values and value_times into arrays for each KPI and geo combination.\n", "columns": {"kpi_id": {"name": "kpi_id", "description": "The unique identifier for each KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "name": {"name": "name", "description": "The name of the KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "kpi_key": {"name": "kpi_key", "description": "The key representing the KPI.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "geo_id": {"name": "geo_id", "description": "The unique identifier for the geographical region.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "unit_id": {"name": "unit_id", "description": "The unique identifier for the unit of measurement.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "value_time_list": {"name": "value_time_list", "description": "Array of value_time values for the KPI and geo.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}, "value_list": {"name": "value_list", "description": "Array of KPI values for the KPI and geo.", "meta": {}, "data_type": null, "constraints": [], "quote": null, "config": {"meta": {}, "tags": []}, "tags": [], "granularity": null, "doc_blocks": []}}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": "smi_report://models/core/schema.yml", "build_path": null, "unrendered_config": {"materialized": "incremental", "unique_key": ["kpi_id", "geo_id"], "pre-hook": ["SET statement_timeout = '4h';", "SET lock_timeout = '30min';", "SET idle_in_transaction_session_timeout = '4h';", "SET work_mem = '256MB';", "SET maintenance_work_mem = '1GB';", "SET tcp_keepalives_idle = 7200;", "SET tcp_keepalives_interval = 30;", "SET tcp_keepalives_count = 9;"]}, "created_at": 1752939681.0899267, "relation_name": "\"postgres\".\"public\".\"core_array_agg_values\"", "raw_code": "-- models/core/core_array_agg_values.sql\n\n-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table\n{{ config(\n    materialized='incremental',\n    unique_key=['kpi_id', 'geo_id'],\n    pre_hook=[\n        \"SET statement_timeout = '4h';\",\n        \"SET lock_timeout = '30min';\",\n        \"SET idle_in_transaction_session_timeout = '4h';\",\n        \"SET work_mem = '256MB';\",\n        \"SET maintenance_work_mem = '1GB';\",\n        \"SET tcp_keepalives_idle = 7200;\",\n        \"SET tcp_keepalives_interval = 30;\",\n        \"SET tcp_keepalives_count = 9;\"\n    ]\n) }}\n\n\nwith kv as (\n    select * from {{ ref('core_kpis_to_values') }}\n),\n\n-- Filter and prepare data for aggregation\nkv_filtered as (\n    select\n        kv.kpi_id,\n        kv.name,\n        kv.kpi_key,\n        kv.geo_id,\n        kv.unit_id,\n        kv.value_time,\n        kv.value,\n        kv.time_updated\n    from kv\n    where kv.value is not null\n),\n\naav as (\n    SELECT\n        kv_filtered.kpi_id,\n        kv_filtered.name,\n        kv_filtered.kpi_key,\n        kv_filtered.geo_id,\n        kv_filtered.unit_id,\n        array_agg(kv_filtered.value_time ORDER BY kv_filtered.value_time) AS value_time_list,\n        array_agg(kv_filtered.value ORDER BY kv_filtered.value_time) AS value_list,\n        max(kv_filtered.time_updated) as time_updated\n    FROM\n        kv_filtered\n    GROUP BY\n        kv_filtered.kpi_id, kv_filtered.name, kv_filtered.kpi_key, kv_filtered.geo_id, kv_filtered.unit_id\n    HAVING\n        array_length(array_agg(kv_filtered.value) FILTER (WHERE kv_filtered.value IS NOT NULL), 1) > 0\n)\n\nSELECT * FROM aav", "doc_blocks": [], "language": "sql", "refs": [{"name": "core_kpis_to_values", "package": null, "version": null}], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": ["model.smi_report.core_kpis_to_values"]}, "compiled_path": "target/compiled/smi_report/models/core/core_array_agg_values.sql", "compiled": true, "compiled_code": "-- models/core/core_array_agg_values.sql\n\n-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table\n\n\n\nwith kv as (\n    select * from \"postgres\".\"public\".\"core_kpis_to_values\"\n),\n\n-- Filter and prepare data for aggregation\nkv_filtered as (\n    select\n        kv.kpi_id,\n        kv.name,\n        kv.kpi_key,\n        kv.geo_id,\n        kv.unit_id,\n        kv.value_time,\n        kv.value,\n        kv.time_updated\n    from kv\n    where kv.value is not null\n),\n\naav as (\n    SELECT\n        kv_filtered.kpi_id,\n        kv_filtered.name,\n        kv_filtered.kpi_key,\n        kv_filtered.geo_id,\n        kv_filtered.unit_id,\n        array_agg(kv_filtered.value_time ORDER BY kv_filtered.value_time) AS value_time_list,\n        array_agg(kv_filtered.value ORDER BY kv_filtered.value_time) AS value_list,\n        max(kv_filtered.time_updated) as time_updated\n    FROM\n        kv_filtered\n    GROUP BY\n        kv_filtered.kpi_id, kv_filtered.name, kv_filtered.kpi_key, kv_filtered.geo_id, kv_filtered.unit_id\n    HAVING\n        array_length(array_agg(kv_filtered.value) FILTER (WHERE kv_filtered.value IS NOT NULL), 1) > 0\n)\n\nSELECT * FROM aav", "extra_ctes_injected": true, "extra_ctes": [], "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}}, "sources": {}, "macros": {"macro.dbt_postgres.postgres__create_table_as": {"name": "postgres__create_table_as", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__create_table_as", "macro_sql": "{% macro postgres__create_table_as(temporary, relation, sql) -%}\n  {%- set unlogged = config.get('unlogged', default=false) -%}\n  {%- set sql_header = config.get('sql_header', none) -%}\n\n  {{ sql_header if sql_header is not none }}\n\n  create {% if temporary -%}\n    temporary\n  {%- elif unlogged -%}\n    unlogged\n  {%- endif %} table {{ relation }}\n  {% set contract_config = config.get('contract') %}\n  {% if contract_config.enforced %}\n    {{ get_assert_columns_equivalent(sql) }}\n  {% endif -%}\n  {% if contract_config.enforced and (not temporary) -%}\n      {{ get_table_columns_and_constraints() }} ;\n    insert into {{ relation }} (\n      {{ adapter.dispatch('get_column_names', 'dbt')() }}\n    )\n    {%- set sql = get_select_subquery(sql) %}\n  {% else %}\n    as\n  {% endif %}\n  (\n    {{ sql }}\n  );\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent", "macro.dbt.get_table_columns_and_constraints", "macro.dbt.default__get_column_names", "macro.dbt.get_select_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.949256, "supported_languages": null}, "macro.dbt_postgres.postgres__get_create_index_sql": {"name": "postgres__get_create_index_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__get_create_index_sql", "macro_sql": "{% macro postgres__get_create_index_sql(relation, index_dict) -%}\n  {%- set index_config = adapter.parse_index(index_dict) -%}\n  {%- set comma_separated_columns = \", \".join(index_config.columns) -%}\n  {%- set index_name = index_config.render(relation) -%}\n\n  create {% if index_config.unique -%}\n    unique\n  {%- endif %} index if not exists\n  \"{{ index_name }}\"\n  on {{ relation }} {% if index_config.type -%}\n    using {{ index_config.type }}\n  {%- endif %}\n  ({{ comma_separated_columns }})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9502025, "supported_languages": null}, "macro.dbt_postgres.postgres__create_schema": {"name": "postgres__create_schema", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__create_schema", "macro_sql": "{% macro postgres__create_schema(relation) -%}\n  {% if relation.database -%}\n    {{ adapter.verify_database(relation.database) }}\n  {%- endif -%}\n  {%- call statement('create_schema') -%}\n    create schema if not exists {{ relation.without_identifier().include(database=False) }}\n  {%- endcall -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.950787, "supported_languages": null}, "macro.dbt_postgres.postgres__drop_schema": {"name": "postgres__drop_schema", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__drop_schema", "macro_sql": "{% macro postgres__drop_schema(relation) -%}\n  {% if relation.database -%}\n    {{ adapter.verify_database(relation.database) }}\n  {%- endif -%}\n  {%- call statement('drop_schema') -%}\n    drop schema if exists {{ relation.without_identifier().include(database=False) }} cascade\n  {%- endcall -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.951361, "supported_languages": null}, "macro.dbt_postgres.postgres__get_columns_in_relation": {"name": "postgres__get_columns_in_relation", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__get_columns_in_relation", "macro_sql": "{% macro postgres__get_columns_in_relation(relation) -%}\n  {% call statement('get_columns_in_relation', fetch_result=True) %}\n      select\n          column_name,\n          data_type,\n          character_maximum_length,\n          numeric_precision,\n          numeric_scale\n\n      from {{ relation.information_schema('columns') }}\n      where table_name = '{{ relation.identifier }}'\n        {% if relation.schema %}\n        and table_schema = '{{ relation.schema }}'\n        {% endif %}\n      order by ordinal_position\n\n  {% endcall %}\n  {% set table = load_result('get_columns_in_relation').table %}\n  {{ return(sql_convert_columns_in_relation(table)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.sql_convert_columns_in_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9522269, "supported_languages": null}, "macro.dbt_postgres.postgres__list_relations_without_caching": {"name": "postgres__list_relations_without_caching", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__list_relations_without_caching", "macro_sql": "{% macro postgres__list_relations_without_caching(schema_relation) %}\n  {% call statement('list_relations_without_caching', fetch_result=True) -%}\n    select\n      '{{ schema_relation.database }}' as database,\n      tablename as name,\n      schemaname as schema,\n      'table' as type\n    from pg_tables\n    where schemaname ilike '{{ schema_relation.schema }}'\n    union all\n    select\n      '{{ schema_relation.database }}' as database,\n      viewname as name,\n      schemaname as schema,\n      'view' as type\n    from pg_views\n    where schemaname ilike '{{ schema_relation.schema }}'\n    union all\n    select\n      '{{ schema_relation.database }}' as database,\n      matviewname as name,\n      schemaname as schema,\n      'materialized_view' as type\n    from pg_matviews\n    where schemaname ilike '{{ schema_relation.schema }}'\n  {% endcall %}\n  {{ return(load_result('list_relations_without_caching').table) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9530222, "supported_languages": null}, "macro.dbt_postgres.postgres__information_schema_name": {"name": "postgres__information_schema_name", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__information_schema_name", "macro_sql": "{% macro postgres__information_schema_name(database) -%}\n  {% if database_name -%}\n    {{ adapter.verify_database(database_name) }}\n  {%- endif -%}\n  information_schema\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9533513, "supported_languages": null}, "macro.dbt_postgres.postgres__list_schemas": {"name": "postgres__list_schemas", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__list_schemas", "macro_sql": "{% macro postgres__list_schemas(database) %}\n  {% if database -%}\n    {{ adapter.verify_database(database) }}\n  {%- endif -%}\n  {% call statement('list_schemas', fetch_result=True, auto_begin=False) %}\n    select distinct nspname from pg_namespace\n  {% endcall %}\n  {{ return(load_result('list_schemas').table) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.953977, "supported_languages": null}, "macro.dbt_postgres.postgres__check_schema_exists": {"name": "postgres__check_schema_exists", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__check_schema_exists", "macro_sql": "{% macro postgres__check_schema_exists(information_schema, schema) -%}\n  {% if information_schema.database -%}\n    {{ adapter.verify_database(information_schema.database) }}\n  {%- endif -%}\n  {% call statement('check_schema_exists', fetch_result=True, auto_begin=False) %}\n    select count(*) from pg_namespace where nspname = '{{ schema }}'\n  {% endcall %}\n  {{ return(load_result('check_schema_exists').table) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9546788, "supported_languages": null}, "macro.dbt_postgres.postgres__make_relation_with_suffix": {"name": "postgres__make_relation_with_suffix", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__make_relation_with_suffix", "macro_sql": "{% macro postgres__make_relation_with_suffix(base_relation, suffix, dstring) %}\n    {% if dstring %}\n      {% set dt = modules.datetime.datetime.now() %}\n      {% set dtstring = dt.strftime(\"%H%M%S%f\") %}\n      {% set suffix = suffix ~ dtstring %}\n    {% endif %}\n    {% set suffix_length = suffix|length %}\n    {% set relation_max_name_length = base_relation.relation_max_name_length() %}\n    {% if suffix_length > relation_max_name_length %}\n        {% do exceptions.raise_compiler_error('Relation suffix is too long (' ~ suffix_length ~ ' characters). Maximum length is ' ~ relation_max_name_length ~ ' characters.') %}\n    {% endif %}\n    {% set identifier = base_relation.identifier[:relation_max_name_length - suffix_length] ~ suffix %}\n\n    {{ return(base_relation.incorporate(path={\"identifier\": identifier })) }}\n\n  {% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9562988, "supported_languages": null}, "macro.dbt_postgres.postgres__make_intermediate_relation": {"name": "postgres__make_intermediate_relation", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__make_intermediate_relation", "macro_sql": "{% macro postgres__make_intermediate_relation(base_relation, suffix) %}\n    {{ return(postgres__make_relation_with_suffix(base_relation, suffix, dstring=False)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__make_relation_with_suffix"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.956681, "supported_languages": null}, "macro.dbt_postgres.postgres__make_temp_relation": {"name": "postgres__make_temp_relation", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__make_temp_relation", "macro_sql": "{% macro postgres__make_temp_relation(base_relation, suffix) %}\n    {% set temp_relation = postgres__make_relation_with_suffix(base_relation, suffix, dstring=True) %}\n    {{ return(temp_relation.incorporate(path={\"schema\": none,\n                                              \"database\": none})) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__make_relation_with_suffix"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9572587, "supported_languages": null}, "macro.dbt_postgres.postgres__make_backup_relation": {"name": "postgres__make_backup_relation", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__make_backup_relation", "macro_sql": "{% macro postgres__make_backup_relation(base_relation, backup_relation_type, suffix) %}\n    {% set backup_relation = postgres__make_relation_with_suffix(base_relation, suffix, dstring=False) %}\n    {{ return(backup_relation.incorporate(type=backup_relation_type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__make_relation_with_suffix"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.957761, "supported_languages": null}, "macro.dbt_postgres.postgres_escape_comment": {"name": "postgres_escape_comment", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres_escape_comment", "macro_sql": "{% macro postgres_escape_comment(comment) -%}\n  {% if comment is not string %}\n    {% do exceptions.raise_compiler_error('cannot escape a non-string: ' ~ comment) %}\n  {% endif %}\n  {%- set magic = '$dbt_comment_literal_block$' -%}\n  {%- if magic in comment -%}\n    {%- do exceptions.raise_compiler_error('The string ' ~ magic ~ ' is not allowed in comments.') -%}\n  {%- endif -%}\n  {{ magic }}{{ comment }}{{ magic }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9585419, "supported_languages": null}, "macro.dbt_postgres.postgres__alter_relation_comment": {"name": "postgres__alter_relation_comment", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__alter_relation_comment", "macro_sql": "{% macro postgres__alter_relation_comment(relation, comment) %}\n  {% set escaped_comment = postgres_escape_comment(comment) %}\n  {% if relation.type == 'materialized_view' -%}\n    {% set relation_type = \"materialized view\" %}\n  {%- else -%}\n    {%- set relation_type = relation.type -%}\n  {%- endif -%}\n  comment on {{ relation_type }} {{ relation }} is {{ escaped_comment }};\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres_escape_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9594207, "supported_languages": null}, "macro.dbt_postgres.postgres__alter_column_comment": {"name": "postgres__alter_column_comment", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__alter_column_comment", "macro_sql": "{% macro postgres__alter_column_comment(relation, column_dict) %}\n  {% set existing_columns = adapter.get_columns_in_relation(relation) | map(attribute=\"name\") | list %}\n  {% for column_name in column_dict if (column_name in existing_columns) %}\n    {% set comment = column_dict[column_name]['description'] %}\n    {% set escaped_comment = postgres_escape_comment(comment) %}\n    comment on column {{ relation }}.{{ adapter.quote(column_name) if column_dict[column_name]['quote'] else column_name }} is {{ escaped_comment }};\n  {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres_escape_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.960521, "supported_languages": null}, "macro.dbt_postgres.postgres__get_show_grant_sql": {"name": "postgres__get_show_grant_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__get_show_grant_sql", "macro_sql": "\n\n{%- macro postgres__get_show_grant_sql(relation) -%}\n  select grantee, privilege_type\n  from {{ relation.information_schema('role_table_grants') }}\n      where grantor = current_role\n        and grantee != current_role\n        and table_schema = '{{ relation.schema }}'\n        and table_name = '{{ relation.identifier }}'\n{%- endmacro -%}\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.960909, "supported_languages": null}, "macro.dbt_postgres.postgres__copy_grants": {"name": "postgres__copy_grants", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__copy_grants", "macro_sql": "{% macro postgres__copy_grants() %}\n    {{ return(False) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9611347, "supported_languages": null}, "macro.dbt_postgres.postgres__get_show_indexes_sql": {"name": "postgres__get_show_indexes_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__get_show_indexes_sql", "macro_sql": "{% macro postgres__get_show_indexes_sql(relation) %}\n    select\n        i.relname                                   as name,\n        m.amname                                    as method,\n        ix.indisunique                              as \"unique\",\n        array_to_string(array_agg(a.attname), ',')  as column_names\n    from pg_index ix\n    join pg_class i\n        on i.oid = ix.indexrelid\n    join pg_am m\n        on m.oid=i.relam\n    join pg_class t\n        on t.oid = ix.indrelid\n    join pg_namespace n\n        on n.oid = t.relnamespace\n    join pg_attribute a\n        on a.attrelid = t.oid\n        and a.attnum = ANY(ix.indkey)\n    where t.relname = '{{ relation.identifier }}'\n      and n.nspname = '{{ relation.schema }}'\n      and t.relkind in ('r', 'm')\n    group by 1, 2, 3\n    order by 1, 2, 3\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9614618, "supported_languages": null}, "macro.dbt_postgres.postgres__get_drop_index_sql": {"name": "postgres__get_drop_index_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/adapters.sql", "original_file_path": "macros/adapters.sql", "unique_id": "macro.dbt_postgres.postgres__get_drop_index_sql", "macro_sql": "\n\n\n{%- macro postgres__get_drop_index_sql(relation, index_name) -%}\n    drop index if exists \"{{ relation.schema }}\".\"{{ index_name }}\"\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.961729, "supported_languages": null}, "macro.dbt_postgres.postgres__get_catalog_relations": {"name": "postgres__get_catalog_relations", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/catalog.sql", "original_file_path": "macros/catalog.sql", "unique_id": "macro.dbt_postgres.postgres__get_catalog_relations", "macro_sql": "{% macro postgres__get_catalog_relations(information_schema, relations) -%}\n  {%- call statement('catalog', fetch_result=True) -%}\n\n    {#\n      If the user has multiple databases set and the first one is wrong, this will fail.\n      But we won't fail in the case where there are multiple quoting-difference-only dbs, which is better.\n    #}\n    {% set database = information_schema.database %}\n    {{ adapter.verify_database(database) }}\n\n    select\n        '{{ database }}' as table_database,\n        sch.nspname as table_schema,\n        tbl.relname as table_name,\n        case tbl.relkind\n            when 'v' then 'VIEW'\n            when 'm' then 'MATERIALIZED VIEW'\n            else 'BASE TABLE'\n        end as table_type,\n        tbl_desc.description as table_comment,\n        col.attname as column_name,\n        col.attnum as column_index,\n        pg_catalog.format_type(col.atttypid, col.atttypmod) as column_type,\n        col_desc.description as column_comment,\n        pg_get_userbyid(tbl.relowner) as table_owner\n\n    from pg_catalog.pg_namespace sch\n    join pg_catalog.pg_class tbl on tbl.relnamespace = sch.oid\n    join pg_catalog.pg_attribute col on col.attrelid = tbl.oid\n    left outer join pg_catalog.pg_description tbl_desc on (tbl_desc.objoid = tbl.oid and tbl_desc.objsubid = 0)\n    left outer join pg_catalog.pg_description col_desc on (col_desc.objoid = tbl.oid and col_desc.objsubid = col.attnum)\n    where (\n      {%- for relation in relations -%}\n        {%- if relation.identifier -%}\n          (upper(sch.nspname) = upper('{{ relation.schema }}') and\n           upper(tbl.relname) = upper('{{ relation.identifier }}'))\n        {%- else-%}\n          upper(sch.nspname) = upper('{{ relation.schema }}')\n        {%- endif -%}\n        {%- if not loop.last %} or {% endif -%}\n      {%- endfor -%}\n    )\n      and not pg_is_other_temp_schema(sch.oid) -- not a temporary schema belonging to another session\n      and tbl.relpersistence in ('p', 'u') -- [p]ermanent table or [u]nlogged table. Exclude [t]emporary tables\n      and tbl.relkind in ('r', 'v', 'f', 'p', 'm') -- o[r]dinary table, [v]iew, [f]oreign table, [p]artitioned table, [m]aterialized view. Other values are [i]ndex, [S]equence, [c]omposite type, [t]OAST table\n      and col.attnum > 0 -- negative numbers are used for system columns such as oid\n      and not col.attisdropped -- column as not been dropped\n\n    order by\n        sch.nspname,\n        tbl.relname,\n        col.attnum\n\n  {%- endcall -%}\n\n  {{ return(load_result('catalog').table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9635396, "supported_languages": null}, "macro.dbt_postgres.postgres__get_catalog": {"name": "postgres__get_catalog", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/catalog.sql", "original_file_path": "macros/catalog.sql", "unique_id": "macro.dbt_postgres.postgres__get_catalog", "macro_sql": "{% macro postgres__get_catalog(information_schema, schemas) -%}\n  {%- set relations = [] -%}\n  {%- for schema in schemas -%}\n    {%- set dummy = relations.append({'schema': schema}) -%}\n  {%- endfor -%}\n  {{ return(postgres__get_catalog_relations(information_schema, relations)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_catalog_relations"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.96419, "supported_languages": null}, "macro.dbt_postgres.postgres__get_relations": {"name": "postgres__get_relations", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations.sql", "original_file_path": "macros/relations.sql", "unique_id": "macro.dbt_postgres.postgres__get_relations", "macro_sql": "{% macro postgres__get_relations() -%}\n\n  {#\n      -- in pg_depend, objid is the dependent, refobjid is the referenced object\n      --  > a pg_depend entry indicates that the referenced object cannot be\n      --  > dropped without also dropping the dependent object.\n  #}\n\n  {%- call statement('relations', fetch_result=True) -%}\n    with relation as (\n        select\n            pg_rewrite.ev_class as class,\n            pg_rewrite.oid as id\n        from pg_rewrite\n    ),\n    class as (\n        select\n            oid as id,\n            relname as name,\n            relnamespace as schema,\n            relkind as kind\n        from pg_class\n    ),\n    dependency as (\n        select distinct\n            pg_depend.objid as id,\n            pg_depend.refobjid as ref\n        from pg_depend\n    ),\n    schema as (\n        select\n            pg_namespace.oid as id,\n            pg_namespace.nspname as name\n        from pg_namespace\n        where nspname != 'information_schema' and nspname not like 'pg\\_%'\n    ),\n    referenced as (\n        select\n            relation.id AS id,\n            referenced_class.name ,\n            referenced_class.schema ,\n            referenced_class.kind\n        from relation\n        join class as referenced_class on relation.class=referenced_class.id\n        where referenced_class.kind in ('r', 'v', 'm')\n    ),\n    relationships as (\n        select\n            referenced.name as referenced_name,\n            referenced.schema as referenced_schema_id,\n            dependent_class.name as dependent_name,\n            dependent_class.schema as dependent_schema_id,\n            referenced.kind as kind\n        from referenced\n        join dependency on referenced.id=dependency.id\n        join class as dependent_class on dependency.ref=dependent_class.id\n        where\n            (referenced.name != dependent_class.name or\n             referenced.schema != dependent_class.schema)\n    )\n\n    select\n        referenced_schema.name as referenced_schema,\n        relationships.referenced_name as referenced_name,\n        dependent_schema.name as dependent_schema,\n        relationships.dependent_name as dependent_name\n    from relationships\n    join schema as dependent_schema on relationships.dependent_schema_id=dependent_schema.id\n    join schema as referenced_schema on relationships.referenced_schema_id=referenced_schema.id\n    group by referenced_schema, referenced_name, dependent_schema, dependent_name\n    order by referenced_schema, referenced_name, dependent_schema, dependent_name;\n\n  {%- endcall -%}\n\n  {{ return(load_result('relations').table) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9651875, "supported_languages": null}, "macro.dbt_postgres.postgres_get_relations": {"name": "postgres_get_relations", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations.sql", "original_file_path": "macros/relations.sql", "unique_id": "macro.dbt_postgres.postgres_get_relations", "macro_sql": "{% macro postgres_get_relations() %}\n  {{ return(postgres__get_relations()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_relations"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9654324, "supported_languages": null}, "macro.dbt_postgres.postgres__current_timestamp": {"name": "postgres__current_timestamp", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/timestamps.sql", "original_file_path": "macros/timestamps.sql", "unique_id": "macro.dbt_postgres.postgres__current_timestamp", "macro_sql": "{% macro postgres__current_timestamp() -%}\n    now()\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9658163, "supported_languages": null}, "macro.dbt_postgres.postgres__snapshot_string_as_time": {"name": "postgres__snapshot_string_as_time", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/timestamps.sql", "original_file_path": "macros/timestamps.sql", "unique_id": "macro.dbt_postgres.postgres__snapshot_string_as_time", "macro_sql": "{% macro postgres__snapshot_string_as_time(timestamp) -%}\n    {%- set result = \"'\" ~ timestamp ~ \"'::timestamp without time zone\" -%}\n    {{ return(result) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9661534, "supported_languages": null}, "macro.dbt_postgres.postgres__snapshot_get_time": {"name": "postgres__snapshot_get_time", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/timestamps.sql", "original_file_path": "macros/timestamps.sql", "unique_id": "macro.dbt_postgres.postgres__snapshot_get_time", "macro_sql": "{% macro postgres__snapshot_get_time() -%}\n  {{ current_timestamp() }}::timestamp without time zone\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9663577, "supported_languages": null}, "macro.dbt_postgres.postgres__current_timestamp_backcompat": {"name": "postgres__current_timestamp_backcompat", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/timestamps.sql", "original_file_path": "macros/timestamps.sql", "unique_id": "macro.dbt_postgres.postgres__current_timestamp_backcompat", "macro_sql": "{% macro postgres__current_timestamp_backcompat() %}\n    current_timestamp::{{ type_timestamp() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.type_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9665647, "supported_languages": null}, "macro.dbt_postgres.postgres__current_timestamp_in_utc_backcompat": {"name": "postgres__current_timestamp_in_utc_backcompat", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/timestamps.sql", "original_file_path": "macros/timestamps.sql", "unique_id": "macro.dbt_postgres.postgres__current_timestamp_in_utc_backcompat", "macro_sql": "{% macro postgres__current_timestamp_in_utc_backcompat() %}\n    (current_timestamp at time zone 'utc')::{{ type_timestamp() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.type_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9667706, "supported_languages": null}, "macro.dbt_postgres.postgres__get_incremental_default_sql": {"name": "postgres__get_incremental_default_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/materializations/incremental_strategies.sql", "original_file_path": "macros/materializations/incremental_strategies.sql", "unique_id": "macro.dbt_postgres.postgres__get_incremental_default_sql", "macro_sql": "{% macro postgres__get_incremental_default_sql(arg_dict) %}\n\n  {% if arg_dict[\"unique_key\"] %}\n    {% do return(get_incremental_delete_insert_sql(arg_dict)) %}\n  {% else %}\n    {% do return(get_incremental_append_sql(arg_dict)) %}\n  {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_incremental_delete_insert_sql", "macro.dbt.get_incremental_append_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9675763, "supported_languages": null}, "macro.dbt_postgres.postgres__get_incremental_microbatch_sql": {"name": "postgres__get_incremental_microbatch_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/materializations/incremental_strategies.sql", "original_file_path": "macros/materializations/incremental_strategies.sql", "unique_id": "macro.dbt_postgres.postgres__get_incremental_microbatch_sql", "macro_sql": "{% macro postgres__get_incremental_microbatch_sql(arg_dict) %}\n\n  {% if arg_dict[\"unique_key\"] %}\n    {% do return(adapter.dispatch('get_incremental_merge_sql', 'dbt')(arg_dict)) %}\n  {% else %}\n    {{ exceptions.raise_compiler_error(\"dbt-postgres 'microbatch' requires a `unique_key` config\") }}\n  {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_incremental_merge_sql", "macro.dbt.default__get_incremental_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9681687, "supported_languages": null}, "macro.dbt_postgres.postgres__snapshot_merge_sql": {"name": "postgres__snapshot_merge_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/materializations/snapshot_merge.sql", "original_file_path": "macros/materializations/snapshot_merge.sql", "unique_id": "macro.dbt_postgres.postgres__snapshot_merge_sql", "macro_sql": "{% macro postgres__snapshot_merge_sql(target, source, insert_cols) -%}\n    {%- set insert_cols_csv = insert_cols | join(', ') -%}\n\n    {%- set columns = config.get(\"snapshot_table_column_names\") or get_snapshot_table_column_names() -%}\n\n    update {{ target }}\n    set {{ columns.dbt_valid_to }} = DBT_INTERNAL_SOURCE.{{ columns.dbt_valid_to }}\n    from {{ source }} as DBT_INTERNAL_SOURCE\n    where DBT_INTERNAL_SOURCE.{{ columns.dbt_scd_id }}::text = {{ target }}.{{ columns.dbt_scd_id }}::text\n      and DBT_INTERNAL_SOURCE.dbt_change_type::text in ('update'::text, 'delete'::text)\n      {% if config.get(\"dbt_valid_to_current\") %}\n        and ({{ target }}.{{ columns.dbt_valid_to }} = {{ config.get('dbt_valid_to_current') }} or {{ target }}.{{ columns.dbt_valid_to }} is null);\n      {% else %}\n        and {{ target }}.{{ columns.dbt_valid_to }} is null;\n      {% endif %}\n\n\n    insert into {{ target }} ({{ insert_cols_csv }})\n    select {% for column in insert_cols -%}\n        DBT_INTERNAL_SOURCE.{{ column }} {%- if not loop.last %}, {%- endif %}\n    {%- endfor %}\n    from {{ source }} as DBT_INTERNAL_SOURCE\n    where DBT_INTERNAL_SOURCE.dbt_change_type::text = 'insert'::text;\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9701724, "supported_languages": null}, "macro.dbt_postgres.postgres__get_alter_materialized_view_as_sql": {"name": "postgres__get_alter_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/alter.sql", "original_file_path": "macros/relations/materialized_view/alter.sql", "unique_id": "macro.dbt_postgres.postgres__get_alter_materialized_view_as_sql", "macro_sql": "{% macro postgres__get_alter_materialized_view_as_sql(\n    relation,\n    configuration_changes,\n    sql,\n    existing_relation,\n    backup_relation,\n    intermediate_relation\n) %}\n\n    -- apply a full refresh immediately if needed\n    {% if configuration_changes.requires_full_refresh %}\n\n        {{ get_replace_sql(existing_relation, relation, sql) }}\n\n    -- otherwise apply individual changes as needed\n    {% else %}\n\n        {{ postgres__update_indexes_on_materialized_view(relation, configuration_changes.indexes) }}\n\n    {%- endif -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_replace_sql", "macro.dbt_postgres.postgres__update_indexes_on_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.971175, "supported_languages": null}, "macro.dbt_postgres.postgres__update_indexes_on_materialized_view": {"name": "postgres__update_indexes_on_materialized_view", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/alter.sql", "original_file_path": "macros/relations/materialized_view/alter.sql", "unique_id": "macro.dbt_postgres.postgres__update_indexes_on_materialized_view", "macro_sql": "\n\n\n{%- macro postgres__update_indexes_on_materialized_view(relation, index_changes) -%}\n    {{- log(\"Applying UPDATE INDEXES to: \" ~ relation) -}}\n\n    {%- for _index_change in index_changes -%}\n        {%- set _index = _index_change.context -%}\n\n        {%- if _index_change.action == \"drop\" -%}\n\n            {{ postgres__get_drop_index_sql(relation, _index.name) }}\n\n        {%- elif _index_change.action == \"create\" -%}\n\n            {{ postgres__get_create_index_sql(relation, _index.as_node_config) }}\n\n        {%- endif -%}\n\t{{ ';' if not loop.last else \"\" }}\n\n    {%- endfor -%}\n\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_drop_index_sql", "macro.dbt_postgres.postgres__get_create_index_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9722445, "supported_languages": null}, "macro.dbt_postgres.postgres__get_materialized_view_configuration_changes": {"name": "postgres__get_materialized_view_configuration_changes", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/alter.sql", "original_file_path": "macros/relations/materialized_view/alter.sql", "unique_id": "macro.dbt_postgres.postgres__get_materialized_view_configuration_changes", "macro_sql": "{% macro postgres__get_materialized_view_configuration_changes(existing_relation, new_config) %}\n    {% set _existing_materialized_view = postgres__describe_materialized_view(existing_relation) %}\n    {% set _configuration_changes = existing_relation.get_materialized_view_config_change_collection(_existing_materialized_view, new_config.model) %}\n    {% do return(_configuration_changes) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__describe_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9727898, "supported_languages": null}, "macro.dbt_postgres.postgres__get_create_materialized_view_as_sql": {"name": "postgres__get_create_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/create.sql", "original_file_path": "macros/relations/materialized_view/create.sql", "unique_id": "macro.dbt_postgres.postgres__get_create_materialized_view_as_sql", "macro_sql": "{% macro postgres__get_create_materialized_view_as_sql(relation, sql) %}\n    create materialized view if not exists {{ relation }} as {{ sql }};\n\n    {% for _index_dict in config.get('indexes', []) -%}\n        {{- get_create_index_sql(relation, _index_dict) -}}{{ ';' if not loop.last else \"\" }}\n    {%- endfor -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_create_index_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9735699, "supported_languages": null}, "macro.dbt_postgres.postgres__describe_materialized_view": {"name": "postgres__describe_materialized_view", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/describe.sql", "original_file_path": "macros/relations/materialized_view/describe.sql", "unique_id": "macro.dbt_postgres.postgres__describe_materialized_view", "macro_sql": "{% macro postgres__describe_materialized_view(relation) %}\n    -- for now just get the indexes, we don't need the name or the query yet\n    {% set _indexes = run_query(get_show_indexes_sql(relation)) %}\n    {% do return({'indexes': _indexes}) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query", "macro.dbt.get_show_indexes_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9741256, "supported_languages": null}, "macro.dbt_postgres.postgres__drop_materialized_view": {"name": "postgres__drop_materialized_view", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/drop.sql", "original_file_path": "macros/relations/materialized_view/drop.sql", "unique_id": "macro.dbt_postgres.postgres__drop_materialized_view", "macro_sql": "{% macro postgres__drop_materialized_view(relation) -%}\n    drop materialized view if exists {{ relation }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.974473, "supported_languages": null}, "macro.dbt_postgres.postgres__refresh_materialized_view": {"name": "postgres__refresh_materialized_view", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/refresh.sql", "original_file_path": "macros/relations/materialized_view/refresh.sql", "unique_id": "macro.dbt_postgres.postgres__refresh_materialized_view", "macro_sql": "{% macro postgres__refresh_materialized_view(relation) %}\n    refresh materialized view {{ relation }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9747517, "supported_languages": null}, "macro.dbt_postgres.postgres__get_rename_materialized_view_sql": {"name": "postgres__get_rename_materialized_view_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/materialized_view/rename.sql", "original_file_path": "macros/relations/materialized_view/rename.sql", "unique_id": "macro.dbt_postgres.postgres__get_rename_materialized_view_sql", "macro_sql": "{% macro postgres__get_rename_materialized_view_sql(relation, new_name) %}\n    alter materialized view {{ relation }} rename to {{ new_name }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9750888, "supported_languages": null}, "macro.dbt_postgres.postgres__drop_table": {"name": "postgres__drop_table", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/table/drop.sql", "original_file_path": "macros/relations/table/drop.sql", "unique_id": "macro.dbt_postgres.postgres__drop_table", "macro_sql": "{% macro postgres__drop_table(relation) -%}\n    drop table if exists {{ relation }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9753594, "supported_languages": null}, "macro.dbt_postgres.postgres__get_rename_table_sql": {"name": "postgres__get_rename_table_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/table/rename.sql", "original_file_path": "macros/relations/table/rename.sql", "unique_id": "macro.dbt_postgres.postgres__get_rename_table_sql", "macro_sql": "{% macro postgres__get_rename_table_sql(relation, new_name) %}\n    alter table {{ relation }} rename to {{ new_name }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9757, "supported_languages": null}, "macro.dbt_postgres.postgres__get_replace_table_sql": {"name": "postgres__get_replace_table_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/table/replace.sql", "original_file_path": "macros/relations/table/replace.sql", "unique_id": "macro.dbt_postgres.postgres__get_replace_table_sql", "macro_sql": "{% macro postgres__get_replace_table_sql(relation, sql) -%}\n\n    {%- set sql_header = config.get('sql_header', none) -%}\n    {{ sql_header if sql_header is not none }}\n\n    create or replace table {{ relation }}\n        {% set contract_config = config.get('contract') %}\n        {% if contract_config.enforced %}\n            {{ get_assert_columns_equivalent(sql) }}\n            {{ get_table_columns_and_constraints() }}\n            {%- set sql = get_select_subquery(sql) %}\n        {% endif %}\n    as (\n        {{ sql }}\n    );\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent", "macro.dbt.get_table_columns_and_constraints", "macro.dbt.get_select_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9768283, "supported_languages": null}, "macro.dbt_postgres.postgres__drop_view": {"name": "postgres__drop_view", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/view/drop.sql", "original_file_path": "macros/relations/view/drop.sql", "unique_id": "macro.dbt_postgres.postgres__drop_view", "macro_sql": "{% macro postgres__drop_view(relation) -%}\n    drop view if exists {{ relation }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9771202, "supported_languages": null}, "macro.dbt_postgres.postgres__get_rename_view_sql": {"name": "postgres__get_rename_view_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/view/rename.sql", "original_file_path": "macros/relations/view/rename.sql", "unique_id": "macro.dbt_postgres.postgres__get_rename_view_sql", "macro_sql": "{% macro postgres__get_rename_view_sql(relation, new_name) %}\n    alter view {{ relation }} rename to {{ new_name }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.977455, "supported_languages": null}, "macro.dbt_postgres.postgres__get_replace_view_sql": {"name": "postgres__get_replace_view_sql", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/relations/view/replace.sql", "original_file_path": "macros/relations/view/replace.sql", "unique_id": "macro.dbt_postgres.postgres__get_replace_view_sql", "macro_sql": "{% macro postgres__get_replace_view_sql(relation, sql) -%}\n\n    {%- set sql_header = config.get('sql_header', none) -%}\n    {{ sql_header if sql_header is not none }}\n\n    create or replace view {{ relation }}\n        {% set contract_config = config.get('contract') %}\n        {% if contract_config.enforced %}\n            {{ get_assert_columns_equivalent(sql) }}\n        {%- endif %}\n    as (\n        {{ sql }}\n    );\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9783955, "supported_languages": null}, "macro.dbt_postgres.postgres__any_value": {"name": "postgres__any_value", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/utils/any_value.sql", "original_file_path": "macros/utils/any_value.sql", "unique_id": "macro.dbt_postgres.postgres__any_value", "macro_sql": "{% macro postgres__any_value(expression) -%}\n\n    min({{ expression }})\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9786925, "supported_languages": null}, "macro.dbt_postgres.postgres__dateadd": {"name": "postgres__dateadd", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/utils/dateadd.sql", "original_file_path": "macros/utils/dateadd.sql", "unique_id": "macro.dbt_postgres.postgres__dateadd", "macro_sql": "{% macro postgres__dateadd(datepart, interval, from_date_or_timestamp) %}\n\n    {{ from_date_or_timestamp }} + ((interval '1 {{ datepart }}') * ({{ interval }}))\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9792197, "supported_languages": null}, "macro.dbt_postgres.postgres__datediff": {"name": "postgres__datediff", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/utils/datediff.sql", "original_file_path": "macros/utils/datediff.sql", "unique_id": "macro.dbt_postgres.postgres__datediff", "macro_sql": "{% macro postgres__datediff(first_date, second_date, datepart) -%}\n\n    {% if datepart == 'year' %}\n        (date_part('year', ({{second_date}})::date) - date_part('year', ({{first_date}})::date))\n    {% elif datepart == 'quarter' %}\n        ({{ datediff(first_date, second_date, 'year') }} * 4 + date_part('quarter', ({{second_date}})::date) - date_part('quarter', ({{first_date}})::date))\n    {% elif datepart == 'month' %}\n        ({{ datediff(first_date, second_date, 'year') }} * 12 + date_part('month', ({{second_date}})::date) - date_part('month', ({{first_date}})::date))\n    {% elif datepart == 'day' %}\n        (({{second_date}})::date - ({{first_date}})::date)\n    {% elif datepart == 'week' %}\n        ({{ datediff(first_date, second_date, 'day') }} / 7 + case\n            when date_part('dow', ({{first_date}})::timestamp) <= date_part('dow', ({{second_date}})::timestamp) then\n                case when {{first_date}} <= {{second_date}} then 0 else -1 end\n            else\n                case when {{first_date}} <= {{second_date}} then 1 else 0 end\n        end)\n    {% elif datepart == 'hour' %}\n        ({{ datediff(first_date, second_date, 'day') }} * 24 + date_part('hour', ({{second_date}})::timestamp) - date_part('hour', ({{first_date}})::timestamp))\n    {% elif datepart == 'minute' %}\n        ({{ datediff(first_date, second_date, 'hour') }} * 60 + date_part('minute', ({{second_date}})::timestamp) - date_part('minute', ({{first_date}})::timestamp))\n    {% elif datepart == 'second' %}\n        ({{ datediff(first_date, second_date, 'minute') }} * 60 + floor(date_part('second', ({{second_date}})::timestamp)) - floor(date_part('second', ({{first_date}})::timestamp)))\n    {% elif datepart == 'millisecond' %}\n        ({{ datediff(first_date, second_date, 'minute') }} * 60000 + floor(date_part('millisecond', ({{second_date}})::timestamp)) - floor(date_part('millisecond', ({{first_date}})::timestamp)))\n    {% elif datepart == 'microsecond' %}\n        ({{ datediff(first_date, second_date, 'minute') }} * 60000000 + floor(date_part('microsecond', ({{second_date}})::timestamp)) - floor(date_part('microsecond', ({{first_date}})::timestamp)))\n    {% else %}\n        {{ exceptions.raise_compiler_error(\"Unsupported datepart for macro datediff in postgres: {!r}\".format(datepart)) }}\n    {% endif %}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.datediff"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9832222, "supported_languages": null}, "macro.dbt_postgres.postgres__last_day": {"name": "postgres__last_day", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/utils/last_day.sql", "original_file_path": "macros/utils/last_day.sql", "unique_id": "macro.dbt_postgres.postgres__last_day", "macro_sql": "{% macro postgres__last_day(date, datepart) -%}\n\n    {%- if datepart == 'quarter' -%}\n    -- postgres dateadd does not support quarter interval.\n    cast(\n        {{dbt.dateadd('day', '-1',\n        dbt.dateadd('month', '3', dbt.date_trunc(datepart, date))\n        )}}\n        as date)\n    {%- else -%}\n    {{dbt.default_last_day(date, datepart)}}\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.dateadd", "macro.dbt.date_trunc", "macro.dbt.default_last_day"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9841385, "supported_languages": null}, "macro.dbt_postgres.postgres__listagg": {"name": "postgres__listagg", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/utils/listagg.sql", "original_file_path": "macros/utils/listagg.sql", "unique_id": "macro.dbt_postgres.postgres__listagg", "macro_sql": "{% macro postgres__listagg(measure, delimiter_text, order_by_clause, limit_num) -%}\n\n    {% if limit_num -%}\n    array_to_string(\n        (array_agg(\n            {{ measure }}\n            {% if order_by_clause -%}\n            {{ order_by_clause }}\n            {%- endif %}\n        ))[1:{{ limit_num }}],\n        {{ delimiter_text }}\n        )\n    {%- else %}\n    string_agg(\n        {{ measure }},\n        {{ delimiter_text }}\n        {% if order_by_clause -%}\n        {{ order_by_clause }}\n        {%- endif %}\n        )\n    {%- endif %}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9850795, "supported_languages": null}, "macro.dbt_postgres.postgres__split_part": {"name": "postgres__split_part", "resource_type": "macro", "package_name": "dbt_postgres", "path": "macros/utils/split_part.sql", "original_file_path": "macros/utils/split_part.sql", "unique_id": "macro.dbt_postgres.postgres__split_part", "macro_sql": "{% macro postgres__split_part(string_text, delimiter_text, part_number) %}\n\n  {% if part_number >= 0 %}\n    {{ dbt.default__split_part(string_text, delimiter_text, part_number) }}\n  {% else %}\n    {{ dbt._split_part_negative(string_text, delimiter_text, part_number) }}\n  {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__split_part", "macro.dbt._split_part_negative"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9857955, "supported_languages": null}, "macro.dbt.copy_grants": {"name": "copy_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.copy_grants", "macro_sql": "{% macro copy_grants() %}\n    {{ return(adapter.dispatch('copy_grants', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__copy_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9876819, "supported_languages": null}, "macro.dbt.default__copy_grants": {"name": "default__copy_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__copy_grants", "macro_sql": "{% macro default__copy_grants() %}\n    {{ return(True) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9879122, "supported_languages": null}, "macro.dbt.support_multiple_grantees_per_dcl_statement": {"name": "support_multiple_grantees_per_dcl_statement", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.support_multiple_grantees_per_dcl_statement", "macro_sql": "{% macro support_multiple_grantees_per_dcl_statement() %}\n    {{ return(adapter.dispatch('support_multiple_grantees_per_dcl_statement', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__support_multiple_grantees_per_dcl_statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9882164, "supported_languages": null}, "macro.dbt.default__support_multiple_grantees_per_dcl_statement": {"name": "default__support_multiple_grantees_per_dcl_statement", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__support_multiple_grantees_per_dcl_statement", "macro_sql": "\n\n{%- macro default__support_multiple_grantees_per_dcl_statement() -%}\n    {{ return(True) }}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.988432, "supported_languages": null}, "macro.dbt.should_revoke": {"name": "should_revoke", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.should_revoke", "macro_sql": "{% macro should_revoke(existing_relation, full_refresh_mode=True) %}\n\n    {% if not existing_relation %}\n        {#-- The table doesn't already exist, so no grants to copy over --#}\n        {{ return(False) }}\n    {% elif full_refresh_mode %}\n        {#-- The object is being REPLACED -- whether grants are copied over depends on the value of user config --#}\n        {{ return(copy_grants()) }}\n    {% else %}\n        {#-- The table is being merged/upserted/inserted -- grants will be carried over --#}\n        {{ return(True) }}\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.copy_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.989075, "supported_languages": null}, "macro.dbt.get_show_grant_sql": {"name": "get_show_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.get_show_grant_sql", "macro_sql": "{% macro get_show_grant_sql(relation) %}\n    {{ return(adapter.dispatch(\"get_show_grant_sql\", \"dbt\")(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_show_grant_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.989429, "supported_languages": null}, "macro.dbt.default__get_show_grant_sql": {"name": "default__get_show_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__get_show_grant_sql", "macro_sql": "{% macro default__get_show_grant_sql(relation) %}\n    show grants on {{ relation.render() }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.989664, "supported_languages": null}, "macro.dbt.get_grant_sql": {"name": "get_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.get_grant_sql", "macro_sql": "{% macro get_grant_sql(relation, privilege, grantees) %}\n    {{ return(adapter.dispatch('get_grant_sql', 'dbt')(relation, privilege, grantees)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_grant_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9900715, "supported_languages": null}, "macro.dbt.default__get_grant_sql": {"name": "default__get_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__get_grant_sql", "macro_sql": "\n\n{%- macro default__get_grant_sql(relation, privilege, grantees) -%}\n    grant {{ privilege }} on {{ relation.render() }} to {{ grantees | join(', ') }}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9904923, "supported_languages": null}, "macro.dbt.get_revoke_sql": {"name": "get_revoke_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.get_revoke_sql", "macro_sql": "{% macro get_revoke_sql(relation, privilege, grantees) %}\n    {{ return(adapter.dispatch('get_revoke_sql', 'dbt')(relation, privilege, grantees)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_revoke_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9909182, "supported_languages": null}, "macro.dbt.default__get_revoke_sql": {"name": "default__get_revoke_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__get_revoke_sql", "macro_sql": "\n\n{%- macro default__get_revoke_sql(relation, privilege, grantees) -%}\n    revoke {{ privilege }} on {{ relation.render() }} from {{ grantees | join(', ') }}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9913037, "supported_languages": null}, "macro.dbt.get_dcl_statement_list": {"name": "get_dcl_statement_list", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.get_dcl_statement_list", "macro_sql": "{% macro get_dcl_statement_list(relation, grant_config, get_dcl_macro) %}\n    {{ return(adapter.dispatch('get_dcl_statement_list', 'dbt')(relation, grant_config, get_dcl_macro)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_dcl_statement_list"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.991732, "supported_languages": null}, "macro.dbt.default__get_dcl_statement_list": {"name": "default__get_dcl_statement_list", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__get_dcl_statement_list", "macro_sql": "\n\n{%- macro default__get_dcl_statement_list(relation, grant_config, get_dcl_macro) -%}\n    {#\n      -- Unpack grant_config into specific privileges and the set of users who need them granted/revoked.\n      -- Depending on whether this database supports multiple grantees per statement, pass in the list of\n      -- all grantees per privilege, or (if not) template one statement per privilege-grantee pair.\n      -- `get_dcl_macro` will be either `get_grant_sql` or `get_revoke_sql`\n    #}\n    {%- set dcl_statements = [] -%}\n    {%- for privilege, grantees in grant_config.items() %}\n        {%- if support_multiple_grantees_per_dcl_statement() and grantees -%}\n          {%- set dcl = get_dcl_macro(relation, privilege, grantees) -%}\n          {%- do dcl_statements.append(dcl) -%}\n        {%- else -%}\n          {%- for grantee in grantees -%}\n              {% set dcl = get_dcl_macro(relation, privilege, [grantee]) %}\n              {%- do dcl_statements.append(dcl) -%}\n          {% endfor -%}\n        {%- endif -%}\n    {%- endfor -%}\n    {{ return(dcl_statements) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.support_multiple_grantees_per_dcl_statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9930406, "supported_languages": null}, "macro.dbt.call_dcl_statements": {"name": "call_dcl_statements", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.call_dcl_statements", "macro_sql": "{% macro call_dcl_statements(dcl_statement_list) %}\n    {{ return(adapter.dispatch(\"call_dcl_statements\", \"dbt\")(dcl_statement_list)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__call_dcl_statements"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9933999, "supported_languages": null}, "macro.dbt.default__call_dcl_statements": {"name": "default__call_dcl_statements", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__call_dcl_statements", "macro_sql": "{% macro default__call_dcl_statements(dcl_statement_list) %}\n    {#\n      -- By default, supply all grant + revoke statements in a single semicolon-separated block,\n      -- so that they're all processed together.\n\n      -- Some databases do not support this. Those adapters will need to override this macro\n      -- to run each statement individually.\n    #}\n    {% call statement('grants') %}\n        {% for dcl_statement in dcl_statement_list %}\n            {{ dcl_statement }};\n        {% endfor %}\n    {% endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9938595, "supported_languages": null}, "macro.dbt.apply_grants": {"name": "apply_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.apply_grants", "macro_sql": "{% macro apply_grants(relation, grant_config, should_revoke) %}\n    {{ return(adapter.dispatch(\"apply_grants\", \"dbt\")(relation, grant_config, should_revoke)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__apply_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9942765, "supported_languages": null}, "macro.dbt.default__apply_grants": {"name": "default__apply_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/apply_grants.sql", "original_file_path": "macros/adapters/apply_grants.sql", "unique_id": "macro.dbt.default__apply_grants", "macro_sql": "{% macro default__apply_grants(relation, grant_config, should_revoke=True) %}\n    {#-- If grant_config is {} or None, this is a no-op --#}\n    {% if grant_config %}\n        {% if should_revoke %}\n            {#-- We think previous grants may have carried over --#}\n            {#-- Show current grants and calculate diffs --#}\n            {% set current_grants_table = run_query(get_show_grant_sql(relation)) %}\n            {% set current_grants_dict = adapter.standardize_grants_dict(current_grants_table) %}\n            {% set needs_granting = diff_of_two_dicts(grant_config, current_grants_dict) %}\n            {% set needs_revoking = diff_of_two_dicts(current_grants_dict, grant_config) %}\n            {% if not (needs_granting or needs_revoking) %}\n                {{ log('On ' ~ relation.render() ~': All grants are in place, no revocation or granting needed.')}}\n            {% endif %}\n        {% else %}\n            {#-- We don't think there's any chance of previous grants having carried over. --#}\n            {#-- Jump straight to granting what the user has configured. --#}\n            {% set needs_revoking = {} %}\n            {% set needs_granting = grant_config %}\n        {% endif %}\n        {% if needs_granting or needs_revoking %}\n            {% set revoke_statement_list = get_dcl_statement_list(relation, needs_revoking, get_revoke_sql) %}\n            {% set grant_statement_list = get_dcl_statement_list(relation, needs_granting, get_grant_sql) %}\n            {% set dcl_statement_list = revoke_statement_list + grant_statement_list %}\n            {% if dcl_statement_list %}\n                {{ call_dcl_statements(dcl_statement_list) }}\n            {% endif %}\n        {% endif %}\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query", "macro.dbt.get_show_grant_sql", "macro.dbt.get_dcl_statement_list", "macro.dbt.call_dcl_statements"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9963076, "supported_languages": null}, "macro.dbt.get_columns_in_relation": {"name": "get_columns_in_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.get_columns_in_relation", "macro_sql": "{% macro get_columns_in_relation(relation) -%}\n  {{ return(adapter.dispatch('get_columns_in_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_columns_in_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.998278, "supported_languages": null}, "macro.dbt.default__get_columns_in_relation": {"name": "default__get_columns_in_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.default__get_columns_in_relation", "macro_sql": "{% macro default__get_columns_in_relation(relation) -%}\n  {{ exceptions.raise_not_implemented(\n    'get_columns_in_relation macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9985776, "supported_languages": null}, "macro.dbt.sql_convert_columns_in_relation": {"name": "sql_convert_columns_in_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.sql_convert_columns_in_relation", "macro_sql": "{% macro sql_convert_columns_in_relation(table) -%}\n  {% set columns = [] %}\n  {% for row in table %}\n    {% do columns.append(api.Column(*row)) %}\n  {% endfor %}\n  {{ return(columns) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9991796, "supported_languages": null}, "macro.dbt.get_list_of_column_names": {"name": "get_list_of_column_names", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.get_list_of_column_names", "macro_sql": "\n\n{%- macro get_list_of_column_names(columns) -%}\n  {% set col_names = [] %}\n  {% for col in columns %}\n    {% do col_names.append(col.name) %}\n  {% endfor %}\n  {{ return(col_names) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938913.9997346, "supported_languages": null}, "macro.dbt.get_empty_subquery_sql": {"name": "get_empty_subquery_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.get_empty_subquery_sql", "macro_sql": "{% macro get_empty_subquery_sql(select_sql, select_sql_header=none) -%}\n  {{ return(adapter.dispatch('get_empty_subquery_sql', 'dbt')(select_sql, select_sql_header)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_empty_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0001345, "supported_languages": null}, "macro.dbt.default__get_empty_subquery_sql": {"name": "default__get_empty_subquery_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.default__get_empty_subquery_sql", "macro_sql": "{% macro default__get_empty_subquery_sql(select_sql, select_sql_header=none) %}\n    {%- if select_sql_header is not none -%}\n    {{ select_sql_header }}\n    {%- endif -%}\n    select * from (\n        {{ select_sql }}\n    ) as __dbt_sbq\n    where false\n    limit 0\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.000514, "supported_languages": null}, "macro.dbt.get_empty_schema_sql": {"name": "get_empty_schema_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.get_empty_schema_sql", "macro_sql": "{% macro get_empty_schema_sql(columns) -%}\n  {{ return(adapter.dispatch('get_empty_schema_sql', 'dbt')(columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_empty_schema_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0008476, "supported_languages": null}, "macro.dbt.default__get_empty_schema_sql": {"name": "default__get_empty_schema_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.default__get_empty_schema_sql", "macro_sql": "{% macro default__get_empty_schema_sql(columns) %}\n    {%- set col_err = [] -%}\n    {%- set col_naked_numeric = [] -%}\n    select\n    {% for i in columns %}\n      {%- set col = columns[i] -%}\n      {%- if col['data_type'] is not defined -%}\n        {%- do col_err.append(col['name']) -%}\n      {#-- If this column's type is just 'numeric' then it is missing precision/scale, raise a warning --#}\n      {%- elif col['data_type'].strip().lower() in ('numeric', 'decimal', 'number') -%}\n        {%- do col_naked_numeric.append(col['name']) -%}\n      {%- endif -%}\n      {% set col_name = adapter.quote(col['name']) if col.get('quote') else col['name'] %}\n      {{ cast('null', col['data_type']) }} as {{ col_name }}{{ \", \" if not loop.last }}\n    {%- endfor -%}\n    {%- if (col_err | length) > 0 -%}\n      {{ exceptions.column_type_missing(column_names=col_err) }}\n    {%- elif (col_naked_numeric | length) > 0 -%}\n      {{ exceptions.warn(\"Detected columns with numeric type and unspecified precision/scale, this can lead to unintended rounding: \" ~ col_naked_numeric ~ \"`\") }}\n    {%- endif -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0028746, "supported_languages": null}, "macro.dbt.get_column_schema_from_query": {"name": "get_column_schema_from_query", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.get_column_schema_from_query", "macro_sql": "{% macro get_column_schema_from_query(select_sql, select_sql_header=none) -%}\n    {% set columns = [] %}\n    {# -- Using an 'empty subquery' here to get the same schema as the given select_sql statement, without necessitating a data scan.#}\n    {% set sql = get_empty_subquery_sql(select_sql, select_sql_header) %}\n    {% set column_schema = adapter.get_column_schema_from_query(sql) %}\n    {{ return(column_schema) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_empty_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.003523, "supported_languages": null}, "macro.dbt.get_columns_in_query": {"name": "get_columns_in_query", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.get_columns_in_query", "macro_sql": "{% macro get_columns_in_query(select_sql) -%}\n  {{ return(adapter.dispatch('get_columns_in_query', 'dbt')(select_sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_columns_in_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0038643, "supported_languages": null}, "macro.dbt.default__get_columns_in_query": {"name": "default__get_columns_in_query", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.default__get_columns_in_query", "macro_sql": "{% macro default__get_columns_in_query(select_sql) %}\n    {% call statement('get_columns_in_query', fetch_result=True, auto_begin=False) -%}\n        {{ get_empty_subquery_sql(select_sql) }}\n    {% endcall %}\n    {{ return(load_result('get_columns_in_query').table.columns | map(attribute='name') | list) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.get_empty_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0044763, "supported_languages": null}, "macro.dbt.alter_column_type": {"name": "alter_column_type", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.alter_column_type", "macro_sql": "{% macro alter_column_type(relation, column_name, new_column_type) -%}\n  {{ return(adapter.dispatch('alter_column_type', 'dbt')(relation, column_name, new_column_type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__alter_column_type"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0048885, "supported_languages": null}, "macro.dbt.default__alter_column_type": {"name": "default__alter_column_type", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.default__alter_column_type", "macro_sql": "{% macro default__alter_column_type(relation, column_name, new_column_type) -%}\n  {#\n    1. Create a new column (w/ temp name and correct type)\n    2. Copy data over to it\n    3. Drop the existing column (cascade!)\n    4. Rename the new column to existing column\n  #}\n  {%- set tmp_column = column_name + \"__dbt_alter\" -%}\n\n  {% call statement('alter_column_type') %}\n    alter table {{ relation.render() }} add column {{ adapter.quote(tmp_column) }} {{ new_column_type }};\n    update {{ relation.render() }} set {{ adapter.quote(tmp_column) }} = {{ adapter.quote(column_name) }};\n    alter table {{ relation.render() }} drop column {{ adapter.quote(column_name) }} cascade;\n    alter table {{ relation.render() }} rename column {{ adapter.quote(tmp_column) }} to {{ adapter.quote(column_name) }}\n  {% endcall %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.006146, "supported_languages": null}, "macro.dbt.alter_relation_add_remove_columns": {"name": "alter_relation_add_remove_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.alter_relation_add_remove_columns", "macro_sql": "{% macro alter_relation_add_remove_columns(relation, add_columns = none, remove_columns = none) -%}\n  {{ return(adapter.dispatch('alter_relation_add_remove_columns', 'dbt')(relation, add_columns, remove_columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__alter_relation_add_remove_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0066173, "supported_languages": null}, "macro.dbt.default__alter_relation_add_remove_columns": {"name": "default__alter_relation_add_remove_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/columns.sql", "original_file_path": "macros/adapters/columns.sql", "unique_id": "macro.dbt.default__alter_relation_add_remove_columns", "macro_sql": "{% macro default__alter_relation_add_remove_columns(relation, add_columns, remove_columns) %}\n\n  {% if add_columns is none %}\n    {% set add_columns = [] %}\n  {% endif %}\n  {% if remove_columns is none %}\n    {% set remove_columns = [] %}\n  {% endif %}\n\n  {% set sql -%}\n\n     alter {{ relation.type }} {{ relation.render() }}\n\n            {% for column in add_columns %}\n               add column {{ column.name }} {{ column.data_type }}{{ ',' if not loop.last }}\n            {% endfor %}{{ ',' if add_columns and remove_columns }}\n\n            {% for column in remove_columns %}\n                drop column {{ column.name }}{{ ',' if not loop.last }}\n            {% endfor %}\n\n  {%- endset -%}\n\n  {% do run_query(sql) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0080435, "supported_languages": null}, "macro.dbt.collect_freshness": {"name": "collect_freshness", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/freshness.sql", "original_file_path": "macros/adapters/freshness.sql", "unique_id": "macro.dbt.collect_freshness", "macro_sql": "{% macro collect_freshness(source, loaded_at_field, filter) %}\n  {{ return(adapter.dispatch('collect_freshness', 'dbt')(source, loaded_at_field, filter))}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__collect_freshness"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0088544, "supported_languages": null}, "macro.dbt.default__collect_freshness": {"name": "default__collect_freshness", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/freshness.sql", "original_file_path": "macros/adapters/freshness.sql", "unique_id": "macro.dbt.default__collect_freshness", "macro_sql": "{% macro default__collect_freshness(source, loaded_at_field, filter) %}\n  {% call statement('collect_freshness', fetch_result=True, auto_begin=False) -%}\n    select\n      max({{ loaded_at_field }}) as max_loaded_at,\n      {{ current_timestamp() }} as snapshotted_at\n    from {{ source }}\n    {% if filter %}\n    where {{ filter }}\n    {% endif %}\n  {% endcall %}\n  {{ return(load_result('collect_freshness')) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.00963, "supported_languages": null}, "macro.dbt.collect_freshness_custom_sql": {"name": "collect_freshness_custom_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/freshness.sql", "original_file_path": "macros/adapters/freshness.sql", "unique_id": "macro.dbt.collect_freshness_custom_sql", "macro_sql": "{% macro collect_freshness_custom_sql(source, loaded_at_query) %}\n  {{ return(adapter.dispatch('collect_freshness_custom_sql', 'dbt')(source, loaded_at_query))}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__collect_freshness_custom_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.010023, "supported_languages": null}, "macro.dbt.default__collect_freshness_custom_sql": {"name": "default__collect_freshness_custom_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/freshness.sql", "original_file_path": "macros/adapters/freshness.sql", "unique_id": "macro.dbt.default__collect_freshness_custom_sql", "macro_sql": "{% macro default__collect_freshness_custom_sql(source, loaded_at_query) %}\n  {% call statement('collect_freshness_custom_sql', fetch_result=True, auto_begin=False) -%}\n  with source_query as (\n    {{ loaded_at_query }}\n  )\n  select\n    (select * from source_query) as max_loaded_at,\n    {{ current_timestamp() }} as snapshotted_at\n  {% endcall %}\n  {{ return(load_result('collect_freshness_custom_sql')) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0105913, "supported_languages": null}, "macro.dbt.get_create_index_sql": {"name": "get_create_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.get_create_index_sql", "macro_sql": "{% macro get_create_index_sql(relation, index_dict) -%}\n  {{ return(adapter.dispatch('get_create_index_sql', 'dbt')(relation, index_dict)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_create_index_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0115774, "supported_languages": null}, "macro.dbt.default__get_create_index_sql": {"name": "default__get_create_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.default__get_create_index_sql", "macro_sql": "{% macro default__get_create_index_sql(relation, index_dict) -%}\n  {% do return(None) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.011849, "supported_languages": null}, "macro.dbt.create_indexes": {"name": "create_indexes", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.create_indexes", "macro_sql": "{% macro create_indexes(relation) -%}\n  {{ adapter.dispatch('create_indexes', 'dbt')(relation) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_indexes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.012236, "supported_languages": null}, "macro.dbt.default__create_indexes": {"name": "default__create_indexes", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.default__create_indexes", "macro_sql": "{% macro default__create_indexes(relation) -%}\n  {%- set _indexes = config.get('indexes', default=[]) -%}\n\n  {% for _index_dict in _indexes %}\n    {% set create_index_sql = get_create_index_sql(relation, _index_dict) %}\n    {% if create_index_sql %}\n      {% do run_query(create_index_sql) %}\n    {% endif %}\n  {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_create_index_sql", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.012978, "supported_languages": null}, "macro.dbt.get_drop_index_sql": {"name": "get_drop_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.get_drop_index_sql", "macro_sql": "{% macro get_drop_index_sql(relation, index_name) -%}\n    {{ adapter.dispatch('get_drop_index_sql', 'dbt')(relation, index_name) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_drop_index_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0133264, "supported_languages": null}, "macro.dbt.default__get_drop_index_sql": {"name": "default__get_drop_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.default__get_drop_index_sql", "macro_sql": "{% macro default__get_drop_index_sql(relation, index_name) -%}\n    {{ exceptions.raise_compiler_error(\"`get_drop_index_sql has not been implemented for this adapter.\") }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0135868, "supported_languages": null}, "macro.dbt.get_show_indexes_sql": {"name": "get_show_indexes_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.get_show_indexes_sql", "macro_sql": "{% macro get_show_indexes_sql(relation) -%}\n    {{ adapter.dispatch('get_show_indexes_sql', 'dbt')(relation) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_show_indexes_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0138805, "supported_languages": null}, "macro.dbt.default__get_show_indexes_sql": {"name": "default__get_show_indexes_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/indexes.sql", "original_file_path": "macros/adapters/indexes.sql", "unique_id": "macro.dbt.default__get_show_indexes_sql", "macro_sql": "{% macro default__get_show_indexes_sql(relation) -%}\n    {{ exceptions.raise_compiler_error(\"`get_show_indexes_sql has not been implemented for this adapter.\") }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0141237, "supported_languages": null}, "macro.dbt.get_catalog_relations": {"name": "get_catalog_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.get_catalog_relations", "macro_sql": "{% macro get_catalog_relations(information_schema, relations) -%}\n  {{ return(adapter.dispatch('get_catalog_relations', 'dbt')(information_schema, relations)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_catalog_relations"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0155623, "supported_languages": null}, "macro.dbt.default__get_catalog_relations": {"name": "default__get_catalog_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__get_catalog_relations", "macro_sql": "{% macro default__get_catalog_relations(information_schema, relations) -%}\n  {% set typename = adapter.type() %}\n  {% set msg -%}\n    get_catalog_relations not implemented for {{ typename }}\n  {%- endset %}\n\n  {{ exceptions.raise_compiler_error(msg) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0160453, "supported_languages": null}, "macro.dbt.get_catalog": {"name": "get_catalog", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.get_catalog", "macro_sql": "{% macro get_catalog(information_schema, schemas) -%}\n  {{ return(adapter.dispatch('get_catalog', 'dbt')(information_schema, schemas)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_catalog"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0164165, "supported_languages": null}, "macro.dbt.default__get_catalog": {"name": "default__get_catalog", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__get_catalog", "macro_sql": "{% macro default__get_catalog(information_schema, schemas) -%}\n\n  {% set typename = adapter.type() %}\n  {% set msg -%}\n    get_catalog not implemented for {{ typename }}\n  {%- endset %}\n\n  {{ exceptions.raise_compiler_error(msg) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0168939, "supported_languages": null}, "macro.dbt.information_schema_name": {"name": "information_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.information_schema_name", "macro_sql": "{% macro information_schema_name(database) %}\n  {{ return(adapter.dispatch('information_schema_name', 'dbt')(database)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__information_schema_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0172331, "supported_languages": null}, "macro.dbt.default__information_schema_name": {"name": "default__information_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__information_schema_name", "macro_sql": "{% macro default__information_schema_name(database) -%}\n  {%- if database -%}\n    {{ database }}.INFORMATION_SCHEMA\n  {%- else -%}\n    INFORMATION_SCHEMA\n  {%- endif -%}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0176437, "supported_languages": null}, "macro.dbt.list_schemas": {"name": "list_schemas", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.list_schemas", "macro_sql": "{% macro list_schemas(database) -%}\n  {{ return(adapter.dispatch('list_schemas', 'dbt')(database)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__list_schemas"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0179868, "supported_languages": null}, "macro.dbt.default__list_schemas": {"name": "default__list_schemas", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__list_schemas", "macro_sql": "{% macro default__list_schemas(database) -%}\n  {% set sql %}\n    select distinct schema_name\n    from {{ information_schema_name(database) }}.SCHEMATA\n    where catalog_name ilike '{{ database }}'\n  {% endset %}\n  {{ return(run_query(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.information_schema_name", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0184374, "supported_languages": null}, "macro.dbt.check_schema_exists": {"name": "check_schema_exists", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.check_schema_exists", "macro_sql": "{% macro check_schema_exists(information_schema, schema) -%}\n  {{ return(adapter.dispatch('check_schema_exists', 'dbt')(information_schema, schema)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__check_schema_exists"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0188105, "supported_languages": null}, "macro.dbt.default__check_schema_exists": {"name": "default__check_schema_exists", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__check_schema_exists", "macro_sql": "{% macro default__check_schema_exists(information_schema, schema) -%}\n  {% set sql -%}\n        select count(*)\n        from {{ information_schema.replace(information_schema_view='SCHEMATA') }}\n        where catalog_name='{{ information_schema.database }}'\n          and schema_name='{{ schema }}'\n  {%- endset %}\n  {{ return(run_query(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.replace", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0193887, "supported_languages": null}, "macro.dbt.list_relations_without_caching": {"name": "list_relations_without_caching", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.list_relations_without_caching", "macro_sql": "{% macro list_relations_without_caching(schema_relation) %}\n  {{ return(adapter.dispatch('list_relations_without_caching', 'dbt')(schema_relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__list_relations_without_caching"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0197465, "supported_languages": null}, "macro.dbt.default__list_relations_without_caching": {"name": "default__list_relations_without_caching", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__list_relations_without_caching", "macro_sql": "{% macro default__list_relations_without_caching(schema_relation) %}\n  {{ exceptions.raise_not_implemented(\n    'list_relations_without_caching macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0200508, "supported_languages": null}, "macro.dbt.get_catalog_for_single_relation": {"name": "get_catalog_for_single_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.get_catalog_for_single_relation", "macro_sql": "{% macro get_catalog_for_single_relation(relation) %}\n  {{ return(adapter.dispatch('get_catalog_for_single_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_catalog_for_single_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0203905, "supported_languages": null}, "macro.dbt.default__get_catalog_for_single_relation": {"name": "default__get_catalog_for_single_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__get_catalog_for_single_relation", "macro_sql": "{% macro default__get_catalog_for_single_relation(relation) %}\n  {{ exceptions.raise_not_implemented(\n    'get_catalog_for_single_relation macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0206912, "supported_languages": null}, "macro.dbt.get_relations": {"name": "get_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.get_relations", "macro_sql": "{% macro get_relations() %}\n  {{ return(adapter.dispatch('get_relations', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_relations"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0209997, "supported_languages": null}, "macro.dbt.default__get_relations": {"name": "default__get_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__get_relations", "macro_sql": "{% macro default__get_relations() %}\n  {{ exceptions.raise_not_implemented(\n    'get_relations macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.021284, "supported_languages": null}, "macro.dbt.get_relation_last_modified": {"name": "get_relation_last_modified", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.get_relation_last_modified", "macro_sql": "{% macro get_relation_last_modified(information_schema, relations) %}\n  {{ return(adapter.dispatch('get_relation_last_modified', 'dbt')(information_schema, relations)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_relation_last_modified"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.02166, "supported_languages": null}, "macro.dbt.default__get_relation_last_modified": {"name": "default__get_relation_last_modified", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/metadata.sql", "original_file_path": "macros/adapters/metadata.sql", "unique_id": "macro.dbt.default__get_relation_last_modified", "macro_sql": "{% macro default__get_relation_last_modified(information_schema, relations) %}\n  {{ exceptions.raise_not_implemented(\n    'get_relation_last_modified macro not implemented for adapter ' + adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0219781, "supported_languages": null}, "macro.dbt.alter_column_comment": {"name": "alter_column_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/persist_docs.sql", "original_file_path": "macros/adapters/persist_docs.sql", "unique_id": "macro.dbt.alter_column_comment", "macro_sql": "{% macro alter_column_comment(relation, column_dict) -%}\n  {{ return(adapter.dispatch('alter_column_comment', 'dbt')(relation, column_dict)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__alter_column_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0227447, "supported_languages": null}, "macro.dbt.default__alter_column_comment": {"name": "default__alter_column_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/persist_docs.sql", "original_file_path": "macros/adapters/persist_docs.sql", "unique_id": "macro.dbt.default__alter_column_comment", "macro_sql": "{% macro default__alter_column_comment(relation, column_dict) -%}\n  {{ exceptions.raise_not_implemented(\n    'alter_column_comment macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.023059, "supported_languages": null}, "macro.dbt.alter_relation_comment": {"name": "alter_relation_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/persist_docs.sql", "original_file_path": "macros/adapters/persist_docs.sql", "unique_id": "macro.dbt.alter_relation_comment", "macro_sql": "{% macro alter_relation_comment(relation, relation_comment) -%}\n  {{ return(adapter.dispatch('alter_relation_comment', 'dbt')(relation, relation_comment)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__alter_relation_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0234308, "supported_languages": null}, "macro.dbt.default__alter_relation_comment": {"name": "default__alter_relation_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/persist_docs.sql", "original_file_path": "macros/adapters/persist_docs.sql", "unique_id": "macro.dbt.default__alter_relation_comment", "macro_sql": "{% macro default__alter_relation_comment(relation, relation_comment) -%}\n  {{ exceptions.raise_not_implemented(\n    'alter_relation_comment macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0237556, "supported_languages": null}, "macro.dbt.persist_docs": {"name": "persist_docs", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/persist_docs.sql", "original_file_path": "macros/adapters/persist_docs.sql", "unique_id": "macro.dbt.persist_docs", "macro_sql": "{% macro persist_docs(relation, model, for_relation=true, for_columns=true) -%}\n  {{ return(adapter.dispatch('persist_docs', 'dbt')(relation, model, for_relation, for_columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0242386, "supported_languages": null}, "macro.dbt.default__persist_docs": {"name": "default__persist_docs", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/persist_docs.sql", "original_file_path": "macros/adapters/persist_docs.sql", "unique_id": "macro.dbt.default__persist_docs", "macro_sql": "{% macro default__persist_docs(relation, model, for_relation, for_columns) -%}\n  {% if for_relation and config.persist_relation_docs() and model.description %}\n    {% do run_query(alter_relation_comment(relation, model.description)) %}\n  {% endif %}\n\n  {% if for_columns and config.persist_column_docs() and model.columns %}\n    {% do run_query(alter_column_comment(relation, model.columns)) %}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query", "macro.dbt.alter_relation_comment", "macro.dbt.alter_column_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.025185, "supported_languages": null}, "macro.dbt.make_intermediate_relation": {"name": "make_intermediate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.make_intermediate_relation", "macro_sql": "{% macro make_intermediate_relation(base_relation, suffix='__dbt_tmp') %}\n  {{ return(adapter.dispatch('make_intermediate_relation', 'dbt')(base_relation, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__make_intermediate_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.026367, "supported_languages": null}, "macro.dbt.default__make_intermediate_relation": {"name": "default__make_intermediate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.default__make_intermediate_relation", "macro_sql": "{% macro default__make_intermediate_relation(base_relation, suffix) %}\n    {{ return(default__make_temp_relation(base_relation, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__make_temp_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0266764, "supported_languages": null}, "macro.dbt.make_temp_relation": {"name": "make_temp_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.make_temp_relation", "macro_sql": "{% macro make_temp_relation(base_relation, suffix='__dbt_tmp') %}\n  {#-- This ensures microbatch batches get unique temp relations to avoid clobbering --#}\n  {% if suffix == '__dbt_tmp' and model.batch %}\n    {% set suffix = suffix ~ '_' ~ model.batch.id %}\n  {% endif %}\n\n  {{ return(adapter.dispatch('make_temp_relation', 'dbt')(base_relation, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__make_temp_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.027364, "supported_languages": null}, "macro.dbt.default__make_temp_relation": {"name": "default__make_temp_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.default__make_temp_relation", "macro_sql": "{% macro default__make_temp_relation(base_relation, suffix) %}\n    {%- set temp_identifier = base_relation.identifier ~ suffix -%}\n    {%- set temp_relation = base_relation.incorporate(\n                                path={\"identifier\": temp_identifier}) -%}\n\n    {{ return(temp_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0279095, "supported_languages": null}, "macro.dbt.make_backup_relation": {"name": "make_backup_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.make_backup_relation", "macro_sql": "{% macro make_backup_relation(base_relation, backup_relation_type, suffix='__dbt_backup') %}\n    {{ return(adapter.dispatch('make_backup_relation', 'dbt')(base_relation, backup_relation_type, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__make_backup_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0283544, "supported_languages": null}, "macro.dbt.default__make_backup_relation": {"name": "default__make_backup_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.default__make_backup_relation", "macro_sql": "{% macro default__make_backup_relation(base_relation, backup_relation_type, suffix) %}\n    {%- set backup_identifier = base_relation.identifier ~ suffix -%}\n    {%- set backup_relation = base_relation.incorporate(\n                                  path={\"identifier\": backup_identifier},\n                                  type=backup_relation_type\n    ) -%}\n    {{ return(backup_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0289326, "supported_languages": null}, "macro.dbt.truncate_relation": {"name": "truncate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.truncate_relation", "macro_sql": "{% macro truncate_relation(relation) -%}\n  {{ return(adapter.dispatch('truncate_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__truncate_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0293095, "supported_languages": null}, "macro.dbt.default__truncate_relation": {"name": "default__truncate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.default__truncate_relation", "macro_sql": "{% macro default__truncate_relation(relation) -%}\n  {% call statement('truncate_relation') -%}\n    truncate table {{ relation.render() }}\n  {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0296571, "supported_languages": null}, "macro.dbt.get_or_create_relation": {"name": "get_or_create_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.get_or_create_relation", "macro_sql": "{% macro get_or_create_relation(database, schema, identifier, type) -%}\n  {{ return(adapter.dispatch('get_or_create_relation', 'dbt')(database, schema, identifier, type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_or_create_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0301044, "supported_languages": null}, "macro.dbt.default__get_or_create_relation": {"name": "default__get_or_create_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.default__get_or_create_relation", "macro_sql": "{% macro default__get_or_create_relation(database, schema, identifier, type) %}\n  {%- set target_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) %}\n\n  {% if target_relation %}\n    {% do return([true, target_relation]) %}\n  {% endif %}\n\n  {%- set new_relation = api.Relation.create(\n      database=database,\n      schema=schema,\n      identifier=identifier,\n      type=type\n  ) -%}\n  {% do return([false, new_relation]) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0310726, "supported_languages": null}, "macro.dbt.load_cached_relation": {"name": "load_cached_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.load_cached_relation", "macro_sql": "{% macro load_cached_relation(relation) %}\n  {% do return(adapter.get_relation(\n    database=relation.database,\n    schema=relation.schema,\n    identifier=relation.identifier\n  )) -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0314987, "supported_languages": null}, "macro.dbt.load_relation": {"name": "load_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/relation.sql", "original_file_path": "macros/adapters/relation.sql", "unique_id": "macro.dbt.load_relation", "macro_sql": "{% macro load_relation(relation) %}\n    {{ return(load_cached_relation(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0317752, "supported_languages": null}, "macro.dbt.create_schema": {"name": "create_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/schema.sql", "original_file_path": "macros/adapters/schema.sql", "unique_id": "macro.dbt.create_schema", "macro_sql": "{% macro create_schema(relation) -%}\n  {{ adapter.dispatch('create_schema', 'dbt')(relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__create_schema"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0323508, "supported_languages": null}, "macro.dbt.default__create_schema": {"name": "default__create_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/schema.sql", "original_file_path": "macros/adapters/schema.sql", "unique_id": "macro.dbt.default__create_schema", "macro_sql": "{% macro default__create_schema(relation) -%}\n  {%- call statement('create_schema') -%}\n    create schema if not exists {{ relation.without_identifier() }}\n  {% endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0327017, "supported_languages": null}, "macro.dbt.drop_schema": {"name": "drop_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/schema.sql", "original_file_path": "macros/adapters/schema.sql", "unique_id": "macro.dbt.drop_schema", "macro_sql": "{% macro drop_schema(relation) -%}\n  {{ adapter.dispatch('drop_schema', 'dbt')(relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__drop_schema"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0330052, "supported_languages": null}, "macro.dbt.default__drop_schema": {"name": "default__drop_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/schema.sql", "original_file_path": "macros/adapters/schema.sql", "unique_id": "macro.dbt.default__drop_schema", "macro_sql": "{% macro default__drop_schema(relation) -%}\n  {%- call statement('drop_schema') -%}\n    drop schema if exists {{ relation.without_identifier() }} cascade\n  {% endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0333474, "supported_languages": null}, "macro.dbt.get_show_sql": {"name": "get_show_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/show.sql", "original_file_path": "macros/adapters/show.sql", "unique_id": "macro.dbt.get_show_sql", "macro_sql": "{% macro get_show_sql(compiled_code, sql_header, limit) -%}\n  {%- if sql_header is not none -%}\n  {{ sql_header }}\n  {%- endif %}\n  {{ get_limit_subquery_sql(compiled_code, limit) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_limit_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0340343, "supported_languages": null}, "macro.dbt.get_limit_subquery_sql": {"name": "get_limit_subquery_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/show.sql", "original_file_path": "macros/adapters/show.sql", "unique_id": "macro.dbt.get_limit_subquery_sql", "macro_sql": "\n{%- macro get_limit_subquery_sql(sql, limit) -%}\n  {{ adapter.dispatch('get_limit_sql', 'dbt')(sql, limit) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__get_limit_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.034382, "supported_languages": null}, "macro.dbt.default__get_limit_sql": {"name": "default__get_limit_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/show.sql", "original_file_path": "macros/adapters/show.sql", "unique_id": "macro.dbt.default__get_limit_sql", "macro_sql": "{% macro default__get_limit_sql(sql, limit) %}\n  {{ sql }}\n  {% if limit is not none %}\n  limit {{ limit }}\n  {%- endif -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.034838, "supported_languages": null}, "macro.dbt.current_timestamp": {"name": "current_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.current_timestamp", "macro_sql": "{%- macro current_timestamp() -%}\n    {{ adapter.dispatch('current_timestamp', 'dbt')() }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt_postgres.postgres__current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0356627, "supported_languages": null}, "macro.dbt.default__current_timestamp": {"name": "default__current_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.default__current_timestamp", "macro_sql": "{% macro default__current_timestamp() -%}\n  {{ exceptions.raise_not_implemented(\n    'current_timestamp macro not implemented for adapter ' + adapter.type()) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.035947, "supported_languages": null}, "macro.dbt.snapshot_get_time": {"name": "snapshot_get_time", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.snapshot_get_time", "macro_sql": "\n\n{%- macro snapshot_get_time() -%}\n    {{ adapter.dispatch('snapshot_get_time', 'dbt')() }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt_postgres.postgres__snapshot_get_time"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0362175, "supported_languages": null}, "macro.dbt.default__snapshot_get_time": {"name": "default__snapshot_get_time", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.default__snapshot_get_time", "macro_sql": "{% macro default__snapshot_get_time() %}\n    {{ current_timestamp() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0364232, "supported_languages": null}, "macro.dbt.get_snapshot_get_time_data_type": {"name": "get_snapshot_get_time_data_type", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.get_snapshot_get_time_data_type", "macro_sql": "{% macro get_snapshot_get_time_data_type() %}\n    {% set snapshot_time = adapter.dispatch('snapshot_get_time', 'dbt')() %}\n    {% set time_data_type_sql = 'select ' ~ snapshot_time ~ ' as dbt_snapshot_time' %}\n    {% set snapshot_time_column_schema = get_column_schema_from_query(time_data_type_sql) %}\n    {% set time_data_type = snapshot_time_column_schema[0].dtype %}\n    {{ return(time_data_type or none) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.snapshot_get_time", "macro.dbt_postgres.postgres__snapshot_get_time", "macro.dbt.get_column_schema_from_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.037169, "supported_languages": null}, "macro.dbt.current_timestamp_backcompat": {"name": "current_timestamp_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.current_timestamp_backcompat", "macro_sql": "{% macro current_timestamp_backcompat() %}\n    {{ return(adapter.dispatch('current_timestamp_backcompat', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__current_timestamp_backcompat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0374908, "supported_languages": null}, "macro.dbt.default__current_timestamp_backcompat": {"name": "default__current_timestamp_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.default__current_timestamp_backcompat", "macro_sql": "{% macro default__current_timestamp_backcompat() %}\n    current_timestamp::timestamp\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0376465, "supported_languages": null}, "macro.dbt.current_timestamp_in_utc_backcompat": {"name": "current_timestamp_in_utc_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.current_timestamp_in_utc_backcompat", "macro_sql": "{% macro current_timestamp_in_utc_backcompat() %}\n    {{ return(adapter.dispatch('current_timestamp_in_utc_backcompat', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__current_timestamp_in_utc_backcompat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.037951, "supported_languages": null}, "macro.dbt.default__current_timestamp_in_utc_backcompat": {"name": "default__current_timestamp_in_utc_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/timestamps.sql", "original_file_path": "macros/adapters/timestamps.sql", "unique_id": "macro.dbt.default__current_timestamp_in_utc_backcompat", "macro_sql": "{% macro default__current_timestamp_in_utc_backcompat() %}\n    {{ return(adapter.dispatch('current_timestamp_backcompat', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.current_timestamp_backcompat", "macro.dbt_postgres.postgres__current_timestamp_backcompat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0382597, "supported_languages": null}, "macro.dbt.validate_sql": {"name": "validate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/validate_sql.sql", "original_file_path": "macros/adapters/validate_sql.sql", "unique_id": "macro.dbt.validate_sql", "macro_sql": "{% macro validate_sql(sql) -%}\n  {{ return(adapter.dispatch('validate_sql', 'dbt')(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__validate_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0387733, "supported_languages": null}, "macro.dbt.default__validate_sql": {"name": "default__validate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/adapters/validate_sql.sql", "original_file_path": "macros/adapters/validate_sql.sql", "unique_id": "macro.dbt.default__validate_sql", "macro_sql": "{% macro default__validate_sql(sql) -%}\n  {% call statement('validate_sql') -%}\n    explain {{ sql }}\n  {% endcall %}\n  {{ return(load_result('validate_sql')) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.039285, "supported_languages": null}, "macro.dbt.convert_datetime": {"name": "convert_datetime", "resource_type": "macro", "package_name": "dbt", "path": "macros/etc/datetime.sql", "original_file_path": "macros/etc/datetime.sql", "unique_id": "macro.dbt.convert_datetime", "macro_sql": "{% macro convert_datetime(date_str, date_fmt) %}\n\n  {% set error_msg -%}\n      The provided partition date '{{ date_str }}' does not match the expected format '{{ date_fmt }}'\n  {%- endset %}\n\n  {% set res = try_or_compiler_error(error_msg, modules.datetime.datetime.strptime, date_str.strip(), date_fmt) %}\n  {{ return(res) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0405958, "supported_languages": null}, "macro.dbt.dates_in_range": {"name": "dates_in_range", "resource_type": "macro", "package_name": "dbt", "path": "macros/etc/datetime.sql", "original_file_path": "macros/etc/datetime.sql", "unique_id": "macro.dbt.dates_in_range", "macro_sql": "{% macro dates_in_range(start_date_str, end_date_str=none, in_fmt=\"%Y%m%d\", out_fmt=\"%Y%m%d\") %}\n    {% set end_date_str = start_date_str if end_date_str is none else end_date_str %}\n\n    {% set start_date = convert_datetime(start_date_str, in_fmt) %}\n    {% set end_date = convert_datetime(end_date_str, in_fmt) %}\n\n    {% set day_count = (end_date - start_date).days %}\n    {% if day_count < 0 %}\n        {% set msg -%}\n            Partition start date is after the end date ({{ start_date }}, {{ end_date }})\n        {%- endset %}\n\n        {{ exceptions.raise_compiler_error(msg, model) }}\n    {% endif %}\n\n    {% set date_list = [] %}\n    {% for i in range(0, day_count + 1) %}\n        {% set the_date = (modules.datetime.timedelta(days=i) + start_date) %}\n        {% if not out_fmt %}\n            {% set _ = date_list.append(the_date) %}\n        {% else %}\n            {% set _ = date_list.append(the_date.strftime(out_fmt)) %}\n        {% endif %}\n    {% endfor %}\n\n    {{ return(date_list) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.convert_datetime"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0427682, "supported_languages": null}, "macro.dbt.partition_range": {"name": "partition_range", "resource_type": "macro", "package_name": "dbt", "path": "macros/etc/datetime.sql", "original_file_path": "macros/etc/datetime.sql", "unique_id": "macro.dbt.partition_range", "macro_sql": "{% macro partition_range(raw_partition_date, date_fmt='%Y%m%d') %}\n    {% set partition_range = (raw_partition_date | string).split(\",\") %}\n\n    {% if (partition_range | length) == 1 %}\n      {% set start_date = partition_range[0] %}\n      {% set end_date = none %}\n    {% elif (partition_range | length) == 2 %}\n      {% set start_date = partition_range[0] %}\n      {% set end_date = partition_range[1] %}\n    {% else %}\n      {{ exceptions.raise_compiler_error(\"Invalid partition time. Expected format: {Start Date}[,{End Date}]. Got: \" ~ raw_partition_date) }}\n    {% endif %}\n\n    {{ return(dates_in_range(start_date, end_date, in_fmt=date_fmt)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.dates_in_range"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0441065, "supported_languages": null}, "macro.dbt.py_current_timestring": {"name": "py_current_timestring", "resource_type": "macro", "package_name": "dbt", "path": "macros/etc/datetime.sql", "original_file_path": "macros/etc/datetime.sql", "unique_id": "macro.dbt.py_current_timestring", "macro_sql": "{% macro py_current_timestring() %}\n    {% set dt = modules.datetime.datetime.now() %}\n    {% do return(dt.strftime(\"%Y%m%d%H%M%S%f\")) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0445366, "supported_languages": null}, "macro.dbt.statement": {"name": "statement", "resource_type": "macro", "package_name": "dbt", "path": "macros/etc/statement.sql", "original_file_path": "macros/etc/statement.sql", "unique_id": "macro.dbt.statement", "macro_sql": "\n{%- macro statement(name=None, fetch_result=False, auto_begin=True, language='sql') -%}\n  {%- if execute: -%}\n    {%- set compiled_code = caller() -%}\n\n    {%- if name == 'main' -%}\n      {{ log('Writing runtime {} for node \"{}\"'.format(language, model['unique_id'])) }}\n      {{ write(compiled_code) }}\n    {%- endif -%}\n    {%- if language == 'sql'-%}\n      {%- set res, table = adapter.execute(compiled_code, auto_begin=auto_begin, fetch=fetch_result) -%}\n    {%- elif language == 'python' -%}\n      {%- set res = submit_python_job(model, compiled_code) -%}\n      {#-- TODO: What should table be for python models? --#}\n      {%- set table = None -%}\n    {%- else -%}\n      {% do exceptions.raise_compiler_error(\"statement macro didn't get supported language\") %}\n    {%- endif -%}\n\n    {%- if name is not none -%}\n      {{ store_result(name, response=res, agate_table=table) }}\n    {%- endif -%}\n\n  {%- endif -%}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0467966, "supported_languages": null}, "macro.dbt.noop_statement": {"name": "noop_statement", "resource_type": "macro", "package_name": "dbt", "path": "macros/etc/statement.sql", "original_file_path": "macros/etc/statement.sql", "unique_id": "macro.dbt.noop_statement", "macro_sql": "{% macro noop_statement(name=None, message=None, code=None, rows_affected=None, res=None) -%}\n  {%- set sql = caller() -%}\n\n  {%- if name == 'main' -%}\n    {{ log('Writing runtime SQL for node \"{}\"'.format(model['unique_id'])) }}\n    {{ write(sql) }}\n  {%- endif -%}\n\n  {%- if name is not none -%}\n    {{ store_raw_result(name, message=message, code=code, rows_affected=rows_affected, agate_table=res) }}\n  {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.047836, "supported_languages": null}, "macro.dbt.run_query": {"name": "run_query", "resource_type": "macro", "package_name": "dbt", "path": "macros/etc/statement.sql", "original_file_path": "macros/etc/statement.sql", "unique_id": "macro.dbt.run_query", "macro_sql": "{% macro run_query(sql) %}\n  {% call statement(\"run_query_statement\", fetch_result=true, auto_begin=false) %}\n    {{ sql }}\n  {% endcall %}\n\n  {% do return(load_result(\"run_query_statement\").table) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.048375, "supported_languages": null}, "macro.dbt.default__test_accepted_values": {"name": "default__test_accepted_values", "resource_type": "macro", "package_name": "dbt", "path": "macros/generic_test_sql/accepted_values.sql", "original_file_path": "macros/generic_test_sql/accepted_values.sql", "unique_id": "macro.dbt.default__test_accepted_values", "macro_sql": "{% macro default__test_accepted_values(model, column_name, values, quote=True) %}\n\nwith all_values as (\n\n    select\n        {{ column_name }} as value_field,\n        count(*) as n_records\n\n    from {{ model }}\n    group by {{ column_name }}\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    {% for value in values -%}\n        {% if quote -%}\n        '{{ value }}'\n        {%- else -%}\n        {{ value }}\n        {%- endif -%}\n        {%- if not loop.last -%},{%- endif %}\n    {%- endfor %}\n)\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0493555, "supported_languages": null}, "macro.dbt.default__test_not_null": {"name": "default__test_not_null", "resource_type": "macro", "package_name": "dbt", "path": "macros/generic_test_sql/not_null.sql", "original_file_path": "macros/generic_test_sql/not_null.sql", "unique_id": "macro.dbt.default__test_not_null", "macro_sql": "{% macro default__test_not_null(model, column_name) %}\n\n{% set column_list = '*' if should_store_failures() else column_name %}\n\nselect {{ column_list }}\nfrom {{ model }}\nwhere {{ column_name }} is null\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.should_store_failures"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0500503, "supported_languages": null}, "macro.dbt.default__test_relationships": {"name": "default__test_relationships", "resource_type": "macro", "package_name": "dbt", "path": "macros/generic_test_sql/relationships.sql", "original_file_path": "macros/generic_test_sql/relationships.sql", "unique_id": "macro.dbt.default__test_relationships", "macro_sql": "{% macro default__test_relationships(model, column_name, to, field) %}\n\nwith child as (\n    select {{ column_name }} as from_field\n    from {{ model }}\n    where {{ column_name }} is not null\n),\n\nparent as (\n    select {{ field }} as to_field\n    from {{ to }}\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.050615, "supported_languages": null}, "macro.dbt.default__test_unique": {"name": "default__test_unique", "resource_type": "macro", "package_name": "dbt", "path": "macros/generic_test_sql/unique.sql", "original_file_path": "macros/generic_test_sql/unique.sql", "unique_id": "macro.dbt.default__test_unique", "macro_sql": "{% macro default__test_unique(model, column_name) %}\n\nselect\n    {{ column_name }} as unique_field,\n    count(*) as n_records\n\nfrom {{ model }}\nwhere {{ column_name }} is not null\ngroup by {{ column_name }}\nhaving count(*) > 1\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0511117, "supported_languages": null}, "macro.dbt.generate_alias_name": {"name": "generate_alias_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/get_custom_name/get_custom_alias.sql", "original_file_path": "macros/get_custom_name/get_custom_alias.sql", "unique_id": "macro.dbt.generate_alias_name", "macro_sql": "{% macro generate_alias_name(custom_alias_name=none, node=none) -%}\n    {% do return(adapter.dispatch('generate_alias_name', 'dbt')(custom_alias_name, node)) %}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_alias_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0518208, "supported_languages": null}, "macro.dbt.default__generate_alias_name": {"name": "default__generate_alias_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/get_custom_name/get_custom_alias.sql", "original_file_path": "macros/get_custom_name/get_custom_alias.sql", "unique_id": "macro.dbt.default__generate_alias_name", "macro_sql": "{% macro default__generate_alias_name(custom_alias_name=none, node=none) -%}\n\n    {%- if custom_alias_name -%}\n\n        {{ custom_alias_name | trim }}\n\n    {%- elif node.version -%}\n\n        {{ return(node.name ~ \"_v\" ~ (node.version | replace(\".\", \"_\"))) }}\n\n    {%- else -%}\n\n        {{ node.name }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.052515, "supported_languages": null}, "macro.dbt.generate_database_name": {"name": "generate_database_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/get_custom_name/get_custom_database.sql", "original_file_path": "macros/get_custom_name/get_custom_database.sql", "unique_id": "macro.dbt.generate_database_name", "macro_sql": "{% macro generate_database_name(custom_database_name=none, node=none) -%}\n    {% do return(adapter.dispatch('generate_database_name', 'dbt')(custom_database_name, node)) %}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_database_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0531693, "supported_languages": null}, "macro.dbt.default__generate_database_name": {"name": "default__generate_database_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/get_custom_name/get_custom_database.sql", "original_file_path": "macros/get_custom_name/get_custom_database.sql", "unique_id": "macro.dbt.default__generate_database_name", "macro_sql": "{% macro default__generate_database_name(custom_database_name=none, node=none) -%}\n    {%- set default_database = target.database -%}\n    {%- if custom_database_name is none -%}\n\n        {{ default_database }}\n\n    {%- else -%}\n\n        {{ custom_database_name }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0536468, "supported_languages": null}, "macro.dbt.generate_schema_name": {"name": "generate_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/get_custom_name/get_custom_schema.sql", "original_file_path": "macros/get_custom_name/get_custom_schema.sql", "unique_id": "macro.dbt.generate_schema_name", "macro_sql": "{% macro generate_schema_name(custom_schema_name=none, node=none) -%}\n    {{ return(adapter.dispatch('generate_schema_name', 'dbt')(custom_schema_name, node)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_schema_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0544555, "supported_languages": null}, "macro.dbt.default__generate_schema_name": {"name": "default__generate_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/get_custom_name/get_custom_schema.sql", "original_file_path": "macros/get_custom_name/get_custom_schema.sql", "unique_id": "macro.dbt.default__generate_schema_name", "macro_sql": "{% macro default__generate_schema_name(custom_schema_name, node) -%}\n\n    {%- set default_schema = target.schema -%}\n    {%- if custom_schema_name is none -%}\n\n        {{ default_schema }}\n\n    {%- else -%}\n\n        {{ default_schema }}_{{ custom_schema_name | trim }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.054955, "supported_languages": null}, "macro.dbt.generate_schema_name_for_env": {"name": "generate_schema_name_for_env", "resource_type": "macro", "package_name": "dbt", "path": "macros/get_custom_name/get_custom_schema.sql", "original_file_path": "macros/get_custom_name/get_custom_schema.sql", "unique_id": "macro.dbt.generate_schema_name_for_env", "macro_sql": "{% macro generate_schema_name_for_env(custom_schema_name, node) -%}\n\n    {%- set default_schema = target.schema -%}\n    {%- if target.name == 'prod' and custom_schema_name is not none -%}\n\n        {{ custom_schema_name | trim }}\n\n    {%- else -%}\n\n        {{ default_schema }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.055475, "supported_languages": null}, "macro.dbt.set_sql_header": {"name": "set_sql_header", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/configs.sql", "original_file_path": "macros/materializations/configs.sql", "unique_id": "macro.dbt.set_sql_header", "macro_sql": "{% macro set_sql_header(config) -%}\n  {{ config.set('sql_header', caller()) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0560322, "supported_languages": null}, "macro.dbt.should_full_refresh": {"name": "should_full_refresh", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/configs.sql", "original_file_path": "macros/materializations/configs.sql", "unique_id": "macro.dbt.should_full_refresh", "macro_sql": "{% macro should_full_refresh() %}\n  {% set config_full_refresh = config.get('full_refresh') %}\n  {% if config_full_refresh is none %}\n    {% set config_full_refresh = flags.FULL_REFRESH %}\n  {% endif %}\n  {% do return(config_full_refresh) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.056577, "supported_languages": null}, "macro.dbt.should_store_failures": {"name": "should_store_failures", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/configs.sql", "original_file_path": "macros/materializations/configs.sql", "unique_id": "macro.dbt.should_store_failures", "macro_sql": "{% macro should_store_failures() %}\n  {% set config_store_failures = config.get('store_failures') %}\n  {% if config_store_failures is none %}\n    {% set config_store_failures = flags.STORE_FAILURES %}\n  {% endif %}\n  {% do return(config_store_failures) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0572357, "supported_languages": null}, "macro.dbt.run_hooks": {"name": "run_hooks", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/hooks.sql", "original_file_path": "macros/materializations/hooks.sql", "unique_id": "macro.dbt.run_hooks", "macro_sql": "{% macro run_hooks(hooks, inside_transaction=True) %}\n  {% for hook in hooks | selectattr('transaction', 'equalto', inside_transaction)  %}\n    {% if not inside_transaction and loop.first %}\n      {% call statement(auto_begin=inside_transaction) %}\n        commit;\n      {% endcall %}\n    {% endif %}\n    {% set rendered = render(hook.get('sql')) | trim %}\n    {% if (rendered | length) > 0 %}\n      {% call statement(auto_begin=inside_transaction) %}\n        {{ rendered }}\n      {% endcall %}\n    {% endif %}\n  {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0587633, "supported_languages": null}, "macro.dbt.make_hook_config": {"name": "make_hook_config", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/hooks.sql", "original_file_path": "macros/materializations/hooks.sql", "unique_id": "macro.dbt.make_hook_config", "macro_sql": "{% macro make_hook_config(sql, inside_transaction) %}\n    {{ tojson({\"sql\": sql, \"transaction\": inside_transaction}) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.059197, "supported_languages": null}, "macro.dbt.before_begin": {"name": "before_begin", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/hooks.sql", "original_file_path": "macros/materializations/hooks.sql", "unique_id": "macro.dbt.before_begin", "macro_sql": "{% macro before_begin(sql) %}\n    {{ make_hook_config(sql, inside_transaction=False) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_hook_config"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0594988, "supported_languages": null}, "macro.dbt.in_transaction": {"name": "in_transaction", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/hooks.sql", "original_file_path": "macros/materializations/hooks.sql", "unique_id": "macro.dbt.in_transaction", "macro_sql": "{% macro in_transaction(sql) %}\n    {{ make_hook_config(sql, inside_transaction=True) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_hook_config"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0597734, "supported_languages": null}, "macro.dbt.after_commit": {"name": "after_commit", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/hooks.sql", "original_file_path": "macros/materializations/hooks.sql", "unique_id": "macro.dbt.after_commit", "macro_sql": "{% macro after_commit(sql) %}\n    {{ make_hook_config(sql, inside_transaction=False) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_hook_config"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0600417, "supported_languages": null}, "macro.dbt.materialization_materialized_view_default": {"name": "materialization_materialized_view_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/materialized_view.sql", "original_file_path": "macros/materializations/models/materialized_view.sql", "unique_id": "macro.dbt.materialization_materialized_view_default", "macro_sql": "{% materialization materialized_view, default %}\n    {% set existing_relation = load_cached_relation(this) %}\n    {% set target_relation = this.incorporate(type=this.MaterializedView) %}\n    {% set intermediate_relation = make_intermediate_relation(target_relation) %}\n    {% set backup_relation_type = target_relation.MaterializedView if existing_relation is none else existing_relation.type %}\n    {% set backup_relation = make_backup_relation(target_relation, backup_relation_type) %}\n\n    {{ materialized_view_setup(backup_relation, intermediate_relation, pre_hooks) }}\n\n        {% set build_sql = materialized_view_get_build_sql(existing_relation, target_relation, backup_relation, intermediate_relation) %}\n\n        {% if build_sql == '' %}\n            {{ materialized_view_execute_no_op(target_relation) }}\n        {% else %}\n            {{ materialized_view_execute_build_sql(build_sql, existing_relation, target_relation, post_hooks) }}\n        {% endif %}\n\n    {{ materialized_view_teardown(backup_relation, intermediate_relation, post_hooks) }}\n\n    {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.materialized_view_setup", "macro.dbt.materialized_view_get_build_sql", "macro.dbt.materialized_view_execute_no_op", "macro.dbt.materialized_view_execute_build_sql", "macro.dbt.materialized_view_teardown"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0627394, "supported_languages": ["sql"]}, "macro.dbt.materialized_view_setup": {"name": "materialized_view_setup", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/materialized_view.sql", "original_file_path": "macros/materializations/models/materialized_view.sql", "unique_id": "macro.dbt.materialized_view_setup", "macro_sql": "{% macro materialized_view_setup(backup_relation, intermediate_relation, pre_hooks) %}\n\n    -- backup_relation and intermediate_relation should not already exist in the database\n    -- it's possible these exist because of a previous run that exited unexpectedly\n    {% set preexisting_backup_relation = load_cached_relation(backup_relation) %}\n    {% set preexisting_intermediate_relation = load_cached_relation(intermediate_relation) %}\n\n    -- drop the temp relations if they exist already in the database\n    {{ drop_relation_if_exists(preexisting_backup_relation) }}\n    {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n\n    {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0634487, "supported_languages": null}, "macro.dbt.materialized_view_teardown": {"name": "materialized_view_teardown", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/materialized_view.sql", "original_file_path": "macros/materializations/models/materialized_view.sql", "unique_id": "macro.dbt.materialized_view_teardown", "macro_sql": "{% macro materialized_view_teardown(backup_relation, intermediate_relation, post_hooks) %}\n\n    -- drop the temp relations if they exist to leave the database clean for the next run\n    {{ drop_relation_if_exists(backup_relation) }}\n    {{ drop_relation_if_exists(intermediate_relation) }}\n\n    {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.063918, "supported_languages": null}, "macro.dbt.materialized_view_get_build_sql": {"name": "materialized_view_get_build_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/materialized_view.sql", "original_file_path": "macros/materializations/models/materialized_view.sql", "unique_id": "macro.dbt.materialized_view_get_build_sql", "macro_sql": "{% macro materialized_view_get_build_sql(existing_relation, target_relation, backup_relation, intermediate_relation) %}\n\n    {% set full_refresh_mode = should_full_refresh() %}\n\n    -- determine the scenario we're in: create, full_refresh, alter, refresh data\n    {% if existing_relation is none %}\n        {% set build_sql = get_create_materialized_view_as_sql(target_relation, sql) %}\n    {% elif full_refresh_mode or not existing_relation.is_materialized_view %}\n        {% set build_sql = get_replace_sql(existing_relation, target_relation, sql) %}\n    {% else %}\n\n        -- get config options\n        {% set on_configuration_change = config.get('on_configuration_change') %}\n        {% set configuration_changes = get_materialized_view_configuration_changes(existing_relation, config) %}\n\n        {% if configuration_changes is none %}\n            {% set build_sql = refresh_materialized_view(target_relation) %}\n\n        {% elif on_configuration_change == 'apply' %}\n            {% set build_sql = get_alter_materialized_view_as_sql(target_relation, configuration_changes, sql, existing_relation, backup_relation, intermediate_relation) %}\n        {% elif on_configuration_change == 'continue' %}\n            {% set build_sql = '' %}\n            {{ exceptions.warn(\"Configuration changes were identified and `on_configuration_change` was set to `continue` for `\" ~ target_relation.render() ~ \"`\") }}\n        {% elif on_configuration_change == 'fail' %}\n            {{ exceptions.raise_fail_fast_error(\"Configuration changes were identified and `on_configuration_change` was set to `fail` for `\" ~ target_relation.render() ~ \"`\") }}\n\n        {% else %}\n            -- this only happens if the user provides a value other than `apply`, 'skip', 'fail'\n            {{ exceptions.raise_compiler_error(\"Unexpected configuration scenario\") }}\n\n        {% endif %}\n\n    {% endif %}\n\n    {% do return(build_sql) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.should_full_refresh", "macro.dbt.get_create_materialized_view_as_sql", "macro.dbt.get_replace_sql", "macro.dbt.get_materialized_view_configuration_changes", "macro.dbt.refresh_materialized_view", "macro.dbt.get_alter_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0661936, "supported_languages": null}, "macro.dbt.materialized_view_execute_no_op": {"name": "materialized_view_execute_no_op", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/materialized_view.sql", "original_file_path": "macros/materializations/models/materialized_view.sql", "unique_id": "macro.dbt.materialized_view_execute_no_op", "macro_sql": "{% macro materialized_view_execute_no_op(target_relation) %}\n    {% do store_raw_result(\n        name=\"main\",\n        message=\"skip \" ~ target_relation,\n        code=\"skip\",\n        rows_affected=\"-1\"\n    ) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0667138, "supported_languages": null}, "macro.dbt.materialized_view_execute_build_sql": {"name": "materialized_view_execute_build_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/materialized_view.sql", "original_file_path": "macros/materializations/models/materialized_view.sql", "unique_id": "macro.dbt.materialized_view_execute_build_sql", "macro_sql": "{% macro materialized_view_execute_build_sql(build_sql, existing_relation, target_relation, post_hooks) %}\n\n    -- `BEGIN` happens here:\n    {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n    {% set grant_config = config.get('grants') %}\n\n    {% call statement(name=\"main\") %}\n        {{ build_sql }}\n    {% endcall %}\n\n    {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n    {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n    {% do persist_docs(target_relation, model) %}\n\n    {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n    {{ adapter.commit() }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_hooks", "macro.dbt.statement", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0678866, "supported_languages": null}, "macro.dbt.materialization_table_default": {"name": "materialization_table_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/table.sql", "original_file_path": "macros/materializations/models/table.sql", "unique_id": "macro.dbt.materialization_table_default", "macro_sql": "{% materialization table, default %}\n\n  {%- set existing_relation = load_cached_relation(this) -%}\n  {%- set target_relation = this.incorporate(type='table') %}\n  {%- set intermediate_relation =  make_intermediate_relation(target_relation) -%}\n  -- the intermediate_relation should not already exist in the database; get_relation\n  -- will return None in that case. Otherwise, we get a relation that we can drop\n  -- later, before we try to use this name for the current operation\n  {%- set preexisting_intermediate_relation = load_cached_relation(intermediate_relation) -%}\n  /*\n      See ../view/view.sql for more information about this relation.\n  */\n  {%- set backup_relation_type = 'table' if existing_relation is none else existing_relation.type -%}\n  {%- set backup_relation = make_backup_relation(target_relation, backup_relation_type) -%}\n  -- as above, the backup_relation should not already exist\n  {%- set preexisting_backup_relation = load_cached_relation(backup_relation) -%}\n  -- grab current tables grants config for comparision later on\n  {% set grant_config = config.get('grants') %}\n\n  -- drop the temp relations if they exist already in the database\n  {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n  {{ drop_relation_if_exists(preexisting_backup_relation) }}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  -- build model\n  {% call statement('main') -%}\n    {{ get_create_table_as_sql(False, intermediate_relation, sql) }}\n  {%- endcall %}\n\n  {% do create_indexes(intermediate_relation) %}\n\n  -- cleanup\n  {% if existing_relation is not none %}\n     /* Do the equivalent of rename_if_exists. 'existing_relation' could have been dropped\n        since the variable was first set. */\n    {% set existing_relation = load_cached_relation(existing_relation) %}\n    {% if existing_relation is not none %}\n        {{ adapter.rename_relation(existing_relation, backup_relation) }}\n    {% endif %}\n  {% endif %}\n\n  {{ adapter.rename_relation(intermediate_relation, target_relation) }}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  -- `COMMIT` happens here\n  {{ adapter.commit() }}\n\n  -- finally, drop the existing/backup relation after the commit\n  {{ drop_relation_if_exists(backup_relation) }}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks", "macro.dbt.statement", "macro.dbt.get_create_table_as_sql", "macro.dbt.create_indexes", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0715826, "supported_languages": ["sql"]}, "macro.dbt.materialization_view_default": {"name": "materialization_view_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/view.sql", "original_file_path": "macros/materializations/models/view.sql", "unique_id": "macro.dbt.materialization_view_default", "macro_sql": "{%- materialization view, default -%}\n\n  {%- set existing_relation = load_cached_relation(this) -%}\n  {%- set target_relation = this.incorporate(type='view') -%}\n  {%- set intermediate_relation =  make_intermediate_relation(target_relation) -%}\n\n  -- the intermediate_relation should not already exist in the database; get_relation\n  -- will return None in that case. Otherwise, we get a relation that we can drop\n  -- later, before we try to use this name for the current operation\n  {%- set preexisting_intermediate_relation = load_cached_relation(intermediate_relation) -%}\n  /*\n     This relation (probably) doesn't exist yet. If it does exist, it's a leftover from\n     a previous run, and we're going to try to drop it immediately. At the end of this\n     materialization, we're going to rename the \"existing_relation\" to this identifier,\n     and then we're going to drop it. In order to make sure we run the correct one of:\n       - drop view ...\n       - drop table ...\n\n     We need to set the type of this relation to be the type of the existing_relation, if it exists,\n     or else \"view\" as a sane default if it does not. Note that if the existing_relation does not\n     exist, then there is nothing to move out of the way and subsequentally drop. In that case,\n     this relation will be effectively unused.\n  */\n  {%- set backup_relation_type = 'view' if existing_relation is none else existing_relation.type -%}\n  {%- set backup_relation = make_backup_relation(target_relation, backup_relation_type) -%}\n  -- as above, the backup_relation should not already exist\n  {%- set preexisting_backup_relation = load_cached_relation(backup_relation) -%}\n  -- grab current tables grants config for comparision later on\n  {% set grant_config = config.get('grants') %}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- drop the temp relations if they exist already in the database\n  {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n  {{ drop_relation_if_exists(preexisting_backup_relation) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  -- build model\n  {% call statement('main') -%}\n    {{ get_create_view_as_sql(intermediate_relation, sql) }}\n  {%- endcall %}\n\n  -- cleanup\n  -- move the existing view out of the way\n  {% if existing_relation is not none %}\n     /* Do the equivalent of rename_if_exists. 'existing_relation' could have been dropped\n        since the variable was first set. */\n    {% set existing_relation = load_cached_relation(existing_relation) %}\n    {% if existing_relation is not none %}\n        {{ adapter.rename_relation(existing_relation, backup_relation) }}\n    {% endif %}\n  {% endif %}\n  {{ adapter.rename_relation(intermediate_relation, target_relation) }}\n\n  {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  {{ adapter.commit() }}\n\n  {{ drop_relation_if_exists(backup_relation) }}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.run_hooks", "macro.dbt.drop_relation_if_exists", "macro.dbt.statement", "macro.dbt.get_create_view_as_sql", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0752487, "supported_languages": ["sql"]}, "macro.dbt.can_clone_table": {"name": "can_clone_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/clone/can_clone_table.sql", "original_file_path": "macros/materializations/models/clone/can_clone_table.sql", "unique_id": "macro.dbt.can_clone_table", "macro_sql": "{% macro can_clone_table() %}\n    {{ return(adapter.dispatch('can_clone_table', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__can_clone_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0757473, "supported_languages": null}, "macro.dbt.default__can_clone_table": {"name": "default__can_clone_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/clone/can_clone_table.sql", "original_file_path": "macros/materializations/models/clone/can_clone_table.sql", "unique_id": "macro.dbt.default__can_clone_table", "macro_sql": "{% macro default__can_clone_table() %}\n    {{ return(False) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.075982, "supported_languages": null}, "macro.dbt.materialization_clone_default": {"name": "materialization_clone_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/clone/clone.sql", "original_file_path": "macros/materializations/models/clone/clone.sql", "unique_id": "macro.dbt.materialization_clone_default", "macro_sql": "{%- materialization clone, default -%}\n\n  {%- set relations = {'relations': []} -%}\n\n  {%- if not defer_relation -%}\n      -- nothing to do\n      {{ log(\"No relation found in state manifest for \" ~ model.unique_id, info=True) }}\n      {{ return(relations) }}\n  {%- endif -%}\n\n  {%- set existing_relation = load_cached_relation(this) -%}\n\n  {%- if existing_relation and not flags.FULL_REFRESH -%}\n      -- noop!\n      {{ log(\"Relation \" ~ existing_relation ~ \" already exists\", info=True) }}\n      {{ return(relations) }}\n  {%- endif -%}\n\n  {%- set other_existing_relation = load_cached_relation(defer_relation) -%}\n\n  -- If this is a database that can do zero-copy cloning of tables, and the other relation is a table, then this will be a table\n  -- Otherwise, this will be a view\n\n  {% set can_clone_table = can_clone_table() %}\n\n  {%- if other_existing_relation and other_existing_relation.type == 'table' and can_clone_table -%}\n\n      {%- set target_relation = this.incorporate(type='table') -%}\n      {% if existing_relation is not none and not existing_relation.is_table %}\n        {{ log(\"Dropping relation \" ~ existing_relation.render() ~ \" because it is of type \" ~ existing_relation.type) }}\n        {{ drop_relation_if_exists(existing_relation) }}\n      {% endif %}\n\n      -- as a general rule, data platforms that can clone tables can also do atomic 'create or replace'\n      {% if target_relation.database == defer_relation.database and\n            target_relation.schema == defer_relation.schema and\n            target_relation.identifier == defer_relation.identifier %}\n        {{ log(\"Target relation and defer relation are the same, skipping clone for relation: \" ~ target_relation.render()) }}\n      {% else %}\n        {% call statement('main') %}\n            {{ create_or_replace_clone(target_relation, defer_relation) }}\n        {% endcall %}\n      {% endif %}\n      {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n      {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n      {% do persist_docs(target_relation, model) %}\n\n      {{ return({'relations': [target_relation]}) }}\n\n  {%- else -%}\n\n      {%- set target_relation = this.incorporate(type='view') -%}\n\n      -- reuse the view materialization\n      -- TODO: support actual dispatch for materialization macros\n      -- Tracking ticket: https://github.com/dbt-labs/dbt-core/issues/7799\n      {% set search_name = \"materialization_view_\" ~ adapter.type() %}\n      {% if not search_name in context %}\n          {% set search_name = \"materialization_view_default\" %}\n      {% endif %}\n      {% set materialization_macro = context[search_name] %}\n      {% set relations = materialization_macro() %}\n      {{ return(relations) }}\n\n  {%- endif -%}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.can_clone_table", "macro.dbt.drop_relation_if_exists", "macro.dbt.statement", "macro.dbt.create_or_replace_clone", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0803576, "supported_languages": ["sql"]}, "macro.dbt.create_or_replace_clone": {"name": "create_or_replace_clone", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/clone/create_or_replace_clone.sql", "original_file_path": "macros/materializations/models/clone/create_or_replace_clone.sql", "unique_id": "macro.dbt.create_or_replace_clone", "macro_sql": "{% macro create_or_replace_clone(this_relation, defer_relation) %}\n    {{ return(adapter.dispatch('create_or_replace_clone', 'dbt')(this_relation, defer_relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_or_replace_clone"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0809314, "supported_languages": null}, "macro.dbt.default__create_or_replace_clone": {"name": "default__create_or_replace_clone", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/clone/create_or_replace_clone.sql", "original_file_path": "macros/materializations/models/clone/create_or_replace_clone.sql", "unique_id": "macro.dbt.default__create_or_replace_clone", "macro_sql": "{% macro default__create_or_replace_clone(this_relation, defer_relation) %}\n    create or replace table {{ this_relation.render() }} clone {{ defer_relation.render() }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0812516, "supported_languages": null}, "macro.dbt.get_quoted_csv": {"name": "get_quoted_csv", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/column_helpers.sql", "original_file_path": "macros/materializations/models/incremental/column_helpers.sql", "unique_id": "macro.dbt.get_quoted_csv", "macro_sql": "{% macro get_quoted_csv(column_names) %}\n\n    {% set quoted = [] %}\n    {% for col in column_names -%}\n        {%- do quoted.append(adapter.quote(col)) -%}\n    {%- endfor %}\n\n    {%- set dest_cols_csv = quoted | join(', ') -%}\n    {{ return(dest_cols_csv) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.082838, "supported_languages": null}, "macro.dbt.diff_columns": {"name": "diff_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/column_helpers.sql", "original_file_path": "macros/materializations/models/incremental/column_helpers.sql", "unique_id": "macro.dbt.diff_columns", "macro_sql": "{% macro diff_columns(source_columns, target_columns) %}\n\n  {% set result = [] %}\n  {% set source_names = source_columns | map(attribute = 'column') | list %}\n  {% set target_names = target_columns | map(attribute = 'column') | list %}\n\n   {# --check whether the name attribute exists in the target - this does not perform a data type check #}\n   {% for sc in source_columns %}\n     {% if sc.name not in target_names %}\n        {{ result.append(sc) }}\n     {% endif %}\n   {% endfor %}\n\n  {{ return(result) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.083836, "supported_languages": null}, "macro.dbt.diff_column_data_types": {"name": "diff_column_data_types", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/column_helpers.sql", "original_file_path": "macros/materializations/models/incremental/column_helpers.sql", "unique_id": "macro.dbt.diff_column_data_types", "macro_sql": "{% macro diff_column_data_types(source_columns, target_columns) %}\n\n  {% set result = [] %}\n  {% for sc in source_columns %}\n    {% set tc = target_columns | selectattr(\"name\", \"equalto\", sc.name) | list | first %}\n    {% if tc %}\n      {% if sc.data_type != tc.data_type and not sc.can_expand_to(other_column=tc) %}\n        {{ result.append( { 'column_name': tc.name, 'new_type': sc.data_type } ) }}\n      {% endif %}\n    {% endif %}\n  {% endfor %}\n\n  {{ return(result) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0850086, "supported_languages": null}, "macro.dbt.get_merge_update_columns": {"name": "get_merge_update_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/column_helpers.sql", "original_file_path": "macros/materializations/models/incremental/column_helpers.sql", "unique_id": "macro.dbt.get_merge_update_columns", "macro_sql": "{% macro get_merge_update_columns(merge_update_columns, merge_exclude_columns, dest_columns) %}\n  {{ return(adapter.dispatch('get_merge_update_columns', 'dbt')(merge_update_columns, merge_exclude_columns, dest_columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_merge_update_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.085456, "supported_languages": null}, "macro.dbt.default__get_merge_update_columns": {"name": "default__get_merge_update_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/column_helpers.sql", "original_file_path": "macros/materializations/models/incremental/column_helpers.sql", "unique_id": "macro.dbt.default__get_merge_update_columns", "macro_sql": "{% macro default__get_merge_update_columns(merge_update_columns, merge_exclude_columns, dest_columns) %}\n  {%- set default_cols = dest_columns | map(attribute=\"quoted\") | list -%}\n\n  {%- if merge_update_columns and merge_exclude_columns -%}\n    {{ exceptions.raise_compiler_error(\n        'Model cannot specify merge_update_columns and merge_exclude_columns. Please update model to use only one config'\n    )}}\n  {%- elif merge_update_columns -%}\n    {%- set update_columns = merge_update_columns -%}\n  {%- elif merge_exclude_columns -%}\n    {%- set update_columns = [] -%}\n    {%- for column in dest_columns -%}\n      {% if column.column | lower not in merge_exclude_columns | map(\"lower\") | list %}\n        {%- do update_columns.append(column.quoted) -%}\n      {% endif %}\n    {%- endfor -%}\n  {%- else -%}\n    {%- set update_columns = default_cols -%}\n  {%- endif -%}\n\n  {{ return(update_columns) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.087504, "supported_languages": null}, "macro.dbt.materialization_incremental_default": {"name": "materialization_incremental_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/incremental.sql", "original_file_path": "macros/materializations/models/incremental/incremental.sql", "unique_id": "macro.dbt.materialization_incremental_default", "macro_sql": "{% materialization incremental, default -%}\n\n  -- relations\n  {%- set existing_relation = load_cached_relation(this) -%}\n  {%- set target_relation = this.incorporate(type='table') -%}\n  {%- set temp_relation = make_temp_relation(target_relation)-%}\n  {%- set intermediate_relation = make_intermediate_relation(target_relation)-%}\n  {%- set backup_relation_type = 'table' if existing_relation is none else existing_relation.type -%}\n  {%- set backup_relation = make_backup_relation(target_relation, backup_relation_type) -%}\n\n  -- configs\n  {%- set unique_key = config.get('unique_key') -%}\n  {%- set full_refresh_mode = (should_full_refresh()  or existing_relation.is_view) -%}\n  {%- set on_schema_change = incremental_validate_on_schema_change(config.get('on_schema_change'), default='ignore') -%}\n\n  -- the temp_ and backup_ relations should not already exist in the database; get_relation\n  -- will return None in that case. Otherwise, we get a relation that we can drop\n  -- later, before we try to use this name for the current operation. This has to happen before\n  -- BEGIN, in a separate transaction\n  {%- set preexisting_intermediate_relation = load_cached_relation(intermediate_relation)-%}\n  {%- set preexisting_backup_relation = load_cached_relation(backup_relation) -%}\n   -- grab current tables grants config for comparision later on\n  {% set grant_config = config.get('grants') %}\n  {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n  {{ drop_relation_if_exists(preexisting_backup_relation) }}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  {% set to_drop = [] %}\n\n  {% set incremental_strategy = config.get('incremental_strategy') or 'default' %}\n  {% set strategy_sql_macro_func = adapter.get_incremental_strategy_macro(context, incremental_strategy) %}\n\n  {% if existing_relation is none %}\n      {% set build_sql = get_create_table_as_sql(False, target_relation, sql) %}\n      {% set relation_for_indexes = target_relation %}\n  {% elif full_refresh_mode %}\n      {% set build_sql = get_create_table_as_sql(False, intermediate_relation, sql) %}\n      {% set relation_for_indexes = intermediate_relation %}\n      {% set need_swap = true %}\n  {% else %}\n    {% do run_query(get_create_table_as_sql(True, temp_relation, sql)) %}\n    {% set relation_for_indexes = temp_relation %}\n    {% set contract_config = config.get('contract') %}\n    {% if not contract_config or not contract_config.enforced %}\n      {% do adapter.expand_target_column_types(\n               from_relation=temp_relation,\n               to_relation=target_relation) %}\n    {% endif %}\n    {#-- Process schema changes. Returns dict of changes if successful. Use source columns for upserting/merging --#}\n    {% set dest_columns = process_schema_changes(on_schema_change, temp_relation, existing_relation) %}\n    {% if not dest_columns %}\n      {% set dest_columns = adapter.get_columns_in_relation(existing_relation) %}\n    {% endif %}\n\n    {#-- Get the incremental_strategy, the macro to use for the strategy, and build the sql --#}\n    {% set incremental_predicates = config.get('predicates', none) or config.get('incremental_predicates', none) %}\n    {% set strategy_arg_dict = ({'target_relation': target_relation, 'temp_relation': temp_relation, 'unique_key': unique_key, 'dest_columns': dest_columns, 'incremental_predicates': incremental_predicates }) %}\n    {% set build_sql = strategy_sql_macro_func(strategy_arg_dict) %}\n\n  {% endif %}\n\n  {% call statement(\"main\") %}\n      {{ build_sql }}\n  {% endcall %}\n\n  {% if existing_relation is none or existing_relation.is_view or should_full_refresh() %}\n    {% do create_indexes(relation_for_indexes) %}\n  {% endif %}\n\n  {% if need_swap %}\n      {% do adapter.rename_relation(target_relation, backup_relation) %}\n      {% do adapter.rename_relation(intermediate_relation, target_relation) %}\n      {% do to_drop.append(backup_relation) %}\n  {% endif %}\n\n  {% set should_revoke = should_revoke(existing_relation, full_refresh_mode) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  -- `COMMIT` happens here\n  {% do adapter.commit() %}\n\n  {% for rel in to_drop %}\n      {% do adapter.drop_relation(rel) %}\n  {% endfor %}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{%- endmaterialization %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_temp_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.should_full_refresh", "macro.dbt.incremental_validate_on_schema_change", "macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks", "macro.dbt.get_create_table_as_sql", "macro.dbt.run_query", "macro.dbt.process_schema_changes", "macro.dbt.statement", "macro.dbt.create_indexes", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0950406, "supported_languages": ["sql"]}, "macro.dbt.is_incremental": {"name": "is_incremental", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/is_incremental.sql", "original_file_path": "macros/materializations/models/incremental/is_incremental.sql", "unique_id": "macro.dbt.is_incremental", "macro_sql": "{% macro is_incremental() %}\n    {#-- do not run introspective queries in parsing #}\n    {% if not execute %}\n        {{ return(False) }}\n    {% else %}\n        {% set relation = adapter.get_relation(this.database, this.schema, this.table) %}\n        {{ return(relation is not none\n                  and relation.type == 'table'\n                  and model.config.materialized == 'incremental'\n                  and not should_full_refresh()) }}\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.should_full_refresh"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.0961397, "supported_languages": null}, "macro.dbt.get_merge_sql": {"name": "get_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/merge.sql", "original_file_path": "macros/materializations/models/incremental/merge.sql", "unique_id": "macro.dbt.get_merge_sql", "macro_sql": "{% macro get_merge_sql(target, source, unique_key, dest_columns, incremental_predicates=none) -%}\n   -- back compat for old kwarg name\n  {% set incremental_predicates = kwargs.get('predicates', incremental_predicates) %}\n  {{ adapter.dispatch('get_merge_sql', 'dbt')(target, source, unique_key, dest_columns, incremental_predicates) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.097998, "supported_languages": null}, "macro.dbt.default__get_merge_sql": {"name": "default__get_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/merge.sql", "original_file_path": "macros/materializations/models/incremental/merge.sql", "unique_id": "macro.dbt.default__get_merge_sql", "macro_sql": "{% macro default__get_merge_sql(target, source, unique_key, dest_columns, incremental_predicates=none) -%}\n    {%- set predicates = [] if incremental_predicates is none else [] + incremental_predicates -%}\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n    {%- set merge_update_columns = config.get('merge_update_columns') -%}\n    {%- set merge_exclude_columns = config.get('merge_exclude_columns') -%}\n    {%- set update_columns = get_merge_update_columns(merge_update_columns, merge_exclude_columns, dest_columns) -%}\n    {%- set sql_header = config.get('sql_header', none) -%}\n\n    {% if unique_key %}\n        {% if unique_key is sequence and unique_key is not mapping and unique_key is not string %}\n            {% for key in unique_key %}\n                {% set this_key_match %}\n                    DBT_INTERNAL_SOURCE.{{ key }} = DBT_INTERNAL_DEST.{{ key }}\n                {% endset %}\n                {% do predicates.append(this_key_match) %}\n            {% endfor %}\n        {% else %}\n            {% set source_unique_key = (\"DBT_INTERNAL_SOURCE.\" ~ unique_key) | trim %}\n\t    {% set target_unique_key = (\"DBT_INTERNAL_DEST.\" ~ unique_key) | trim %}\n\t    {% set unique_key_match = equals(source_unique_key, target_unique_key) | trim %}\n            {% do predicates.append(unique_key_match) %}\n        {% endif %}\n    {% else %}\n        {% do predicates.append('FALSE') %}\n    {% endif %}\n\n    {{ sql_header if sql_header is not none }}\n\n    merge into {{ target }} as DBT_INTERNAL_DEST\n        using {{ source }} as DBT_INTERNAL_SOURCE\n        on {{\"(\" ~ predicates | join(\") and (\") ~ \")\"}}\n\n    {% if unique_key %}\n    when matched then update set\n        {% for column_name in update_columns -%}\n            {{ column_name }} = DBT_INTERNAL_SOURCE.{{ column_name }}\n            {%- if not loop.last %}, {%- endif %}\n        {%- endfor %}\n    {% endif %}\n\n    when not matched then insert\n        ({{ dest_cols_csv }})\n    values\n        ({{ dest_cols_csv }})\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv", "macro.dbt.get_merge_update_columns", "macro.dbt.equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.101056, "supported_languages": null}, "macro.dbt.get_delete_insert_merge_sql": {"name": "get_delete_insert_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/merge.sql", "original_file_path": "macros/materializations/models/incremental/merge.sql", "unique_id": "macro.dbt.get_delete_insert_merge_sql", "macro_sql": "{% macro get_delete_insert_merge_sql(target, source, unique_key, dest_columns, incremental_predicates) -%}\n  {{ adapter.dispatch('get_delete_insert_merge_sql', 'dbt')(target, source, unique_key, dest_columns, incremental_predicates) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_delete_insert_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1015463, "supported_languages": null}, "macro.dbt.default__get_delete_insert_merge_sql": {"name": "default__get_delete_insert_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/merge.sql", "original_file_path": "macros/materializations/models/incremental/merge.sql", "unique_id": "macro.dbt.default__get_delete_insert_merge_sql", "macro_sql": "{% macro default__get_delete_insert_merge_sql(target, source, unique_key, dest_columns, incremental_predicates) -%}\n\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n\n    {% if unique_key %}\n        {% if unique_key is string %}\n        {% set unique_key = [unique_key] %}\n        {% endif %}\n\n        {%- set unique_key_str = unique_key|join(', ') -%}\n\n        delete from {{ target }} as DBT_INTERNAL_DEST\n        where ({{ unique_key_str }}) in (\n            select distinct {{ unique_key_str }}\n            from {{ source }} as DBT_INTERNAL_SOURCE\n        )\n        {%- if incremental_predicates %}\n            {% for predicate in incremental_predicates %}\n                and {{ predicate }}\n            {% endfor %}\n        {%- endif -%};\n\n    {% endif %}\n\n    insert into {{ target }} ({{ dest_cols_csv }})\n    (\n        select {{ dest_cols_csv }}\n        from {{ source }}\n    )\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.102843, "supported_languages": null}, "macro.dbt.get_insert_overwrite_merge_sql": {"name": "get_insert_overwrite_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/merge.sql", "original_file_path": "macros/materializations/models/incremental/merge.sql", "unique_id": "macro.dbt.get_insert_overwrite_merge_sql", "macro_sql": "{% macro get_insert_overwrite_merge_sql(target, source, dest_columns, predicates, include_sql_header=false) -%}\n  {{ adapter.dispatch('get_insert_overwrite_merge_sql', 'dbt')(target, source, dest_columns, predicates, include_sql_header) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_insert_overwrite_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1033301, "supported_languages": null}, "macro.dbt.default__get_insert_overwrite_merge_sql": {"name": "default__get_insert_overwrite_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/merge.sql", "original_file_path": "macros/materializations/models/incremental/merge.sql", "unique_id": "macro.dbt.default__get_insert_overwrite_merge_sql", "macro_sql": "{% macro default__get_insert_overwrite_merge_sql(target, source, dest_columns, predicates, include_sql_header) -%}\n    {#-- The only time include_sql_header is True: --#}\n    {#-- BigQuery + insert_overwrite strategy + \"static\" partitions config --#}\n    {#-- We should consider including the sql header at the materialization level instead --#}\n\n    {%- set predicates = [] if predicates is none else [] + predicates -%}\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n    {%- set sql_header = config.get('sql_header', none) -%}\n\n    {{ sql_header if sql_header is not none and include_sql_header }}\n\n    merge into {{ target }} as DBT_INTERNAL_DEST\n        using {{ source }} as DBT_INTERNAL_SOURCE\n        on FALSE\n\n    when not matched by source\n        {% if predicates %} and {{ predicates | join(' and ') }} {% endif %}\n        then delete\n\n    when not matched then insert\n        ({{ dest_cols_csv }})\n    values\n        ({{ dest_cols_csv }})\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.104498, "supported_languages": null}, "macro.dbt.incremental_validate_on_schema_change": {"name": "incremental_validate_on_schema_change", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/on_schema_change.sql", "original_file_path": "macros/materializations/models/incremental/on_schema_change.sql", "unique_id": "macro.dbt.incremental_validate_on_schema_change", "macro_sql": "{% macro incremental_validate_on_schema_change(on_schema_change, default='ignore') %}\n\n   {% if on_schema_change not in ['sync_all_columns', 'append_new_columns', 'fail', 'ignore'] %}\n\n     {% set log_message = 'Invalid value for on_schema_change (%s) specified. Setting default value of %s.' % (on_schema_change, default) %}\n     {% do log(log_message) %}\n\n     {{ return(default) }}\n\n   {% else %}\n\n     {{ return(on_schema_change) }}\n\n   {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1065524, "supported_languages": null}, "macro.dbt.check_for_schema_changes": {"name": "check_for_schema_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/on_schema_change.sql", "original_file_path": "macros/materializations/models/incremental/on_schema_change.sql", "unique_id": "macro.dbt.check_for_schema_changes", "macro_sql": "{% macro check_for_schema_changes(source_relation, target_relation) %}\n\n  {% set schema_changed = False %}\n\n  {%- set source_columns = adapter.get_columns_in_relation(source_relation) -%}\n  {%- set target_columns = adapter.get_columns_in_relation(target_relation) -%}\n  {%- set source_not_in_target = diff_columns(source_columns, target_columns) -%}\n  {%- set target_not_in_source = diff_columns(target_columns, source_columns) -%}\n\n  {% set new_target_types = diff_column_data_types(source_columns, target_columns) %}\n\n  {% if source_not_in_target != [] %}\n    {% set schema_changed = True %}\n  {% elif target_not_in_source != [] or new_target_types != [] %}\n    {% set schema_changed = True %}\n  {% elif new_target_types != [] %}\n    {% set schema_changed = True %}\n  {% endif %}\n\n  {% set changes_dict = {\n    'schema_changed': schema_changed,\n    'source_not_in_target': source_not_in_target,\n    'target_not_in_source': target_not_in_source,\n    'source_columns': source_columns,\n    'target_columns': target_columns,\n    'new_target_types': new_target_types\n  } %}\n\n  {% set msg %}\n    In {{ target_relation }}:\n        Schema changed: {{ schema_changed }}\n        Source columns not in target: {{ source_not_in_target }}\n        Target columns not in source: {{ target_not_in_source }}\n        New column types: {{ new_target_types }}\n  {% endset %}\n\n  {% do log(msg) %}\n\n  {{ return(changes_dict) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.diff_columns", "macro.dbt.diff_column_data_types"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1086729, "supported_languages": null}, "macro.dbt.sync_column_schemas": {"name": "sync_column_schemas", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/on_schema_change.sql", "original_file_path": "macros/materializations/models/incremental/on_schema_change.sql", "unique_id": "macro.dbt.sync_column_schemas", "macro_sql": "{% macro sync_column_schemas(on_schema_change, target_relation, schema_changes_dict) %}\n\n  {%- set add_to_target_arr = schema_changes_dict['source_not_in_target'] -%}\n\n  {%- if on_schema_change == 'append_new_columns'-%}\n     {%- if add_to_target_arr | length > 0 -%}\n       {%- do alter_relation_add_remove_columns(target_relation, add_to_target_arr, none) -%}\n     {%- endif -%}\n\n  {% elif on_schema_change == 'sync_all_columns' %}\n     {%- set remove_from_target_arr = schema_changes_dict['target_not_in_source'] -%}\n     {%- set new_target_types = schema_changes_dict['new_target_types'] -%}\n\n     {% if add_to_target_arr | length > 0 or remove_from_target_arr | length > 0 %}\n       {%- do alter_relation_add_remove_columns(target_relation, add_to_target_arr, remove_from_target_arr) -%}\n     {% endif %}\n\n     {% if new_target_types != [] %}\n       {% for ntt in new_target_types %}\n         {% set column_name = ntt['column_name'] %}\n         {% set new_type = ntt['new_type'] %}\n         {% do alter_column_type(target_relation, column_name, new_type) %}\n       {% endfor %}\n     {% endif %}\n\n  {% endif %}\n\n  {% set schema_change_message %}\n    In {{ target_relation }}:\n        Schema change approach: {{ on_schema_change }}\n        Columns added: {{ add_to_target_arr }}\n        Columns removed: {{ remove_from_target_arr }}\n        Data types changed: {{ new_target_types }}\n  {% endset %}\n\n  {% do log(schema_change_message) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.alter_relation_add_remove_columns", "macro.dbt.alter_column_type"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1109138, "supported_languages": null}, "macro.dbt.process_schema_changes": {"name": "process_schema_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/on_schema_change.sql", "original_file_path": "macros/materializations/models/incremental/on_schema_change.sql", "unique_id": "macro.dbt.process_schema_changes", "macro_sql": "{% macro process_schema_changes(on_schema_change, source_relation, target_relation) %}\n\n    {% if on_schema_change == 'ignore' %}\n\n     {{ return({}) }}\n\n    {% else %}\n\n      {% set schema_changes_dict = check_for_schema_changes(source_relation, target_relation) %}\n\n      {% if schema_changes_dict['schema_changed'] %}\n\n        {% if on_schema_change == 'fail' %}\n\n          {% set fail_msg %}\n              The source and target schemas on this incremental model are out of sync!\n              They can be reconciled in several ways:\n                - set the `on_schema_change` config to either append_new_columns or sync_all_columns, depending on your situation.\n                - Re-run the incremental model with `full_refresh: True` to update the target schema.\n                - update the schema manually and re-run the process.\n\n              Additional troubleshooting context:\n                 Source columns not in target: {{ schema_changes_dict['source_not_in_target'] }}\n                 Target columns not in source: {{ schema_changes_dict['target_not_in_source'] }}\n                 New column types: {{ schema_changes_dict['new_target_types'] }}\n          {% endset %}\n\n          {% do exceptions.raise_compiler_error(fail_msg) %}\n\n        {# -- unless we ignore, run the sync operation per the config #}\n        {% else %}\n\n          {% do sync_column_schemas(on_schema_change, target_relation, schema_changes_dict) %}\n\n        {% endif %}\n\n      {% endif %}\n\n      {{ return(schema_changes_dict['source_columns']) }}\n\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.check_for_schema_changes", "macro.dbt.sync_column_schemas"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1124353, "supported_languages": null}, "macro.dbt.get_incremental_append_sql": {"name": "get_incremental_append_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.get_incremental_append_sql", "macro_sql": "{% macro get_incremental_append_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_append_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_append_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1135695, "supported_languages": null}, "macro.dbt.default__get_incremental_append_sql": {"name": "default__get_incremental_append_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.default__get_incremental_append_sql", "macro_sql": "{% macro default__get_incremental_append_sql(arg_dict) %}\n\n  {% do return(get_insert_into_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"dest_columns\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_insert_into_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.114, "supported_languages": null}, "macro.dbt.get_incremental_delete_insert_sql": {"name": "get_incremental_delete_insert_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.get_incremental_delete_insert_sql", "macro_sql": "{% macro get_incremental_delete_insert_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_delete_insert_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_delete_insert_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1143436, "supported_languages": null}, "macro.dbt.default__get_incremental_delete_insert_sql": {"name": "default__get_incremental_delete_insert_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.default__get_incremental_delete_insert_sql", "macro_sql": "{% macro default__get_incremental_delete_insert_sql(arg_dict) %}\n\n  {% do return(get_delete_insert_merge_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"unique_key\"], arg_dict[\"dest_columns\"], arg_dict[\"incremental_predicates\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_delete_insert_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1148686, "supported_languages": null}, "macro.dbt.get_incremental_merge_sql": {"name": "get_incremental_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.get_incremental_merge_sql", "macro_sql": "{% macro get_incremental_merge_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_merge_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1152112, "supported_languages": null}, "macro.dbt.default__get_incremental_merge_sql": {"name": "default__get_incremental_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.default__get_incremental_merge_sql", "macro_sql": "{% macro default__get_incremental_merge_sql(arg_dict) %}\n\n  {% do return(get_merge_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"unique_key\"], arg_dict[\"dest_columns\"], arg_dict[\"incremental_predicates\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1157434, "supported_languages": null}, "macro.dbt.get_incremental_insert_overwrite_sql": {"name": "get_incremental_insert_overwrite_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.get_incremental_insert_overwrite_sql", "macro_sql": "{% macro get_incremental_insert_overwrite_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_insert_overwrite_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_insert_overwrite_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1160862, "supported_languages": null}, "macro.dbt.default__get_incremental_insert_overwrite_sql": {"name": "default__get_incremental_insert_overwrite_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.default__get_incremental_insert_overwrite_sql", "macro_sql": "{% macro default__get_incremental_insert_overwrite_sql(arg_dict) %}\n\n  {% do return(get_insert_overwrite_merge_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"dest_columns\"], arg_dict[\"incremental_predicates\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_insert_overwrite_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1165617, "supported_languages": null}, "macro.dbt.get_incremental_default_sql": {"name": "get_incremental_default_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.get_incremental_default_sql", "macro_sql": "{% macro get_incremental_default_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_default_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_incremental_default_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.116902, "supported_languages": null}, "macro.dbt.default__get_incremental_default_sql": {"name": "default__get_incremental_default_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.default__get_incremental_default_sql", "macro_sql": "{% macro default__get_incremental_default_sql(arg_dict) %}\n\n  {% do return(get_incremental_append_sql(arg_dict)) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_incremental_append_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.117186, "supported_languages": null}, "macro.dbt.get_incremental_microbatch_sql": {"name": "get_incremental_microbatch_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.get_incremental_microbatch_sql", "macro_sql": "{% macro get_incremental_microbatch_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_microbatch_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_incremental_microbatch_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.117524, "supported_languages": null}, "macro.dbt.default__get_incremental_microbatch_sql": {"name": "default__get_incremental_microbatch_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.default__get_incremental_microbatch_sql", "macro_sql": "{% macro default__get_incremental_microbatch_sql(arg_dict) %}\n\n  {{ exceptions.raise_not_implemented('microbatch materialization strategy not implemented for adapter ' + adapter.type()) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1178286, "supported_languages": null}, "macro.dbt.get_insert_into_sql": {"name": "get_insert_into_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/models/incremental/strategies.sql", "original_file_path": "macros/materializations/models/incremental/strategies.sql", "unique_id": "macro.dbt.get_insert_into_sql", "macro_sql": "{% macro get_insert_into_sql(target_relation, temp_relation, dest_columns) %}\n\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n\n    insert into {{ target_relation }} ({{ dest_cols_csv }})\n    (\n        select {{ dest_cols_csv }}\n        from {{ temp_relation }}\n    )\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1183386, "supported_languages": null}, "macro.dbt.create_csv_table": {"name": "create_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.create_csv_table", "macro_sql": "{% macro create_csv_table(model, agate_table) -%}\n  {{ adapter.dispatch('create_csv_table', 'dbt')(model, agate_table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_csv_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1200614, "supported_languages": null}, "macro.dbt.default__create_csv_table": {"name": "default__create_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.default__create_csv_table", "macro_sql": "{% macro default__create_csv_table(model, agate_table) %}\n  {%- set column_override = model['config'].get('column_types', {}) -%}\n  {%- set quote_seed_column = model['config'].get('quote_columns', None) -%}\n\n  {% set sql %}\n    create table {{ this.render() }} (\n        {%- for col_name in agate_table.column_names -%}\n            {%- set inferred_type = adapter.convert_type(agate_table, loop.index0) -%}\n            {%- set type = column_override.get(col_name, inferred_type) -%}\n            {%- set column_name = (col_name | string) -%}\n            {{ adapter.quote_seed_column(column_name, quote_seed_column) }} {{ type }} {%- if not loop.last -%}, {%- endif -%}\n        {%- endfor -%}\n    )\n  {% endset %}\n\n  {% call statement('_') -%}\n    {{ sql }}\n  {%- endcall %}\n\n  {{ return(sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1217625, "supported_languages": null}, "macro.dbt.reset_csv_table": {"name": "reset_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.reset_csv_table", "macro_sql": "{% macro reset_csv_table(model, full_refresh, old_relation, agate_table) -%}\n  {{ adapter.dispatch('reset_csv_table', 'dbt')(model, full_refresh, old_relation, agate_table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__reset_csv_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.122202, "supported_languages": null}, "macro.dbt.default__reset_csv_table": {"name": "default__reset_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.default__reset_csv_table", "macro_sql": "{% macro default__reset_csv_table(model, full_refresh, old_relation, agate_table) %}\n    {% set sql = \"\" %}\n    {% if full_refresh %}\n        {{ adapter.drop_relation(old_relation) }}\n        {% set sql = create_csv_table(model, agate_table) %}\n    {% else %}\n        {{ adapter.truncate_relation(old_relation) }}\n        {% set sql = \"truncate table \" ~ old_relation.render() %}\n    {% endif %}\n\n    {{ return(sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.create_csv_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1230907, "supported_languages": null}, "macro.dbt.get_csv_sql": {"name": "get_csv_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.get_csv_sql", "macro_sql": "{% macro get_csv_sql(create_or_truncate_sql, insert_sql) %}\n    {{ adapter.dispatch('get_csv_sql', 'dbt')(create_or_truncate_sql, insert_sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_csv_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1234493, "supported_languages": null}, "macro.dbt.default__get_csv_sql": {"name": "default__get_csv_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.default__get_csv_sql", "macro_sql": "{% macro default__get_csv_sql(create_or_truncate_sql, insert_sql) %}\n    {{ create_or_truncate_sql }};\n    -- dbt seed --\n    {{ insert_sql }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1237214, "supported_languages": null}, "macro.dbt.get_binding_char": {"name": "get_binding_char", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.get_binding_char", "macro_sql": "{% macro get_binding_char() -%}\n  {{ adapter.dispatch('get_binding_char', 'dbt')() }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_binding_char"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1239913, "supported_languages": null}, "macro.dbt.default__get_binding_char": {"name": "default__get_binding_char", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.default__get_binding_char", "macro_sql": "{% macro default__get_binding_char() %}\n  {{ return('%s') }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1242151, "supported_languages": null}, "macro.dbt.get_batch_size": {"name": "get_batch_size", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.get_batch_size", "macro_sql": "{% macro get_batch_size() -%}\n  {{ return(adapter.dispatch('get_batch_size', 'dbt')()) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_batch_size"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1245103, "supported_languages": null}, "macro.dbt.default__get_batch_size": {"name": "default__get_batch_size", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.default__get_batch_size", "macro_sql": "{% macro default__get_batch_size() %}\n  {{ return(10000) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1247358, "supported_languages": null}, "macro.dbt.get_seed_column_quoted_csv": {"name": "get_seed_column_quoted_csv", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.get_seed_column_quoted_csv", "macro_sql": "{% macro get_seed_column_quoted_csv(model, column_names) %}\n  {%- set quote_seed_column = model['config'].get('quote_columns', None) -%}\n    {% set quoted = [] %}\n    {% for col in column_names -%}\n        {%- do quoted.append(adapter.quote_seed_column(col, quote_seed_column)) -%}\n    {%- endfor %}\n\n    {%- set dest_cols_csv = quoted | join(', ') -%}\n    {{ return(dest_cols_csv) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1255949, "supported_languages": null}, "macro.dbt.load_csv_rows": {"name": "load_csv_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.load_csv_rows", "macro_sql": "{% macro load_csv_rows(model, agate_table) -%}\n  {{ adapter.dispatch('load_csv_rows', 'dbt')(model, agate_table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__load_csv_rows"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1259432, "supported_languages": null}, "macro.dbt.default__load_csv_rows": {"name": "default__load_csv_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/helpers.sql", "original_file_path": "macros/materializations/seeds/helpers.sql", "unique_id": "macro.dbt.default__load_csv_rows", "macro_sql": "{% macro default__load_csv_rows(model, agate_table) %}\n\n  {% set batch_size = get_batch_size() %}\n\n  {% set cols_sql = get_seed_column_quoted_csv(model, agate_table.column_names) %}\n  {% set bindings = [] %}\n\n  {% set statements = [] %}\n\n  {% for chunk in agate_table.rows | batch(batch_size) %}\n      {% set bindings = [] %}\n\n      {% for row in chunk %}\n          {% do bindings.extend(row) %}\n      {% endfor %}\n\n      {% set sql %}\n          insert into {{ this.render() }} ({{ cols_sql }}) values\n          {% for row in chunk -%}\n              ({%- for column in agate_table.column_names -%}\n                  {{ get_binding_char() }}\n                  {%- if not loop.last%},{%- endif %}\n              {%- endfor -%})\n              {%- if not loop.last%},{%- endif %}\n          {%- endfor %}\n      {% endset %}\n\n      {% do adapter.add_query(sql, bindings=bindings, abridge_sql_log=True) %}\n\n      {% if loop.index0 == 0 %}\n          {% do statements.append(sql) %}\n      {% endif %}\n  {% endfor %}\n\n  {# Return SQL so we can render it out into the compiled files #}\n  {{ return(statements[0]) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_batch_size", "macro.dbt.get_seed_column_quoted_csv", "macro.dbt.get_binding_char"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1280332, "supported_languages": null}, "macro.dbt.materialization_seed_default": {"name": "materialization_seed_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/seeds/seed.sql", "original_file_path": "macros/materializations/seeds/seed.sql", "unique_id": "macro.dbt.materialization_seed_default", "macro_sql": "{% materialization seed, default %}\n\n  {%- set identifier = model['alias'] -%}\n  {%- set full_refresh_mode = (should_full_refresh()) -%}\n\n  {%- set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) -%}\n\n  {%- set exists_as_table = (old_relation is not none and old_relation.is_table) -%}\n  {%- set exists_as_view = (old_relation is not none and old_relation.is_view) -%}\n\n  {%- set grant_config = config.get('grants') -%}\n  {%- set agate_table = load_agate_table() -%}\n  -- grab current tables grants config for comparison later on\n\n  {%- do store_result('agate_table', response='OK', agate_table=agate_table) -%}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  -- build model\n  {% set create_table_sql = \"\" %}\n  {% if exists_as_view %}\n    {{ exceptions.raise_compiler_error(\"Cannot seed to '{}', it is a view\".format(old_relation.render())) }}\n  {% elif exists_as_table %}\n    {% set create_table_sql = reset_csv_table(model, full_refresh_mode, old_relation, agate_table) %}\n  {% else %}\n    {% set create_table_sql = create_csv_table(model, agate_table) %}\n  {% endif %}\n\n  {% set code = 'CREATE' if full_refresh_mode else 'INSERT' %}\n  {% set rows_affected = (agate_table.rows | length) %}\n  {% set sql = load_csv_rows(model, agate_table) %}\n\n  {% call noop_statement('main', code ~ ' ' ~ rows_affected, code, rows_affected) %}\n    {{ get_csv_sql(create_table_sql, sql) }};\n  {% endcall %}\n\n  {% set target_relation = this.incorporate(type='table') %}\n\n  {% set should_revoke = should_revoke(old_relation, full_refresh_mode) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {% if full_refresh_mode or not exists_as_table %}\n    {% do create_indexes(target_relation) %}\n  {% endif %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  -- `COMMIT` happens here\n  {{ adapter.commit() }}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.should_full_refresh", "macro.dbt.run_hooks", "macro.dbt.reset_csv_table", "macro.dbt.create_csv_table", "macro.dbt.load_csv_rows", "macro.dbt.noop_statement", "macro.dbt.get_csv_sql", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs", "macro.dbt.create_indexes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1328022, "supported_languages": ["sql"]}, "macro.dbt.create_columns": {"name": "create_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.create_columns", "macro_sql": "{% macro create_columns(relation, columns) %}\n  {{ adapter.dispatch('create_columns', 'dbt')(relation, columns) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.136423, "supported_languages": null}, "macro.dbt.default__create_columns": {"name": "default__create_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.default__create_columns", "macro_sql": "{% macro default__create_columns(relation, columns) %}\n  {% for column in columns %}\n    {% call statement() %}\n      alter table {{ relation.render() }} add column {{ adapter.quote(column.name) }} {{ column.data_type }};\n    {% endcall %}\n  {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1370184, "supported_languages": null}, "macro.dbt.post_snapshot": {"name": "post_snapshot", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.post_snapshot", "macro_sql": "{% macro post_snapshot(staging_relation) %}\n  {{ adapter.dispatch('post_snapshot', 'dbt')(staging_relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__post_snapshot"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1373305, "supported_languages": null}, "macro.dbt.default__post_snapshot": {"name": "default__post_snapshot", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.default__post_snapshot", "macro_sql": "{% macro default__post_snapshot(staging_relation) %}\n    {# no-op #}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.13751, "supported_languages": null}, "macro.dbt.get_true_sql": {"name": "get_true_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.get_true_sql", "macro_sql": "{% macro get_true_sql() %}\n  {{ adapter.dispatch('get_true_sql', 'dbt')() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_true_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.137782, "supported_languages": null}, "macro.dbt.default__get_true_sql": {"name": "default__get_true_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.default__get_true_sql", "macro_sql": "{% macro default__get_true_sql() %}\n    {{ return('TRUE') }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1380055, "supported_languages": null}, "macro.dbt.snapshot_staging_table": {"name": "snapshot_staging_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.snapshot_staging_table", "macro_sql": "{% macro snapshot_staging_table(strategy, source_sql, target_relation) -%}\n  {{ adapter.dispatch('snapshot_staging_table', 'dbt')(strategy, source_sql, target_relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__snapshot_staging_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1383793, "supported_languages": null}, "macro.dbt.get_snapshot_table_column_names": {"name": "get_snapshot_table_column_names", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.get_snapshot_table_column_names", "macro_sql": "{% macro get_snapshot_table_column_names() %}\n    {{ return({'dbt_valid_to': 'dbt_valid_to', 'dbt_valid_from': 'dbt_valid_from', 'dbt_scd_id': 'dbt_scd_id', 'dbt_updated_at': 'dbt_updated_at', 'dbt_is_deleted': 'dbt_is_deleted'}) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1388276, "supported_languages": null}, "macro.dbt.default__snapshot_staging_table": {"name": "default__snapshot_staging_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.default__snapshot_staging_table", "macro_sql": "{% macro default__snapshot_staging_table(strategy, source_sql, target_relation) -%}\n    {% set columns = config.get('snapshot_table_column_names') or get_snapshot_table_column_names() %}\n    {% if strategy.hard_deletes == 'new_record' %}\n        {% set new_scd_id = snapshot_hash_arguments([columns.dbt_scd_id, snapshot_get_time()]) %}\n    {% endif %}\n    with snapshot_query as (\n\n        {{ source_sql }}\n\n    ),\n\n    snapshotted_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }}\n        from {{ target_relation }}\n        where\n            {% if config.get('dbt_valid_to_current') %}\n\t\t{% set source_unique_key = columns.dbt_valid_to | trim %}\n\t\t{% set target_unique_key = config.get('dbt_valid_to_current') | trim %}\n\n\t\t{# The exact equals semantics between NULL values depends on the current behavior flag set. Also, update records if the source field is null #}\n                ( {{ equals(source_unique_key, target_unique_key) }} or {{ source_unique_key }} is null )\n            {% else %}\n                {{ columns.dbt_valid_to }} is null\n            {% endif %}\n\n    ),\n\n    insertions_source_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }},\n            {{ strategy.updated_at }} as {{ columns.dbt_updated_at }},\n            {{ strategy.updated_at }} as {{ columns.dbt_valid_from }},\n            {{ get_dbt_valid_to_current(strategy, columns) }},\n            {{ strategy.scd_id }} as {{ columns.dbt_scd_id }}\n\n        from snapshot_query\n    ),\n\n    updates_source_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }},\n            {{ strategy.updated_at }} as {{ columns.dbt_updated_at }},\n            {{ strategy.updated_at }} as {{ columns.dbt_valid_from }},\n            {{ strategy.updated_at }} as {{ columns.dbt_valid_to }}\n\n        from snapshot_query\n    ),\n\n    {%- if strategy.hard_deletes == 'invalidate' or strategy.hard_deletes == 'new_record' %}\n\n    deletes_source_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }}\n        from snapshot_query\n    ),\n    {% endif %}\n\n    insertions as (\n\n        select\n            'insert' as dbt_change_type,\n            source_data.*\n          {%- if strategy.hard_deletes == 'new_record' -%}\n            ,'False' as {{ columns.dbt_is_deleted }}\n          {%- endif %}\n\n        from insertions_source_data as source_data\n        left outer join snapshotted_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n            where {{ unique_key_is_null(strategy.unique_key, \"snapshotted_data\") }}\n            or ({{ unique_key_is_not_null(strategy.unique_key, \"snapshotted_data\") }} and (\n               {{ strategy.row_changed }} {%- if strategy.hard_deletes == 'new_record' -%} or snapshotted_data.{{ columns.dbt_is_deleted }} = 'True' {% endif %}\n            )\n\n        )\n\n    ),\n\n    updates as (\n\n        select\n            'update' as dbt_change_type,\n            source_data.*,\n            snapshotted_data.{{ columns.dbt_scd_id }}\n          {%- if strategy.hard_deletes == 'new_record' -%}\n            , snapshotted_data.{{ columns.dbt_is_deleted }}\n          {%- endif %}\n\n        from updates_source_data as source_data\n        join snapshotted_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n        where (\n            {{ strategy.row_changed }}  {%- if strategy.hard_deletes == 'new_record' -%} or snapshotted_data.{{ columns.dbt_is_deleted }} = 'True' {% endif %}\n        )\n    )\n\n    {%- if strategy.hard_deletes == 'invalidate' or strategy.hard_deletes == 'new_record' %}\n    ,\n    deletes as (\n\n        select\n            'delete' as dbt_change_type,\n            source_data.*,\n            {{ snapshot_get_time() }} as {{ columns.dbt_valid_from }},\n            {{ snapshot_get_time() }} as {{ columns.dbt_updated_at }},\n            {{ snapshot_get_time() }} as {{ columns.dbt_valid_to }},\n            snapshotted_data.{{ columns.dbt_scd_id }}\n          {%- if strategy.hard_deletes == 'new_record' -%}\n            , snapshotted_data.{{ columns.dbt_is_deleted }}\n          {%- endif %}\n        from snapshotted_data\n        left join deletes_source_data as source_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n            where {{ unique_key_is_null(strategy.unique_key, \"source_data\") }}\n\n            {%- if strategy.hard_deletes == 'new_record' %}\n            and not (\n                --avoid updating the record's valid_to if the latest entry is marked as deleted\n                snapshotted_data.{{ columns.dbt_is_deleted }} = 'True'\n                and snapshotted_data.{{ columns.dbt_valid_to }} is null\n            )\n            {%- endif %}\n    )\n    {%- endif %}\n\n    {%- if strategy.hard_deletes == 'new_record' %}\n        {% set snapshotted_cols = get_list_of_column_names(get_columns_in_relation(target_relation)) %}\n        {% set source_sql_cols = get_column_schema_from_query(source_sql) %}\n    ,\n    deletion_records as (\n\n        select\n            'insert' as dbt_change_type,\n            {#\n                If a column has been added to the source it won't yet exist in the\n                snapshotted table so we insert a null value as a placeholder for the column.\n             #}\n            {%- for col in source_sql_cols -%}\n            {%- if col.name in snapshotted_cols -%}\n            snapshotted_data.{{ adapter.quote(col.column) }},\n            {%- else -%}\n            NULL as {{ adapter.quote(col.column) }},\n            {%- endif -%}\n            {% endfor -%}\n            {%- if strategy.unique_key | is_list -%}\n                {%- for key in strategy.unique_key -%}\n            snapshotted_data.{{ key }} as dbt_unique_key_{{ loop.index }},\n                {% endfor -%}\n            {%- else -%}\n            snapshotted_data.dbt_unique_key as dbt_unique_key,\n            {% endif -%}\n            {{ snapshot_get_time() }} as {{ columns.dbt_valid_from }},\n            {{ snapshot_get_time() }} as {{ columns.dbt_updated_at }},\n            snapshotted_data.{{ columns.dbt_valid_to }} as {{ columns.dbt_valid_to }},\n            {{ new_scd_id }} as {{ columns.dbt_scd_id }},\n            'True' as {{ columns.dbt_is_deleted }}\n        from snapshotted_data\n        left join deletes_source_data as source_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n        where {{ unique_key_is_null(strategy.unique_key, \"source_data\") }}\n        and not (\n            --avoid inserting a new record if the latest one is marked as deleted\n            snapshotted_data.{{ columns.dbt_is_deleted }} = 'True'\n            and snapshotted_data.{{ columns.dbt_valid_to }} is null\n            )\n\n    )\n    {%- endif %}\n\n    select * from insertions\n    union all\n    select * from updates\n    {%- if strategy.hard_deletes == 'invalidate' or strategy.hard_deletes == 'new_record' %}\n    union all\n    select * from deletes\n    {%- endif %}\n    {%- if strategy.hard_deletes == 'new_record' %}\n    union all\n    select * from deletion_records\n    {%- endif %}\n\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.snapshot_hash_arguments", "macro.dbt.snapshot_get_time", "macro.dbt.unique_key_fields", "macro.dbt.equals", "macro.dbt.get_dbt_valid_to_current", "macro.dbt.unique_key_join_on", "macro.dbt.unique_key_is_null", "macro.dbt.unique_key_is_not_null", "macro.dbt.get_list_of_column_names", "macro.dbt.get_columns_in_relation", "macro.dbt.get_column_schema_from_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.19304, "supported_languages": null}, "macro.dbt.build_snapshot_table": {"name": "build_snapshot_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.build_snapshot_table", "macro_sql": "{% macro build_snapshot_table(strategy, sql) -%}\n  {{ adapter.dispatch('build_snapshot_table', 'dbt')(strategy, sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__build_snapshot_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.193495, "supported_languages": null}, "macro.dbt.default__build_snapshot_table": {"name": "default__build_snapshot_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.default__build_snapshot_table", "macro_sql": "{% macro default__build_snapshot_table(strategy, sql) %}\n    {% set columns = config.get('snapshot_table_column_names') or get_snapshot_table_column_names() %}\n\n    select *,\n        {{ strategy.scd_id }} as {{ columns.dbt_scd_id }},\n        {{ strategy.updated_at }} as {{ columns.dbt_updated_at }},\n        {{ strategy.updated_at }} as {{ columns.dbt_valid_from }},\n        {{ get_dbt_valid_to_current(strategy, columns) }}\n      {%- if strategy.hard_deletes == 'new_record' -%}\n        , 'False' as {{ columns.dbt_is_deleted }}\n      {% endif -%}\n    from (\n        {{ sql }}\n    ) sbq\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.get_dbt_valid_to_current"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1944385, "supported_languages": null}, "macro.dbt.build_snapshot_staging_table": {"name": "build_snapshot_staging_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.build_snapshot_staging_table", "macro_sql": "{% macro build_snapshot_staging_table(strategy, sql, target_relation) %}\n    {% set temp_relation = make_temp_relation(target_relation) %}\n\n    {% set select = snapshot_staging_table(strategy, sql, target_relation) %}\n\n    {% call statement('build_snapshot_staging_relation') %}\n        {{ create_table_as(True, temp_relation, select) }}\n    {% endcall %}\n\n    {% do return(temp_relation) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_temp_relation", "macro.dbt.snapshot_staging_table", "macro.dbt.statement", "macro.dbt.create_table_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1952264, "supported_languages": null}, "macro.dbt.get_updated_at_column_data_type": {"name": "get_updated_at_column_data_type", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.get_updated_at_column_data_type", "macro_sql": "{% macro get_updated_at_column_data_type(snapshot_sql) %}\n    {% set snapshot_sql_column_schema = get_column_schema_from_query(snapshot_sql) %}\n    {% set dbt_updated_at_data_type = null %}\n    {% set ns = namespace() -%} {#-- handle for-loop scoping with a namespace --#}\n    {% set ns.dbt_updated_at_data_type = null -%}\n    {% for column in snapshot_sql_column_schema %}\n    {%   if ((column.column == 'dbt_updated_at') or (column.column == 'DBT_UPDATED_AT')) %}\n    {%     set ns.dbt_updated_at_data_type = column.dtype %}\n    {%   endif %}\n    {% endfor %}\n    {{ return(ns.dbt_updated_at_data_type or none)  }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_column_schema_from_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1963193, "supported_languages": null}, "macro.dbt.check_time_data_types": {"name": "check_time_data_types", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.check_time_data_types", "macro_sql": "{% macro check_time_data_types(sql) %}\n  {% set dbt_updated_at_data_type = get_updated_at_column_data_type(sql) %}\n  {% set snapshot_get_time_data_type = get_snapshot_get_time_data_type() %}\n  {% if snapshot_get_time_data_type is not none and dbt_updated_at_data_type is not none and snapshot_get_time_data_type != dbt_updated_at_data_type %}\n  {%   if exceptions.warn_snapshot_timestamp_data_types %}\n  {{     exceptions.warn_snapshot_timestamp_data_types(snapshot_get_time_data_type, dbt_updated_at_data_type) }}\n  {%   endif %}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_updated_at_column_data_type", "macro.dbt.get_snapshot_get_time_data_type"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.197109, "supported_languages": null}, "macro.dbt.get_dbt_valid_to_current": {"name": "get_dbt_valid_to_current", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.get_dbt_valid_to_current", "macro_sql": "{% macro get_dbt_valid_to_current(strategy, columns) %}\n  {% set dbt_valid_to_current = config.get('dbt_valid_to_current') or \"null\" %}\n  coalesce(nullif({{ strategy.updated_at }}, {{ strategy.updated_at }}), {{dbt_valid_to_current}})\n  as {{ columns.dbt_valid_to }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1976292, "supported_languages": null}, "macro.dbt.unique_key_fields": {"name": "unique_key_fields", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.unique_key_fields", "macro_sql": "{% macro unique_key_fields(unique_key) %}\n    {% if unique_key | is_list %}\n        {% for key in unique_key %}\n            {{ key }} as dbt_unique_key_{{ loop.index }}\n            {%- if not loop.last %} , {%- endif %}\n        {% endfor %}\n    {% else %}\n        {{ unique_key }} as dbt_unique_key\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.198265, "supported_languages": null}, "macro.dbt.unique_key_join_on": {"name": "unique_key_join_on", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.unique_key_join_on", "macro_sql": "{% macro unique_key_join_on(unique_key, identifier, from_identifier) %}\n    {% if unique_key | is_list %}\n        {% for key in unique_key %}\n\t    {% set source_unique_key = (identifier ~ \".dbt_unique_key_\" ~ loop.index) | trim %}\n\t    {% set target_unique_key = (from_identifier ~ \".dbt_unique_key_\" ~ loop.index) | trim %}\n\t    {{ equals(source_unique_key, target_unique_key) }}\n            {%- if not loop.last %} and {%- endif %}\n        {% endfor %}\n    {% else %}\n        {{ identifier }}.dbt_unique_key = {{ from_identifier }}.dbt_unique_key\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1993353, "supported_languages": null}, "macro.dbt.unique_key_is_null": {"name": "unique_key_is_null", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.unique_key_is_null", "macro_sql": "{% macro unique_key_is_null(unique_key, identifier) %}\n    {% if unique_key | is_list %}\n        {{ identifier }}.dbt_unique_key_1 is null\n    {% else %}\n        {{ identifier }}.dbt_unique_key is null\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.1997607, "supported_languages": null}, "macro.dbt.unique_key_is_not_null": {"name": "unique_key_is_not_null", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/helpers.sql", "original_file_path": "macros/materializations/snapshots/helpers.sql", "unique_id": "macro.dbt.unique_key_is_not_null", "macro_sql": "{% macro unique_key_is_not_null(unique_key, identifier) %}\n    {% if unique_key | is_list %}\n        {{ identifier }}.dbt_unique_key_1 is not null\n    {% else %}\n        {{ identifier }}.dbt_unique_key is not null\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2002242, "supported_languages": null}, "macro.dbt.materialization_snapshot_default": {"name": "materialization_snapshot_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/snapshot.sql", "original_file_path": "macros/materializations/snapshots/snapshot.sql", "unique_id": "macro.dbt.materialization_snapshot_default", "macro_sql": "{% materialization snapshot, default %}\n\n  {%- set target_table = model.get('alias', model.get('name')) -%}\n\n  {%- set strategy_name = config.get('strategy') -%}\n  {%- set unique_key = config.get('unique_key') %}\n  -- grab current tables grants config for comparision later on\n  {%- set grant_config = config.get('grants') -%}\n\n  {% set target_relation_exists, target_relation = get_or_create_relation(\n          database=model.database,\n          schema=model.schema,\n          identifier=target_table,\n          type='table') -%}\n\n  {%- if not target_relation.is_table -%}\n    {% do exceptions.relation_wrong_type(target_relation, 'table') %}\n  {%- endif -%}\n\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  {% set strategy_macro = strategy_dispatch(strategy_name) %}\n  {# The model['config'] parameter below is no longer used, but passing anyway for compatibility #}\n  {# It was a dictionary of config, instead of the config object from the context #}\n  {% set strategy = strategy_macro(model, \"snapshotted_data\", \"source_data\", model['config'], target_relation_exists) %}\n\n  {% if not target_relation_exists %}\n\n      {% set build_sql = build_snapshot_table(strategy, model['compiled_code']) %}\n      {% set build_or_select_sql = build_sql %}\n      {% set final_sql = create_table_as(False, target_relation, build_sql) %}\n\n  {% else %}\n\n      {% set columns = config.get(\"snapshot_table_column_names\") or get_snapshot_table_column_names() %}\n\n      {{ adapter.assert_valid_snapshot_target_given_strategy(target_relation, columns, strategy) }}\n\n      {% set build_or_select_sql = snapshot_staging_table(strategy, sql, target_relation) %}\n      {% set staging_table = build_snapshot_staging_table(strategy, sql, target_relation) %}\n\n      -- this may no-op if the database does not require column expansion\n      {% do adapter.expand_target_column_types(from_relation=staging_table,\n                                               to_relation=target_relation) %}\n\n      {% set remove_columns = ['dbt_change_type', 'DBT_CHANGE_TYPE', 'dbt_unique_key', 'DBT_UNIQUE_KEY'] %}\n      {% if unique_key | is_list %}\n          {% for key in strategy.unique_key %}\n              {{ remove_columns.append('dbt_unique_key_' + loop.index|string) }}\n              {{ remove_columns.append('DBT_UNIQUE_KEY_' + loop.index|string) }}\n          {% endfor %}\n      {% endif %}\n\n      {% set missing_columns = adapter.get_missing_columns(staging_table, target_relation)\n                                   | rejectattr('name', 'in', remove_columns)\n                                   | list %}\n\n      {% do create_columns(target_relation, missing_columns) %}\n\n      {% set source_columns = adapter.get_columns_in_relation(staging_table)\n                                   | rejectattr('name', 'in', remove_columns)\n                                   | list %}\n\n      {% set quoted_source_columns = [] %}\n      {% for column in source_columns %}\n        {% do quoted_source_columns.append(adapter.quote(column.name)) %}\n      {% endfor %}\n\n      {% set final_sql = snapshot_merge_sql(\n            target = target_relation,\n            source = staging_table,\n            insert_cols = quoted_source_columns\n         )\n      %}\n\n  {% endif %}\n\n\n  {{ check_time_data_types(build_or_select_sql) }}\n\n  {% call statement('main') %}\n      {{ final_sql }}\n  {% endcall %}\n\n  {% set should_revoke = should_revoke(target_relation_exists, full_refresh_mode=False) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {% if not target_relation_exists %}\n    {% do create_indexes(target_relation) %}\n  {% endif %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  {{ adapter.commit() }}\n\n  {% if staging_table is defined %}\n      {% do post_snapshot(staging_table) %}\n  {% endif %}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.get_or_create_relation", "macro.dbt.run_hooks", "macro.dbt.strategy_dispatch", "macro.dbt.build_snapshot_table", "macro.dbt.create_table_as", "macro.dbt.get_snapshot_table_column_names", "macro.dbt.snapshot_staging_table", "macro.dbt.build_snapshot_staging_table", "macro.dbt.create_columns", "macro.dbt.snapshot_merge_sql", "macro.dbt.check_time_data_types", "macro.dbt.statement", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs", "macro.dbt.create_indexes", "macro.dbt.post_snapshot"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2074296, "supported_languages": ["sql"]}, "macro.dbt.snapshot_merge_sql": {"name": "snapshot_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/snapshot_merge.sql", "original_file_path": "macros/materializations/snapshots/snapshot_merge.sql", "unique_id": "macro.dbt.snapshot_merge_sql", "macro_sql": "{% macro snapshot_merge_sql(target, source, insert_cols) -%}\n  {{ adapter.dispatch('snapshot_merge_sql', 'dbt')(target, source, insert_cols) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__snapshot_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2082813, "supported_languages": null}, "macro.dbt.default__snapshot_merge_sql": {"name": "default__snapshot_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/snapshot_merge.sql", "original_file_path": "macros/materializations/snapshots/snapshot_merge.sql", "unique_id": "macro.dbt.default__snapshot_merge_sql", "macro_sql": "{% macro default__snapshot_merge_sql(target, source, insert_cols) -%}\n    {%- set insert_cols_csv = insert_cols | join(', ') -%}\n\n    {%- set columns = config.get(\"snapshot_table_column_names\") or get_snapshot_table_column_names() -%}\n\n    merge into {{ target.render() }} as DBT_INTERNAL_DEST\n    using {{ source }} as DBT_INTERNAL_SOURCE\n    on DBT_INTERNAL_SOURCE.{{ columns.dbt_scd_id }} = DBT_INTERNAL_DEST.{{ columns.dbt_scd_id }}\n\n    when matched\n     {% if config.get(\"dbt_valid_to_current\") %}\n\t{% set source_unique_key = (\"DBT_INTERNAL_DEST.\" ~ columns.dbt_valid_to) | trim %}\n\t{% set target_unique_key = config.get('dbt_valid_to_current') | trim %}\n\tand ({{ equals(source_unique_key, target_unique_key) }} or {{ source_unique_key }} is null)\n\n     {% else %}\n       and DBT_INTERNAL_DEST.{{ columns.dbt_valid_to }} is null\n     {% endif %}\n     and DBT_INTERNAL_SOURCE.dbt_change_type in ('update', 'delete')\n        then update\n        set {{ columns.dbt_valid_to }} = DBT_INTERNAL_SOURCE.{{ columns.dbt_valid_to }}\n\n    when not matched\n     and DBT_INTERNAL_SOURCE.dbt_change_type = 'insert'\n        then insert ({{ insert_cols_csv }})\n        values ({{ insert_cols_csv }})\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2097988, "supported_languages": null}, "macro.dbt.strategy_dispatch": {"name": "strategy_dispatch", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.strategy_dispatch", "macro_sql": "{% macro strategy_dispatch(name) -%}\n{% set original_name = name %}\n  {% if '.' in name %}\n    {% set package_name, name = name.split(\".\", 1) %}\n  {% else %}\n    {% set package_name = none %}\n  {% endif %}\n\n  {% if package_name is none %}\n    {% set package_context = context %}\n  {% elif package_name in context %}\n    {% set package_context = context[package_name] %}\n  {% else %}\n    {% set error_msg %}\n        Could not find package '{{package_name}}', called with '{{original_name}}'\n    {% endset %}\n    {{ exceptions.raise_compiler_error(error_msg | trim) }}\n  {% endif %}\n\n  {%- set search_name = 'snapshot_' ~ name ~ '_strategy' -%}\n\n  {% if search_name not in package_context %}\n    {% set error_msg %}\n        The specified strategy macro '{{name}}' was not found in package '{{ package_name }}'\n    {% endset %}\n    {{ exceptions.raise_compiler_error(error_msg | trim) }}\n  {% endif %}\n  {{ return(package_context[search_name]) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2134707, "supported_languages": null}, "macro.dbt.snapshot_hash_arguments": {"name": "snapshot_hash_arguments", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.snapshot_hash_arguments", "macro_sql": "{% macro snapshot_hash_arguments(args) -%}\n  {{ adapter.dispatch('snapshot_hash_arguments', 'dbt')(args) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__snapshot_hash_arguments"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2138042, "supported_languages": null}, "macro.dbt.default__snapshot_hash_arguments": {"name": "default__snapshot_hash_arguments", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.default__snapshot_hash_arguments", "macro_sql": "{% macro default__snapshot_hash_arguments(args) -%}\n    md5({%- for arg in args -%}\n        coalesce(cast({{ arg }} as varchar ), '')\n        {% if not loop.last %} || '|' || {% endif %}\n    {%- endfor -%})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2142262, "supported_languages": null}, "macro.dbt.snapshot_timestamp_strategy": {"name": "snapshot_timestamp_strategy", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.snapshot_timestamp_strategy", "macro_sql": "{% macro snapshot_timestamp_strategy(node, snapshotted_rel, current_rel, model_config, target_exists) %}\n    {# The model_config parameter is no longer used, but is passed in anyway for compatibility. #}\n    {% set primary_key = config.get('unique_key') %}\n    {% set updated_at = config.get('updated_at') %}\n    {% set hard_deletes = adapter.get_hard_deletes_behavior(config) %}\n    {% set invalidate_hard_deletes = hard_deletes == 'invalidate' %}\n    {% set columns = config.get(\"snapshot_table_column_names\") or get_snapshot_table_column_names() %}\n\n    {#/*\n        The snapshot relation might not have an {{ updated_at }} value if the\n        snapshot strategy is changed from `check` to `timestamp`. We\n        should use a dbt-created column for the comparison in the snapshot\n        table instead of assuming that the user-supplied {{ updated_at }}\n        will be present in the historical data.\n\n        See https://github.com/dbt-labs/dbt-core/issues/2350\n    */ #}\n    {% set row_changed_expr -%}\n        ({{ snapshotted_rel }}.{{ columns.dbt_valid_from }} < {{ current_rel }}.{{ updated_at }})\n    {%- endset %}\n\n    {% set scd_args = api.Relation.scd_args(primary_key, updated_at) %}\n    {% set scd_id_expr = snapshot_hash_arguments(scd_args) %}\n\n    {% do return({\n        \"unique_key\": primary_key,\n        \"updated_at\": updated_at,\n        \"row_changed\": row_changed_expr,\n        \"scd_id\": scd_id_expr,\n        \"invalidate_hard_deletes\": invalidate_hard_deletes,\n        \"hard_deletes\": hard_deletes\n    }) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.snapshot_hash_arguments"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2159724, "supported_languages": null}, "macro.dbt.snapshot_string_as_time": {"name": "snapshot_string_as_time", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.snapshot_string_as_time", "macro_sql": "{% macro snapshot_string_as_time(timestamp) -%}\n    {{ adapter.dispatch('snapshot_string_as_time', 'dbt')(timestamp) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__snapshot_string_as_time"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2163, "supported_languages": null}, "macro.dbt.default__snapshot_string_as_time": {"name": "default__snapshot_string_as_time", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.default__snapshot_string_as_time", "macro_sql": "{% macro default__snapshot_string_as_time(timestamp) %}\n    {% do exceptions.raise_not_implemented(\n        'snapshot_string_as_time macro not implemented for adapter '+adapter.type()\n    ) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2166228, "supported_languages": null}, "macro.dbt.snapshot_check_all_get_existing_columns": {"name": "snapshot_check_all_get_existing_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.snapshot_check_all_get_existing_columns", "macro_sql": "{% macro snapshot_check_all_get_existing_columns(node, target_exists, check_cols_config) -%}\n    {%- if not target_exists -%}\n        {#-- no table yet -> return whatever the query does --#}\n        {{ return((false, query_columns)) }}\n    {%- endif -%}\n\n    {#-- handle any schema changes --#}\n    {%- set target_relation = adapter.get_relation(database=node.database, schema=node.schema, identifier=node.alias) -%}\n\n    {% if check_cols_config == 'all' %}\n        {%- set query_columns = get_columns_in_query(node['compiled_code']) -%}\n\n    {% elif check_cols_config is iterable and (check_cols_config | length) > 0 %}\n        {#-- query for proper casing/quoting, to support comparison below --#}\n        {%- set select_check_cols_from_target -%}\n            {#-- N.B. The whitespace below is necessary to avoid edge case issue with comments --#}\n            {#-- See: https://github.com/dbt-labs/dbt-core/issues/6781 --#}\n            select {{ check_cols_config | join(', ') }} from (\n                {{ node['compiled_code'] }}\n            ) subq\n        {%- endset -%}\n        {% set query_columns = get_columns_in_query(select_check_cols_from_target) %}\n\n    {% else %}\n        {% do exceptions.raise_compiler_error(\"Invalid value for 'check_cols': \" ~ check_cols_config) %}\n    {% endif %}\n\n    {%- set existing_cols = adapter.get_columns_in_relation(target_relation) | map(attribute = 'name') | list -%}\n    {%- set ns = namespace() -%} {#-- handle for-loop scoping with a namespace --#}\n    {%- set ns.column_added = false -%}\n\n    {%- set intersection = [] -%}\n    {%- for col in query_columns -%}\n        {%- if col in existing_cols -%}\n            {%- do intersection.append(adapter.quote(col)) -%}\n        {%- else -%}\n            {% set ns.column_added = true %}\n        {%- endif -%}\n    {%- endfor -%}\n    {{ return((ns.column_added, intersection)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_columns_in_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.219125, "supported_languages": null}, "macro.dbt.snapshot_check_strategy": {"name": "snapshot_check_strategy", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/snapshots/strategies.sql", "original_file_path": "macros/materializations/snapshots/strategies.sql", "unique_id": "macro.dbt.snapshot_check_strategy", "macro_sql": "{% macro snapshot_check_strategy(node, snapshotted_rel, current_rel, model_config, target_exists) %}\n    {# The model_config parameter is no longer used, but is passed in anyway for compatibility. #}\n    {% set check_cols_config = config.get('check_cols') %}\n    {% set primary_key = config.get('unique_key') %}\n    {% set hard_deletes = adapter.get_hard_deletes_behavior(config) %}\n    {% set invalidate_hard_deletes = hard_deletes == 'invalidate' %}\n    {% set updated_at = config.get('updated_at') or snapshot_get_time() %}\n\n    {% set column_added = false %}\n\n    {% set column_added, check_cols = snapshot_check_all_get_existing_columns(node, target_exists, check_cols_config) %}\n\n    {%- set row_changed_expr -%}\n    (\n    {%- if column_added -%}\n        {{ get_true_sql() }}\n    {%- else -%}\n    {%- for col in check_cols -%}\n        {{ snapshotted_rel }}.{{ col }} != {{ current_rel }}.{{ col }}\n        or\n        (\n            (({{ snapshotted_rel }}.{{ col }} is null) and not ({{ current_rel }}.{{ col }} is null))\n            or\n            ((not {{ snapshotted_rel }}.{{ col }} is null) and ({{ current_rel }}.{{ col }} is null))\n        )\n        {%- if not loop.last %} or {% endif -%}\n    {%- endfor -%}\n    {%- endif -%}\n    )\n    {%- endset %}\n\n    {% set scd_args = api.Relation.scd_args(primary_key, updated_at) %}\n    {% set scd_id_expr = snapshot_hash_arguments(scd_args) %}\n\n    {% do return({\n        \"unique_key\": primary_key,\n        \"updated_at\": updated_at,\n        \"row_changed\": row_changed_expr,\n        \"scd_id\": scd_id_expr,\n        \"invalidate_hard_deletes\": invalidate_hard_deletes,\n        \"hard_deletes\": hard_deletes\n    }) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.snapshot_get_time", "macro.dbt.snapshot_check_all_get_existing_columns", "macro.dbt.get_true_sql", "macro.dbt.snapshot_hash_arguments"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.221818, "supported_languages": null}, "macro.dbt.get_test_sql": {"name": "get_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/helpers.sql", "original_file_path": "macros/materializations/tests/helpers.sql", "unique_id": "macro.dbt.get_test_sql", "macro_sql": "{% macro get_test_sql(main_sql, fail_calc, warn_if, error_if, limit) -%}\n  {{ adapter.dispatch('get_test_sql', 'dbt')(main_sql, fail_calc, warn_if, error_if, limit) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_test_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2227995, "supported_languages": null}, "macro.dbt.default__get_test_sql": {"name": "default__get_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/helpers.sql", "original_file_path": "macros/materializations/tests/helpers.sql", "unique_id": "macro.dbt.default__get_test_sql", "macro_sql": "{% macro default__get_test_sql(main_sql, fail_calc, warn_if, error_if, limit) -%}\n    select\n      {{ fail_calc }} as failures,\n      {{ fail_calc }} {{ warn_if }} as should_warn,\n      {{ fail_calc }} {{ error_if }} as should_error\n    from (\n      {{ main_sql }}\n      {{ \"limit \" ~ limit if limit != none }}\n    ) dbt_internal_test\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2233577, "supported_languages": null}, "macro.dbt.get_unit_test_sql": {"name": "get_unit_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/helpers.sql", "original_file_path": "macros/materializations/tests/helpers.sql", "unique_id": "macro.dbt.get_unit_test_sql", "macro_sql": "{% macro get_unit_test_sql(main_sql, expected_fixture_sql, expected_column_names) -%}\n  {{ adapter.dispatch('get_unit_test_sql', 'dbt')(main_sql, expected_fixture_sql, expected_column_names) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_unit_test_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.223761, "supported_languages": null}, "macro.dbt.default__get_unit_test_sql": {"name": "default__get_unit_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/helpers.sql", "original_file_path": "macros/materializations/tests/helpers.sql", "unique_id": "macro.dbt.default__get_unit_test_sql", "macro_sql": "{% macro default__get_unit_test_sql(main_sql, expected_fixture_sql, expected_column_names) -%}\n-- Build actual result given inputs\nwith dbt_internal_unit_test_actual as (\n  select\n    {% for expected_column_name in expected_column_names %}{{expected_column_name}}{% if not loop.last -%},{% endif %}{%- endfor -%}, {{ dbt.string_literal(\"actual\") }} as {{ adapter.quote(\"actual_or_expected\") }}\n  from (\n    {{ main_sql }}\n  ) _dbt_internal_unit_test_actual\n),\n-- Build expected result\ndbt_internal_unit_test_expected as (\n  select\n    {% for expected_column_name in expected_column_names %}{{expected_column_name}}{% if not loop.last -%}, {% endif %}{%- endfor -%}, {{ dbt.string_literal(\"expected\") }} as {{ adapter.quote(\"actual_or_expected\") }}\n  from (\n    {{ expected_fixture_sql }}\n  ) _dbt_internal_unit_test_expected\n)\n-- Union actual and expected results\nselect * from dbt_internal_unit_test_actual\nunion all\nselect * from dbt_internal_unit_test_expected\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.string_literal"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2248638, "supported_languages": null}, "macro.dbt.materialization_test_default": {"name": "materialization_test_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/test.sql", "original_file_path": "macros/materializations/tests/test.sql", "unique_id": "macro.dbt.materialization_test_default", "macro_sql": "{%- materialization test, default -%}\n\n  {% set relations = [] %}\n  {% set limit = config.get('limit') %}\n\n  {% set sql_with_limit %}\n    {{ get_limit_subquery_sql(sql, limit) }}\n  {% endset %}\n\n  {% if should_store_failures() %}\n\n    {% set identifier = model['alias'] %}\n    {% set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) %}\n\n    {% set store_failures_as = config.get('store_failures_as') %}\n    -- if `--store-failures` is invoked via command line and `store_failures_as` is not set,\n    -- config.get('store_failures_as', 'table') returns None, not 'table'\n    {% if store_failures_as == none %}{% set store_failures_as = 'table' %}{% endif %}\n    {% if store_failures_as not in ['table', 'view'] %}\n        {{ exceptions.raise_compiler_error(\n            \"'\" ~ store_failures_as ~ \"' is not a valid value for `store_failures_as`. \"\n            \"Accepted values are: ['ephemeral', 'table', 'view']\"\n        ) }}\n    {% endif %}\n\n    {% set target_relation = api.Relation.create(\n        identifier=identifier, schema=schema, database=database, type=store_failures_as) -%} %}\n\n    {% if old_relation %}\n        {% do adapter.drop_relation(old_relation) %}\n    {% endif %}\n\n    {% call statement(auto_begin=True) %}\n        {{ get_create_sql(target_relation, sql_with_limit) }}\n    {% endcall %}\n\n    {% do relations.append(target_relation) %}\n\n    {# Since the test failures have already been saved to the database, reuse that result rather than querying again #}\n    {% set main_sql %}\n        select *\n        from {{ target_relation }}\n    {% endset %}\n\n    {{ adapter.commit() }}\n\n  {% else %}\n\n      {% set main_sql = sql_with_limit %}\n\n  {% endif %}\n\n  {% set fail_calc = config.get('fail_calc') %}\n  {% set warn_if = config.get('warn_if') %}\n  {% set error_if = config.get('error_if') %}\n\n  {% call statement('main', fetch_result=True) -%}\n\n    {# The limit has already been included above, and we do not want to duplicate it again. We also want to be safe for macro overrides treating `limit` as a required parameter. #}\n    {{ get_test_sql(main_sql, fail_calc, warn_if, error_if, limit=none)}}\n\n  {%- endcall %}\n\n  {{ return({'relations': relations}) }}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.get_limit_subquery_sql", "macro.dbt.should_store_failures", "macro.dbt.statement", "macro.dbt.get_create_sql", "macro.dbt.get_test_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2288814, "supported_languages": ["sql"]}, "macro.dbt.materialization_unit_default": {"name": "materialization_unit_default", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/unit.sql", "original_file_path": "macros/materializations/tests/unit.sql", "unique_id": "macro.dbt.materialization_unit_default", "macro_sql": "{%- materialization unit, default -%}\n\n  {% set relations = [] %}\n\n  {% set expected_rows = config.get('expected_rows') %}\n  {% set expected_sql = config.get('expected_sql') %}\n  {% set tested_expected_column_names = expected_rows[0].keys() if (expected_rows | length ) > 0 else get_columns_in_query(sql) %} %}\n\n  {%- set target_relation = this.incorporate(type='table') -%}\n  {%- set temp_relation = make_temp_relation(target_relation)-%}\n  {% do run_query(get_create_table_as_sql(True, temp_relation, get_empty_subquery_sql(sql))) %}\n  {%- set columns_in_relation = adapter.get_columns_in_relation(temp_relation) -%}\n  {%- set column_name_to_data_types = {} -%}\n  {%- for column in columns_in_relation -%}\n  {%-   do column_name_to_data_types.update({column.name|lower: column.data_type}) -%}\n  {%- endfor -%}\n\n  {% if not expected_sql %}\n  {%   set expected_sql = get_expected_sql(expected_rows, column_name_to_data_types) %}\n  {% endif %}\n  {% set unit_test_sql = get_unit_test_sql(sql, expected_sql, tested_expected_column_names) %}\n\n  {% call statement('main', fetch_result=True) -%}\n\n    {{ unit_test_sql }}\n\n  {%- endcall %}\n\n  {% do adapter.drop_relation(temp_relation) %}\n\n  {{ return({'relations': relations}) }}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.get_columns_in_query", "macro.dbt.make_temp_relation", "macro.dbt.run_query", "macro.dbt.get_create_table_as_sql", "macro.dbt.get_empty_subquery_sql", "macro.dbt.get_expected_sql", "macro.dbt.get_unit_test_sql", "macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.231645, "supported_languages": ["sql"]}, "macro.dbt.get_where_subquery": {"name": "get_where_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/where_subquery.sql", "original_file_path": "macros/materializations/tests/where_subquery.sql", "unique_id": "macro.dbt.get_where_subquery", "macro_sql": "{% macro get_where_subquery(relation) -%}\n    {% do return(adapter.dispatch('get_where_subquery', 'dbt')(relation)) %}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_where_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.232269, "supported_languages": null}, "macro.dbt.default__get_where_subquery": {"name": "default__get_where_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros/materializations/tests/where_subquery.sql", "original_file_path": "macros/materializations/tests/where_subquery.sql", "unique_id": "macro.dbt.default__get_where_subquery", "macro_sql": "{% macro default__get_where_subquery(relation) -%}\n    {% set where = config.get('where', '') %}\n    {% if where %}\n        {%- set filtered -%}\n            (select * from {{ relation }} where {{ where }}) dbt_subquery\n        {%- endset -%}\n        {% do return(filtered) %}\n    {%- else -%}\n        {% do return(relation) %}\n    {%- endif -%}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2329705, "supported_languages": null}, "macro.dbt.resolve_model_name": {"name": "resolve_model_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/python_model/python.sql", "original_file_path": "macros/python_model/python.sql", "unique_id": "macro.dbt.resolve_model_name", "macro_sql": "{% macro resolve_model_name(input_model_name) %}\n    {{ return(adapter.dispatch('resolve_model_name', 'dbt')(input_model_name)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__resolve_model_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2342348, "supported_languages": null}, "macro.dbt.default__resolve_model_name": {"name": "default__resolve_model_name", "resource_type": "macro", "package_name": "dbt", "path": "macros/python_model/python.sql", "original_file_path": "macros/python_model/python.sql", "unique_id": "macro.dbt.default__resolve_model_name", "macro_sql": "\n\n{%- macro default__resolve_model_name(input_model_name) -%}\n    {{  input_model_name | string | replace('\"', '\\\"') }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2345302, "supported_languages": null}, "macro.dbt.build_ref_function": {"name": "build_ref_function", "resource_type": "macro", "package_name": "dbt", "path": "macros/python_model/python.sql", "original_file_path": "macros/python_model/python.sql", "unique_id": "macro.dbt.build_ref_function", "macro_sql": "{% macro build_ref_function(model) %}\n\n    {%- set ref_dict = {} -%}\n    {%- for _ref in model.refs -%}\n        {% set _ref_args = [_ref.get('package'), _ref['name']] if _ref.get('package') else [_ref['name'],] %}\n        {%- set resolved = ref(*_ref_args, v=_ref.get('version')) -%}\n\n        {#\n            We want to get the string of the returned relation by calling .render() in order to skip sample/empty\n            mode rendering logic. However, people override the default ref macro, and often return a string instead\n            of a relation (like the ref macro does by default). Thus, to make sure we dont blow things up, we have\n            to ensure the resolved relation has a .render() method.\n        #}\n        {%- if resolved.render is defined and resolved.render is callable -%}\n            {%- set resolved = resolved.render() -%}\n        {%- endif -%}\n\n        {%- if _ref.get('version') -%}\n            {% do _ref_args.extend([\"v\" ~ _ref['version']]) %}\n        {%- endif -%}\n       {%- do ref_dict.update({_ref_args | join('.'): resolve_model_name(resolved)}) -%}\n    {%- endfor -%}\n\ndef ref(*args, **kwargs):\n    refs = {{ ref_dict | tojson }}\n    key = '.'.join(args)\n    version = kwargs.get(\"v\") or kwargs.get(\"version\")\n    if version:\n        key += f\".v{version}\"\n    dbt_load_df_function = kwargs.get(\"dbt_load_df_function\")\n    return dbt_load_df_function(refs[key])\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.resolve_model_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2362316, "supported_languages": null}, "macro.dbt.build_source_function": {"name": "build_source_function", "resource_type": "macro", "package_name": "dbt", "path": "macros/python_model/python.sql", "original_file_path": "macros/python_model/python.sql", "unique_id": "macro.dbt.build_source_function", "macro_sql": "{% macro build_source_function(model) %}\n\n    {%- set source_dict = {} -%}\n    {%- for _source in model.sources -%}\n        {%- set resolved = source(*_source) -%}\n        {%- do source_dict.update({_source | join('.'): resolve_model_name(resolved)}) -%}\n    {%- endfor -%}\n\ndef source(*args, dbt_load_df_function):\n    sources = {{ source_dict | tojson }}\n    key = '.'.join(args)\n    return dbt_load_df_function(sources[key])\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.resolve_model_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2369928, "supported_languages": null}, "macro.dbt.build_config_dict": {"name": "build_config_dict", "resource_type": "macro", "package_name": "dbt", "path": "macros/python_model/python.sql", "original_file_path": "macros/python_model/python.sql", "unique_id": "macro.dbt.build_config_dict", "macro_sql": "{% macro build_config_dict(model) %}\n    {%- set config_dict = {} -%}\n    {% set config_dbt_used = zip(model.config.config_keys_used, model.config.config_keys_defaults) | list %}\n    {%- for key, default in config_dbt_used -%}\n        {# weird type testing with enum, would be much easier to write this logic in Python! #}\n        {%- if key == \"language\" -%}\n          {%- set value = \"python\" -%}\n        {%- endif -%}\n        {%- set value = model.config.get(key, default) -%}\n        {%- do config_dict.update({key: value}) -%}\n    {%- endfor -%}\nconfig_dict = {{ config_dict }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.238036, "supported_languages": null}, "macro.dbt.py_script_postfix": {"name": "py_script_postfix", "resource_type": "macro", "package_name": "dbt", "path": "macros/python_model/python.sql", "original_file_path": "macros/python_model/python.sql", "unique_id": "macro.dbt.py_script_postfix", "macro_sql": "{% macro py_script_postfix(model) %}\n# This part is user provided model code\n# you will need to copy the next section to run the code\n# COMMAND ----------\n# this part is dbt logic for get ref work, do not modify\n\n{{ build_ref_function(model ) }}\n{{ build_source_function(model ) }}\n{{ build_config_dict(model) }}\n\nclass config:\n    def __init__(self, *args, **kwargs):\n        pass\n\n    @staticmethod\n    def get(key, default=None):\n        return config_dict.get(key, default)\n\nclass this:\n    \"\"\"dbt.this() or dbt.this.identifier\"\"\"\n    database = \"{{ this.database }}\"\n    schema = \"{{ this.schema }}\"\n    identifier = \"{{ this.identifier }}\"\n    {% set this_relation_name = resolve_model_name(this) %}\n    def __repr__(self):\n        return '{{ this_relation_name  }}'\n\n\nclass dbtObj:\n    def __init__(self, load_df_function) -> None:\n        self.source = lambda *args: source(*args, dbt_load_df_function=load_df_function)\n        self.ref = lambda *args, **kwargs: ref(*args, **kwargs, dbt_load_df_function=load_df_function)\n        self.config = config\n        self.this = this()\n        self.is_incremental = {{ is_incremental() }}\n\n# COMMAND ----------\n{{py_script_comment()}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.build_ref_function", "macro.dbt.build_source_function", "macro.dbt.build_config_dict", "macro.dbt.resolve_model_name", "macro.dbt.is_incremental", "macro.dbt.py_script_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2388964, "supported_languages": null}, "macro.dbt.py_script_comment": {"name": "py_script_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros/python_model/python.sql", "original_file_path": "macros/python_model/python.sql", "unique_id": "macro.dbt.py_script_comment", "macro_sql": "{%macro py_script_comment()%}\n{%endmacro%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2390695, "supported_languages": null}, "macro.dbt.get_create_sql": {"name": "get_create_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/create.sql", "original_file_path": "macros/relations/create.sql", "unique_id": "macro.dbt.get_create_sql", "macro_sql": "{%- macro get_create_sql(relation, sql) -%}\n    {{- log('Applying CREATE to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_create_sql', 'dbt')(relation, sql) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_create_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.239775, "supported_languages": null}, "macro.dbt.default__get_create_sql": {"name": "default__get_create_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/create.sql", "original_file_path": "macros/relations/create.sql", "unique_id": "macro.dbt.default__get_create_sql", "macro_sql": "{%- macro default__get_create_sql(relation, sql) -%}\n\n    {%- if relation.is_view -%}\n        {{ get_create_view_as_sql(relation, sql) }}\n\n    {%- elif relation.is_table -%}\n        {{ get_create_table_as_sql(False, relation, sql) }}\n\n    {%- elif relation.is_materialized_view -%}\n        {{ get_create_materialized_view_as_sql(relation, sql) }}\n\n    {%- else -%}\n        {{- exceptions.raise_compiler_error(\"`get_create_sql` has not been implemented for: \" ~ relation.type ) -}}\n\n    {%- endif -%}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.get_create_view_as_sql", "macro.dbt.get_create_table_as_sql", "macro.dbt.get_create_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2407048, "supported_languages": null}, "macro.dbt.get_create_backup_sql": {"name": "get_create_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/create_backup.sql", "original_file_path": "macros/relations/create_backup.sql", "unique_id": "macro.dbt.get_create_backup_sql", "macro_sql": "{%- macro get_create_backup_sql(relation) -%}\n    {{- log('Applying CREATE BACKUP to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_create_backup_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_create_backup_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.24129, "supported_languages": null}, "macro.dbt.default__get_create_backup_sql": {"name": "default__get_create_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/create_backup.sql", "original_file_path": "macros/relations/create_backup.sql", "unique_id": "macro.dbt.default__get_create_backup_sql", "macro_sql": "{%- macro default__get_create_backup_sql(relation) -%}\n\n    -- get the standard backup name\n    {% set backup_relation = make_backup_relation(relation, relation.type) %}\n\n    -- drop any pre-existing backup\n    {{ get_drop_sql(backup_relation) }};\n\n    {{ get_rename_sql(relation, backup_relation.identifier) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_backup_relation", "macro.dbt.get_drop_sql", "macro.dbt.get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2417877, "supported_languages": null}, "macro.dbt.get_create_intermediate_sql": {"name": "get_create_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/create_intermediate.sql", "original_file_path": "macros/relations/create_intermediate.sql", "unique_id": "macro.dbt.get_create_intermediate_sql", "macro_sql": "{%- macro get_create_intermediate_sql(relation, sql) -%}\n    {{- log('Applying CREATE INTERMEDIATE to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_create_intermediate_sql', 'dbt')(relation, sql) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_create_intermediate_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2424011, "supported_languages": null}, "macro.dbt.default__get_create_intermediate_sql": {"name": "default__get_create_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/create_intermediate.sql", "original_file_path": "macros/relations/create_intermediate.sql", "unique_id": "macro.dbt.default__get_create_intermediate_sql", "macro_sql": "{%- macro default__get_create_intermediate_sql(relation, sql) -%}\n\n    -- get the standard intermediate name\n    {% set intermediate_relation = make_intermediate_relation(relation) %}\n\n    -- drop any pre-existing intermediate\n    {{ get_drop_sql(intermediate_relation) }};\n\n    {{ get_create_sql(intermediate_relation, sql) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_intermediate_relation", "macro.dbt.get_drop_sql", "macro.dbt.get_create_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2428703, "supported_languages": null}, "macro.dbt.get_drop_sql": {"name": "get_drop_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/drop.sql", "original_file_path": "macros/relations/drop.sql", "unique_id": "macro.dbt.get_drop_sql", "macro_sql": "{%- macro get_drop_sql(relation) -%}\n    {{- log('Applying DROP to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_drop_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_drop_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.243683, "supported_languages": null}, "macro.dbt.default__get_drop_sql": {"name": "default__get_drop_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/drop.sql", "original_file_path": "macros/relations/drop.sql", "unique_id": "macro.dbt.default__get_drop_sql", "macro_sql": "{%- macro default__get_drop_sql(relation) -%}\n\n    {%- if relation.is_view -%}\n        {{ drop_view(relation) }}\n\n    {%- elif relation.is_table -%}\n        {{ drop_table(relation) }}\n\n    {%- elif relation.is_materialized_view -%}\n        {{ drop_materialized_view(relation) }}\n\n    {%- else -%}\n        drop {{ relation.type }} if exists {{ relation.render() }} cascade\n\n    {%- endif -%}\n\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.drop_view", "macro.dbt.drop_table", "macro.dbt.drop_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2443953, "supported_languages": null}, "macro.dbt.drop_relation": {"name": "drop_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/drop.sql", "original_file_path": "macros/relations/drop.sql", "unique_id": "macro.dbt.drop_relation", "macro_sql": "{% macro drop_relation(relation) -%}\n    {{ return(adapter.dispatch('drop_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2447426, "supported_languages": null}, "macro.dbt.default__drop_relation": {"name": "default__drop_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/drop.sql", "original_file_path": "macros/relations/drop.sql", "unique_id": "macro.dbt.default__drop_relation", "macro_sql": "{% macro default__drop_relation(relation) -%}\n    {% call statement('drop_relation', auto_begin=False) -%}\n        {{ get_drop_sql(relation) }}\n    {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.get_drop_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2451222, "supported_languages": null}, "macro.dbt.drop_relation_if_exists": {"name": "drop_relation_if_exists", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/drop.sql", "original_file_path": "macros/relations/drop.sql", "unique_id": "macro.dbt.drop_relation_if_exists", "macro_sql": "{% macro drop_relation_if_exists(relation) %}\n  {% if relation is not none %}\n    {{ adapter.drop_relation(relation) }}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2454872, "supported_languages": null}, "macro.dbt.get_drop_backup_sql": {"name": "get_drop_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/drop_backup.sql", "original_file_path": "macros/relations/drop_backup.sql", "unique_id": "macro.dbt.get_drop_backup_sql", "macro_sql": "{%- macro get_drop_backup_sql(relation) -%}\n    {{- log('Applying DROP BACKUP to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_drop_backup_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_drop_backup_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2460585, "supported_languages": null}, "macro.dbt.default__get_drop_backup_sql": {"name": "default__get_drop_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/drop_backup.sql", "original_file_path": "macros/relations/drop_backup.sql", "unique_id": "macro.dbt.default__get_drop_backup_sql", "macro_sql": "{%- macro default__get_drop_backup_sql(relation) -%}\n\n    -- get the standard backup name\n    {% set backup_relation = make_backup_relation(relation, relation.type) %}\n\n    {{ get_drop_sql(backup_relation) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_backup_relation", "macro.dbt.get_drop_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.246447, "supported_languages": null}, "macro.dbt.get_rename_sql": {"name": "get_rename_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/rename.sql", "original_file_path": "macros/relations/rename.sql", "unique_id": "macro.dbt.get_rename_sql", "macro_sql": "{%- macro get_rename_sql(relation, new_name) -%}\n    {{- log('Applying RENAME to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_rename_sql', 'dbt')(relation, new_name) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2472649, "supported_languages": null}, "macro.dbt.default__get_rename_sql": {"name": "default__get_rename_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/rename.sql", "original_file_path": "macros/relations/rename.sql", "unique_id": "macro.dbt.default__get_rename_sql", "macro_sql": "{%- macro default__get_rename_sql(relation, new_name) -%}\n\n    {%- if relation.is_view -%}\n        {{ get_rename_view_sql(relation, new_name) }}\n\n    {%- elif relation.is_table -%}\n        {{ get_rename_table_sql(relation, new_name) }}\n\n    {%- elif relation.is_materialized_view -%}\n        {{ get_rename_materialized_view_sql(relation, new_name) }}\n\n    {%- else -%}\n        {{- exceptions.raise_compiler_error(\"`get_rename_sql` has not been implemented for: \" ~ relation.type ) -}}\n\n    {%- endif -%}\n\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.get_rename_view_sql", "macro.dbt.get_rename_table_sql", "macro.dbt.get_rename_materialized_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2480564, "supported_languages": null}, "macro.dbt.rename_relation": {"name": "rename_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/rename.sql", "original_file_path": "macros/relations/rename.sql", "unique_id": "macro.dbt.rename_relation", "macro_sql": "{% macro rename_relation(from_relation, to_relation) -%}\n  {{ return(adapter.dispatch('rename_relation', 'dbt')(from_relation, to_relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__rename_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2484443, "supported_languages": null}, "macro.dbt.default__rename_relation": {"name": "default__rename_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/rename.sql", "original_file_path": "macros/relations/rename.sql", "unique_id": "macro.dbt.default__rename_relation", "macro_sql": "{% macro default__rename_relation(from_relation, to_relation) -%}\n  {% set target_name = adapter.quote_as_configured(to_relation.identifier, 'identifier') %}\n  {% call statement('rename_relation') -%}\n    alter table {{ from_relation.render() }} rename to {{ target_name }}\n  {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2490153, "supported_languages": null}, "macro.dbt.get_rename_intermediate_sql": {"name": "get_rename_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/rename_intermediate.sql", "original_file_path": "macros/relations/rename_intermediate.sql", "unique_id": "macro.dbt.get_rename_intermediate_sql", "macro_sql": "{%- macro get_rename_intermediate_sql(relation) -%}\n    {{- log('Applying RENAME INTERMEDIATE to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_rename_intermediate_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_rename_intermediate_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2495878, "supported_languages": null}, "macro.dbt.default__get_rename_intermediate_sql": {"name": "default__get_rename_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/rename_intermediate.sql", "original_file_path": "macros/relations/rename_intermediate.sql", "unique_id": "macro.dbt.default__get_rename_intermediate_sql", "macro_sql": "{%- macro default__get_rename_intermediate_sql(relation) -%}\n\n    -- get the standard intermediate name\n    {% set intermediate_relation = make_intermediate_relation(relation) %}\n\n    {{ get_rename_sql(intermediate_relation, relation.identifier) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_intermediate_relation", "macro.dbt.get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2499819, "supported_languages": null}, "macro.dbt.get_replace_sql": {"name": "get_replace_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/replace.sql", "original_file_path": "macros/relations/replace.sql", "unique_id": "macro.dbt.get_replace_sql", "macro_sql": "{% macro get_replace_sql(existing_relation, target_relation, sql) %}\n    {{- log('Applying REPLACE to: ' ~ existing_relation) -}}\n    {{- adapter.dispatch('get_replace_sql', 'dbt')(existing_relation, target_relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_replace_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2509608, "supported_languages": null}, "macro.dbt.default__get_replace_sql": {"name": "default__get_replace_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/replace.sql", "original_file_path": "macros/relations/replace.sql", "unique_id": "macro.dbt.default__get_replace_sql", "macro_sql": "{% macro default__get_replace_sql(existing_relation, target_relation, sql) %}\n\n    {# /* use a create or replace statement if possible */ #}\n\n    {% set is_replaceable = existing_relation.type == target_relation.type and existing_relation.can_be_replaced %}\n\n    {% if is_replaceable and existing_relation.is_view %}\n        {{ get_replace_view_sql(target_relation, sql) }}\n\n    {% elif is_replaceable and existing_relation.is_table %}\n        {{ get_replace_table_sql(target_relation, sql) }}\n\n    {% elif is_replaceable and existing_relation.is_materialized_view %}\n        {{ get_replace_materialized_view_sql(target_relation, sql) }}\n\n    {# /* a create or replace statement is not possible, so try to stage and/or backup to be safe */ #}\n\n    {# /* create target_relation as an intermediate relation, then swap it out with the existing one using a backup */ #}\n    {%- elif target_relation.can_be_renamed and existing_relation.can_be_renamed -%}\n        {{ get_create_intermediate_sql(target_relation, sql) }};\n        {{ get_create_backup_sql(existing_relation) }};\n        {{ get_rename_intermediate_sql(target_relation) }};\n        {{ get_drop_backup_sql(existing_relation) }}\n\n    {# /* create target_relation as an intermediate relation, then swap it out with the existing one without using a backup */ #}\n    {%- elif target_relation.can_be_renamed -%}\n        {{ get_create_intermediate_sql(target_relation, sql) }};\n        {{ get_drop_sql(existing_relation) }};\n        {{ get_rename_intermediate_sql(target_relation) }}\n\n    {# /* create target_relation in place by first backing up the existing relation */ #}\n    {%- elif existing_relation.can_be_renamed -%}\n        {{ get_create_backup_sql(existing_relation) }};\n        {{ get_create_sql(target_relation, sql) }};\n        {{ get_drop_backup_sql(existing_relation) }}\n\n    {# /* no renaming is allowed, so just drop and create */ #}\n    {%- else -%}\n        {{ get_drop_sql(existing_relation) }};\n        {{ get_create_sql(target_relation, sql) }}\n\n    {%- endif -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_replace_view_sql", "macro.dbt.get_replace_table_sql", "macro.dbt.get_replace_materialized_view_sql", "macro.dbt.get_create_intermediate_sql", "macro.dbt.get_create_backup_sql", "macro.dbt.get_rename_intermediate_sql", "macro.dbt.get_drop_backup_sql", "macro.dbt.get_drop_sql", "macro.dbt.get_create_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2532191, "supported_languages": null}, "macro.dbt.drop_schema_named": {"name": "drop_schema_named", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/schema.sql", "original_file_path": "macros/relations/schema.sql", "unique_id": "macro.dbt.drop_schema_named", "macro_sql": "{% macro drop_schema_named(schema_name) %}\n    {{ return(adapter.dispatch('drop_schema_named', 'dbt') (schema_name)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_schema_named"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.253739, "supported_languages": null}, "macro.dbt.default__drop_schema_named": {"name": "default__drop_schema_named", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/schema.sql", "original_file_path": "macros/relations/schema.sql", "unique_id": "macro.dbt.default__drop_schema_named", "macro_sql": "{% macro default__drop_schema_named(schema_name) %}\n  {% set schema_relation = api.Relation.create(schema=schema_name) %}\n  {{ adapter.drop_schema(schema_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.254137, "supported_languages": null}, "macro.dbt.get_table_columns_and_constraints": {"name": "get_table_columns_and_constraints", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.get_table_columns_and_constraints", "macro_sql": "{%- macro get_table_columns_and_constraints() -%}\n  {{ adapter.dispatch('get_table_columns_and_constraints', 'dbt')() }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__get_table_columns_and_constraints"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2554324, "supported_languages": null}, "macro.dbt.default__get_table_columns_and_constraints": {"name": "default__get_table_columns_and_constraints", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.default__get_table_columns_and_constraints", "macro_sql": "{% macro default__get_table_columns_and_constraints() -%}\n  {{ return(table_columns_and_constraints()) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.table_columns_and_constraints"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2556834, "supported_languages": null}, "macro.dbt.table_columns_and_constraints": {"name": "table_columns_and_constraints", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.table_columns_and_constraints", "macro_sql": "{% macro table_columns_and_constraints() %}\n  {# loop through user_provided_columns to create DDL with data types and constraints #}\n    {%- set raw_column_constraints = adapter.render_raw_columns_constraints(raw_columns=model['columns']) -%}\n    {%- set raw_model_constraints = adapter.render_raw_model_constraints(raw_constraints=model['constraints']) -%}\n    (\n    {% for c in raw_column_constraints -%}\n      {{ c }}{{ \",\" if not loop.last or raw_model_constraints }}\n    {% endfor %}\n    {% for c in raw_model_constraints -%}\n        {{ c }}{{ \",\" if not loop.last }}\n    {% endfor -%}\n    )\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2566366, "supported_languages": null}, "macro.dbt.get_assert_columns_equivalent": {"name": "get_assert_columns_equivalent", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.get_assert_columns_equivalent", "macro_sql": "\n\n{%- macro get_assert_columns_equivalent(sql) -%}\n  {{ adapter.dispatch('get_assert_columns_equivalent', 'dbt')(sql) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__get_assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2569551, "supported_languages": null}, "macro.dbt.default__get_assert_columns_equivalent": {"name": "default__get_assert_columns_equivalent", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.default__get_assert_columns_equivalent", "macro_sql": "{% macro default__get_assert_columns_equivalent(sql) -%}\n  {{ return(assert_columns_equivalent(sql)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2572176, "supported_languages": null}, "macro.dbt.assert_columns_equivalent": {"name": "assert_columns_equivalent", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.assert_columns_equivalent", "macro_sql": "{% macro assert_columns_equivalent(sql) %}\n\n  {#-- First ensure the user has defined 'columns' in yaml specification --#}\n  {%- set user_defined_columns = model['columns'] -%}\n  {%- if not user_defined_columns -%}\n      {{ exceptions.raise_contract_error([], []) }}\n  {%- endif -%}\n\n  {#-- Obtain the column schema provided by sql file. #}\n  {%- set sql_file_provided_columns = get_column_schema_from_query(sql, config.get('sql_header', none)) -%}\n  {#--Obtain the column schema provided by the schema file by generating an 'empty schema' query from the model's columns. #}\n  {%- set schema_file_provided_columns = get_column_schema_from_query(get_empty_schema_sql(user_defined_columns)) -%}\n\n  {#-- create dictionaries with name and formatted data type and strings for exception #}\n  {%- set sql_columns = format_columns(sql_file_provided_columns) -%}\n  {%- set yaml_columns = format_columns(schema_file_provided_columns)  -%}\n\n  {%- if sql_columns|length != yaml_columns|length -%}\n    {%- do exceptions.raise_contract_error(yaml_columns, sql_columns) -%}\n  {%- endif -%}\n\n  {%- for sql_col in sql_columns -%}\n    {%- set yaml_col = [] -%}\n    {%- for this_col in yaml_columns -%}\n      {%- if this_col['name'] == sql_col['name'] -%}\n        {%- do yaml_col.append(this_col) -%}\n        {%- break -%}\n      {%- endif -%}\n    {%- endfor -%}\n    {%- if not yaml_col -%}\n      {#-- Column with name not found in yaml #}\n      {%- do exceptions.raise_contract_error(yaml_columns, sql_columns) -%}\n    {%- endif -%}\n    {%- if sql_col['formatted'] != yaml_col[0]['formatted'] -%}\n      {#-- Column data types don't match #}\n      {%- do exceptions.raise_contract_error(yaml_columns, sql_columns) -%}\n    {%- endif -%}\n  {%- endfor -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_column_schema_from_query", "macro.dbt.get_empty_schema_sql", "macro.dbt.format_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2595081, "supported_languages": null}, "macro.dbt.format_columns": {"name": "format_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.format_columns", "macro_sql": "{% macro format_columns(columns) %}\n  {% set formatted_columns = [] %}\n  {% for column in columns %}\n    {%- set formatted_column = adapter.dispatch('format_column', 'dbt')(column) -%}\n    {%- do formatted_columns.append(formatted_column) -%}\n  {% endfor %}\n  {{ return(formatted_columns) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__format_column"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2602224, "supported_languages": null}, "macro.dbt.default__format_column": {"name": "default__format_column", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/column/columns_spec_ddl.sql", "original_file_path": "macros/relations/column/columns_spec_ddl.sql", "unique_id": "macro.dbt.default__format_column", "macro_sql": "{% macro default__format_column(column) -%}\n  {% set data_type = column.dtype %}\n  {% set formatted = column.column.lower() ~ \" \" ~ data_type %}\n  {{ return({'name': column.name, 'data_type': data_type, 'formatted': formatted}) }}\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.260841, "supported_languages": null}, "macro.dbt.get_alter_materialized_view_as_sql": {"name": "get_alter_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/alter.sql", "original_file_path": "macros/relations/materialized_view/alter.sql", "unique_id": "macro.dbt.get_alter_materialized_view_as_sql", "macro_sql": "{% macro get_alter_materialized_view_as_sql(\n    relation,\n    configuration_changes,\n    sql,\n    existing_relation,\n    backup_relation,\n    intermediate_relation\n) %}\n    {{- log('Applying ALTER to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_alter_materialized_view_as_sql', 'dbt')(\n        relation,\n        configuration_changes,\n        sql,\n        existing_relation,\n        backup_relation,\n        intermediate_relation\n    ) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_alter_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2618127, "supported_languages": null}, "macro.dbt.default__get_alter_materialized_view_as_sql": {"name": "default__get_alter_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/alter.sql", "original_file_path": "macros/relations/materialized_view/alter.sql", "unique_id": "macro.dbt.default__get_alter_materialized_view_as_sql", "macro_sql": "{% macro default__get_alter_materialized_view_as_sql(\n    relation,\n    configuration_changes,\n    sql,\n    existing_relation,\n    backup_relation,\n    intermediate_relation\n) %}\n    {{ exceptions.raise_compiler_error(\"Materialized views have not been implemented for this adapter.\") }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2621675, "supported_languages": null}, "macro.dbt.get_materialized_view_configuration_changes": {"name": "get_materialized_view_configuration_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/alter.sql", "original_file_path": "macros/relations/materialized_view/alter.sql", "unique_id": "macro.dbt.get_materialized_view_configuration_changes", "macro_sql": "{% macro get_materialized_view_configuration_changes(existing_relation, new_config) %}\n    /* {#\n    It's recommended that configuration changes be formatted as follows:\n    {\"<change_category>\": [{\"action\": \"<name>\", \"context\": ...}]}\n\n    For example:\n    {\n        \"indexes\": [\n            {\"action\": \"drop\", \"context\": \"index_abc\"},\n            {\"action\": \"create\", \"context\": {\"columns\": [\"column_1\", \"column_2\"], \"type\": \"hash\", \"unique\": True}},\n        ],\n    }\n\n    Either way, `get_materialized_view_configuration_changes` needs to align with `get_alter_materialized_view_as_sql`.\n    #} */\n    {{- log('Determining configuration changes on: ' ~ existing_relation) -}}\n    {%- do return(adapter.dispatch('get_materialized_view_configuration_changes', 'dbt')(existing_relation, new_config)) -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_materialized_view_configuration_changes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2626832, "supported_languages": null}, "macro.dbt.default__get_materialized_view_configuration_changes": {"name": "default__get_materialized_view_configuration_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/alter.sql", "original_file_path": "macros/relations/materialized_view/alter.sql", "unique_id": "macro.dbt.default__get_materialized_view_configuration_changes", "macro_sql": "{% macro default__get_materialized_view_configuration_changes(existing_relation, new_config) %}\n    {{ exceptions.raise_compiler_error(\"Materialized views have not been implemented for this adapter.\") }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2629604, "supported_languages": null}, "macro.dbt.get_create_materialized_view_as_sql": {"name": "get_create_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/create.sql", "original_file_path": "macros/relations/materialized_view/create.sql", "unique_id": "macro.dbt.get_create_materialized_view_as_sql", "macro_sql": "{% macro get_create_materialized_view_as_sql(relation, sql) -%}\n    {{- adapter.dispatch('get_create_materialized_view_as_sql', 'dbt')(relation, sql) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_create_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2634583, "supported_languages": null}, "macro.dbt.default__get_create_materialized_view_as_sql": {"name": "default__get_create_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/create.sql", "original_file_path": "macros/relations/materialized_view/create.sql", "unique_id": "macro.dbt.default__get_create_materialized_view_as_sql", "macro_sql": "{% macro default__get_create_materialized_view_as_sql(relation, sql) -%}\n    {{ exceptions.raise_compiler_error(\n        \"`get_create_materialized_view_as_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2637486, "supported_languages": null}, "macro.dbt.drop_materialized_view": {"name": "drop_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/drop.sql", "original_file_path": "macros/relations/materialized_view/drop.sql", "unique_id": "macro.dbt.drop_materialized_view", "macro_sql": "{% macro drop_materialized_view(relation) -%}\n    {{- adapter.dispatch('drop_materialized_view', 'dbt')(relation) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__drop_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2642074, "supported_languages": null}, "macro.dbt.default__drop_materialized_view": {"name": "default__drop_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/drop.sql", "original_file_path": "macros/relations/materialized_view/drop.sql", "unique_id": "macro.dbt.default__drop_materialized_view", "macro_sql": "{% macro default__drop_materialized_view(relation) -%}\n    drop materialized view if exists {{ relation.render() }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.264444, "supported_languages": null}, "macro.dbt.refresh_materialized_view": {"name": "refresh_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/refresh.sql", "original_file_path": "macros/relations/materialized_view/refresh.sql", "unique_id": "macro.dbt.refresh_materialized_view", "macro_sql": "{% macro refresh_materialized_view(relation) %}\n    {{- log('Applying REFRESH to: ' ~ relation) -}}\n    {{- adapter.dispatch('refresh_materialized_view', 'dbt')(relation) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__refresh_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2649941, "supported_languages": null}, "macro.dbt.default__refresh_materialized_view": {"name": "default__refresh_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/refresh.sql", "original_file_path": "macros/relations/materialized_view/refresh.sql", "unique_id": "macro.dbt.default__refresh_materialized_view", "macro_sql": "{% macro default__refresh_materialized_view(relation) %}\n    {{ exceptions.raise_compiler_error(\"`refresh_materialized_view` has not been implemented for this adapter.\") }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2653716, "supported_languages": null}, "macro.dbt.get_rename_materialized_view_sql": {"name": "get_rename_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/rename.sql", "original_file_path": "macros/relations/materialized_view/rename.sql", "unique_id": "macro.dbt.get_rename_materialized_view_sql", "macro_sql": "{% macro get_rename_materialized_view_sql(relation, new_name) %}\n    {{- adapter.dispatch('get_rename_materialized_view_sql', 'dbt')(relation, new_name) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_rename_materialized_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2658706, "supported_languages": null}, "macro.dbt.default__get_rename_materialized_view_sql": {"name": "default__get_rename_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/rename.sql", "original_file_path": "macros/relations/materialized_view/rename.sql", "unique_id": "macro.dbt.default__get_rename_materialized_view_sql", "macro_sql": "{% macro default__get_rename_materialized_view_sql(relation, new_name) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_rename_materialized_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2661507, "supported_languages": null}, "macro.dbt.get_replace_materialized_view_sql": {"name": "get_replace_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/replace.sql", "original_file_path": "macros/relations/materialized_view/replace.sql", "unique_id": "macro.dbt.get_replace_materialized_view_sql", "macro_sql": "{% macro get_replace_materialized_view_sql(relation, sql) %}\n    {{- adapter.dispatch('get_replace_materialized_view_sql', 'dbt')(relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_replace_materialized_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.266643, "supported_languages": null}, "macro.dbt.default__get_replace_materialized_view_sql": {"name": "default__get_replace_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/materialized_view/replace.sql", "original_file_path": "macros/relations/materialized_view/replace.sql", "unique_id": "macro.dbt.default__get_replace_materialized_view_sql", "macro_sql": "{% macro default__get_replace_materialized_view_sql(relation, sql) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_replace_materialized_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2669258, "supported_languages": null}, "macro.dbt.get_create_table_as_sql": {"name": "get_create_table_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/create.sql", "original_file_path": "macros/relations/table/create.sql", "unique_id": "macro.dbt.get_create_table_as_sql", "macro_sql": "{% macro get_create_table_as_sql(temporary, relation, sql) -%}\n  {{ adapter.dispatch('get_create_table_as_sql', 'dbt')(temporary, relation, sql) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_create_table_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.268155, "supported_languages": null}, "macro.dbt.default__get_create_table_as_sql": {"name": "default__get_create_table_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/create.sql", "original_file_path": "macros/relations/table/create.sql", "unique_id": "macro.dbt.default__get_create_table_as_sql", "macro_sql": "{% macro default__get_create_table_as_sql(temporary, relation, sql) -%}\n  {{ return(create_table_as(temporary, relation, sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.create_table_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2685206, "supported_languages": null}, "macro.dbt.create_table_as": {"name": "create_table_as", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/create.sql", "original_file_path": "macros/relations/table/create.sql", "unique_id": "macro.dbt.create_table_as", "macro_sql": "{% macro create_table_as(temporary, relation, compiled_code, language='sql') -%}\n  {# backward compatibility for create_table_as that does not support language #}\n  {% if language == \"sql\" %}\n    {{ adapter.dispatch('create_table_as', 'dbt')(temporary, relation, compiled_code)}}\n  {% else %}\n    {{ adapter.dispatch('create_table_as', 'dbt')(temporary, relation, compiled_code, language) }}\n  {% endif %}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__create_table_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2694256, "supported_languages": null}, "macro.dbt.default__create_table_as": {"name": "default__create_table_as", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/create.sql", "original_file_path": "macros/relations/table/create.sql", "unique_id": "macro.dbt.default__create_table_as", "macro_sql": "{% macro default__create_table_as(temporary, relation, sql) -%}\n  {%- set sql_header = config.get('sql_header', none) -%}\n\n  {{ sql_header if sql_header is not none }}\n\n  create {% if temporary: -%}temporary{%- endif %} table\n    {{ relation.include(database=(not temporary), schema=(not temporary)) }}\n  {% set contract_config = config.get('contract') %}\n  {% if contract_config.enforced and (not temporary) %}\n    {{ get_assert_columns_equivalent(sql) }}\n    {{ get_table_columns_and_constraints() }}\n    {%- set sql = get_select_subquery(sql) %}\n  {% endif %}\n  as (\n    {{ sql }}\n  );\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent", "macro.dbt.get_table_columns_and_constraints", "macro.dbt.get_select_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.270675, "supported_languages": null}, "macro.dbt.default__get_column_names": {"name": "default__get_column_names", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/create.sql", "original_file_path": "macros/relations/table/create.sql", "unique_id": "macro.dbt.default__get_column_names", "macro_sql": "{% macro default__get_column_names() %}\n  {#- loop through user_provided_columns to get column names -#}\n    {%- set user_provided_columns = model['columns'] -%}\n    {%- for i in user_provided_columns %}\n      {%- set col = user_provided_columns[i] -%}\n      {%- set col_name = adapter.quote(col['name']) if col.get('quote') else col['name'] -%}\n      {{ col_name }}{{ \", \" if not loop.last }}\n    {%- endfor -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2715354, "supported_languages": null}, "macro.dbt.get_select_subquery": {"name": "get_select_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/create.sql", "original_file_path": "macros/relations/table/create.sql", "unique_id": "macro.dbt.get_select_subquery", "macro_sql": "{% macro get_select_subquery(sql) %}\n  {{ return(adapter.dispatch('get_select_subquery', 'dbt')(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_select_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2718933, "supported_languages": null}, "macro.dbt.default__get_select_subquery": {"name": "default__get_select_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/create.sql", "original_file_path": "macros/relations/table/create.sql", "unique_id": "macro.dbt.default__get_select_subquery", "macro_sql": "{% macro default__get_select_subquery(sql) %}\n    select {{ adapter.dispatch('get_column_names', 'dbt')() }}\n    from (\n        {{ sql }}\n    ) as model_subq\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_column_names"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.272228, "supported_languages": null}, "macro.dbt.drop_table": {"name": "drop_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/drop.sql", "original_file_path": "macros/relations/table/drop.sql", "unique_id": "macro.dbt.drop_table", "macro_sql": "{% macro drop_table(relation) -%}\n    {{- adapter.dispatch('drop_table', 'dbt')(relation) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__drop_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2726943, "supported_languages": null}, "macro.dbt.default__drop_table": {"name": "default__drop_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/drop.sql", "original_file_path": "macros/relations/table/drop.sql", "unique_id": "macro.dbt.default__drop_table", "macro_sql": "{% macro default__drop_table(relation) -%}\n    drop table if exists {{ relation.render() }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.272934, "supported_languages": null}, "macro.dbt.get_rename_table_sql": {"name": "get_rename_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/rename.sql", "original_file_path": "macros/relations/table/rename.sql", "unique_id": "macro.dbt.get_rename_table_sql", "macro_sql": "{% macro get_rename_table_sql(relation, new_name) %}\n    {{- adapter.dispatch('get_rename_table_sql', 'dbt')(relation, new_name) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_rename_table_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.273423, "supported_languages": null}, "macro.dbt.default__get_rename_table_sql": {"name": "default__get_rename_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/rename.sql", "original_file_path": "macros/relations/table/rename.sql", "unique_id": "macro.dbt.default__get_rename_table_sql", "macro_sql": "{% macro default__get_rename_table_sql(relation, new_name) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_rename_table_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2737029, "supported_languages": null}, "macro.dbt.get_replace_table_sql": {"name": "get_replace_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/replace.sql", "original_file_path": "macros/relations/table/replace.sql", "unique_id": "macro.dbt.get_replace_table_sql", "macro_sql": "{% macro get_replace_table_sql(relation, sql) %}\n    {{- adapter.dispatch('get_replace_table_sql', 'dbt')(relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_replace_table_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.274189, "supported_languages": null}, "macro.dbt.default__get_replace_table_sql": {"name": "default__get_replace_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/table/replace.sql", "original_file_path": "macros/relations/table/replace.sql", "unique_id": "macro.dbt.default__get_replace_table_sql", "macro_sql": "{% macro default__get_replace_table_sql(relation, sql) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_replace_table_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2744672, "supported_languages": null}, "macro.dbt.get_create_view_as_sql": {"name": "get_create_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/create.sql", "original_file_path": "macros/relations/view/create.sql", "unique_id": "macro.dbt.get_create_view_as_sql", "macro_sql": "{% macro get_create_view_as_sql(relation, sql) -%}\n  {{ adapter.dispatch('get_create_view_as_sql', 'dbt')(relation, sql) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_create_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2751179, "supported_languages": null}, "macro.dbt.default__get_create_view_as_sql": {"name": "default__get_create_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/create.sql", "original_file_path": "macros/relations/view/create.sql", "unique_id": "macro.dbt.default__get_create_view_as_sql", "macro_sql": "{% macro default__get_create_view_as_sql(relation, sql) -%}\n  {{ return(create_view_as(relation, sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.create_view_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2755523, "supported_languages": null}, "macro.dbt.create_view_as": {"name": "create_view_as", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/create.sql", "original_file_path": "macros/relations/view/create.sql", "unique_id": "macro.dbt.create_view_as", "macro_sql": "{% macro create_view_as(relation, sql) -%}\n  {{ adapter.dispatch('create_view_as', 'dbt')(relation, sql) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_view_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2759054, "supported_languages": null}, "macro.dbt.default__create_view_as": {"name": "default__create_view_as", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/create.sql", "original_file_path": "macros/relations/view/create.sql", "unique_id": "macro.dbt.default__create_view_as", "macro_sql": "{% macro default__create_view_as(relation, sql) -%}\n  {%- set sql_header = config.get('sql_header', none) -%}\n\n  {{ sql_header if sql_header is not none }}\n  create view {{ relation.render() }}\n    {% set contract_config = config.get('contract') %}\n    {% if contract_config.enforced %}\n      {{ get_assert_columns_equivalent(sql) }}\n    {%- endif %}\n  as (\n    {{ sql }}\n  );\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2766962, "supported_languages": null}, "macro.dbt.drop_view": {"name": "drop_view", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/drop.sql", "original_file_path": "macros/relations/view/drop.sql", "unique_id": "macro.dbt.drop_view", "macro_sql": "{% macro drop_view(relation) -%}\n    {{- adapter.dispatch('drop_view', 'dbt')(relation) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__drop_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2771645, "supported_languages": null}, "macro.dbt.default__drop_view": {"name": "default__drop_view", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/drop.sql", "original_file_path": "macros/relations/view/drop.sql", "unique_id": "macro.dbt.default__drop_view", "macro_sql": "{% macro default__drop_view(relation) -%}\n    drop view if exists {{ relation.render() }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2774022, "supported_languages": null}, "macro.dbt.get_rename_view_sql": {"name": "get_rename_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/rename.sql", "original_file_path": "macros/relations/view/rename.sql", "unique_id": "macro.dbt.get_rename_view_sql", "macro_sql": "{% macro get_rename_view_sql(relation, new_name) %}\n    {{- adapter.dispatch('get_rename_view_sql', 'dbt')(relation, new_name) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_rename_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2779076, "supported_languages": null}, "macro.dbt.default__get_rename_view_sql": {"name": "default__get_rename_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/rename.sql", "original_file_path": "macros/relations/view/rename.sql", "unique_id": "macro.dbt.default__get_rename_view_sql", "macro_sql": "{% macro default__get_rename_view_sql(relation, new_name) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_rename_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2781851, "supported_languages": null}, "macro.dbt.get_replace_view_sql": {"name": "get_replace_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/replace.sql", "original_file_path": "macros/relations/view/replace.sql", "unique_id": "macro.dbt.get_replace_view_sql", "macro_sql": "{% macro get_replace_view_sql(relation, sql) %}\n    {{- adapter.dispatch('get_replace_view_sql', 'dbt')(relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__get_replace_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2791119, "supported_languages": null}, "macro.dbt.default__get_replace_view_sql": {"name": "default__get_replace_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/replace.sql", "original_file_path": "macros/relations/view/replace.sql", "unique_id": "macro.dbt.default__get_replace_view_sql", "macro_sql": "{% macro default__get_replace_view_sql(relation, sql) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_replace_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2793994, "supported_languages": null}, "macro.dbt.create_or_replace_view": {"name": "create_or_replace_view", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/replace.sql", "original_file_path": "macros/relations/view/replace.sql", "unique_id": "macro.dbt.create_or_replace_view", "macro_sql": "{% macro create_or_replace_view() %}\n  {%- set identifier = model['alias'] -%}\n\n  {%- set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) -%}\n  {%- set exists_as_view = (old_relation is not none and old_relation.is_view) -%}\n\n  {%- set target_relation = api.Relation.create(\n      identifier=identifier, schema=schema, database=database,\n      type='view') -%}\n  {% set grant_config = config.get('grants') %}\n\n  {{ run_hooks(pre_hooks) }}\n\n  -- If there's a table with the same name and we weren't told to full refresh,\n  -- that's an error. If we were told to full refresh, drop it. This behavior differs\n  -- for Snowflake and BigQuery, so multiple dispatch is used.\n  {%- if old_relation is not none and old_relation.is_table -%}\n    {{ handle_existing_table(should_full_refresh(), old_relation) }}\n  {%- endif -%}\n\n  -- build model\n  {% call statement('main') -%}\n    {{ get_create_view_as_sql(target_relation, sql) }}\n  {%- endcall %}\n\n  {% set should_revoke = should_revoke(exists_as_view, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {{ run_hooks(post_hooks) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_hooks", "macro.dbt.handle_existing_table", "macro.dbt.should_full_refresh", "macro.dbt.statement", "macro.dbt.get_create_view_as_sql", "macro.dbt.should_revoke", "macro.dbt.apply_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2813582, "supported_languages": null}, "macro.dbt.handle_existing_table": {"name": "handle_existing_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/replace.sql", "original_file_path": "macros/relations/view/replace.sql", "unique_id": "macro.dbt.handle_existing_table", "macro_sql": "{% macro handle_existing_table(full_refresh, old_relation) %}\n    {{ adapter.dispatch('handle_existing_table', 'dbt')(full_refresh, old_relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__handle_existing_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2817369, "supported_languages": null}, "macro.dbt.default__handle_existing_table": {"name": "default__handle_existing_table", "resource_type": "macro", "package_name": "dbt", "path": "macros/relations/view/replace.sql", "original_file_path": "macros/relations/view/replace.sql", "unique_id": "macro.dbt.default__handle_existing_table", "macro_sql": "{% macro default__handle_existing_table(full_refresh, old_relation) %}\n    {{ log(\"Dropping relation \" ~ old_relation.render() ~ \" because it is of type \" ~ old_relation.type) }}\n    {{ adapter.drop_relation(old_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2821712, "supported_languages": null}, "macro.dbt.get_fixture_sql": {"name": "get_fixture_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/unit_test_sql/get_fixture_sql.sql", "original_file_path": "macros/unit_test_sql/get_fixture_sql.sql", "unique_id": "macro.dbt.get_fixture_sql", "macro_sql": "{% macro get_fixture_sql(rows, column_name_to_data_types) %}\n-- Fixture for {{ model.name }}\n{% set default_row = {} %}\n\n{%- if not column_name_to_data_types -%}\n{#-- Use defer_relation IFF it is available in the manifest and 'this' is missing from the database --#}\n{%-   set this_or_defer_relation = defer_relation if (defer_relation and not load_relation(this)) else this -%}\n{%-   set columns_in_relation = adapter.get_columns_in_relation(this_or_defer_relation) -%}\n\n{%-   set column_name_to_data_types = {} -%}\n{%-   for column in columns_in_relation -%}\n{#-- This needs to be a case-insensitive comparison --#}\n{%-     do column_name_to_data_types.update({column.name|lower: column.data_type}) -%}\n{%-   endfor -%}\n{%- endif -%}\n\n{%- if not column_name_to_data_types -%}\n    {{ exceptions.raise_compiler_error(\"Not able to get columns for unit test '\" ~ model.name ~ \"' from relation \" ~ this ~ \" because the relation doesn't exist\") }}\n{%- endif -%}\n\n{%- for column_name, column_type in column_name_to_data_types.items() -%}\n    {%- do default_row.update({column_name: (safe_cast(\"null\", column_type) | trim )}) -%}\n{%- endfor -%}\n\n{{ validate_fixture_rows(rows, row_number) }}\n\n{%- for row in rows -%}\n{%-   set formatted_row = format_row(row, column_name_to_data_types) -%}\n{%-   set default_row_copy = default_row.copy() -%}\n{%-   do default_row_copy.update(formatted_row) -%}\nselect\n{%-   for column_name, column_value in default_row_copy.items() %} {{ column_value }} as {{ column_name }}{% if not loop.last -%}, {%- endif %}\n{%-   endfor %}\n{%-   if not loop.last %}\nunion all\n{%    endif %}\n{%- endfor -%}\n\n{%- if (rows | length) == 0 -%}\n    select\n    {%- for column_name, column_value in default_row.items() %} {{ column_value }} as {{ column_name }}{% if not loop.last -%},{%- endif %}\n    {%- endfor %}\n    limit 0\n{%- endif -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.load_relation", "macro.dbt.safe_cast", "macro.dbt.validate_fixture_rows", "macro.dbt.format_row"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2864602, "supported_languages": null}, "macro.dbt.get_expected_sql": {"name": "get_expected_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros/unit_test_sql/get_fixture_sql.sql", "original_file_path": "macros/unit_test_sql/get_fixture_sql.sql", "unique_id": "macro.dbt.get_expected_sql", "macro_sql": "{% macro get_expected_sql(rows, column_name_to_data_types) %}\n\n{%- if (rows | length) == 0 -%}\n    select * from dbt_internal_unit_test_actual\n    limit 0\n{%- else -%}\n{%- for row in rows -%}\n{%- set formatted_row = format_row(row, column_name_to_data_types) -%}\nselect\n{%- for column_name, column_value in formatted_row.items() %} {{ column_value }} as {{ column_name }}{% if not loop.last -%}, {%- endif %}\n{%- endfor %}\n{%- if not loop.last %}\nunion all\n{% endif %}\n{%- endfor -%}\n{%- endif -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.format_row"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.287492, "supported_languages": null}, "macro.dbt.format_row": {"name": "format_row", "resource_type": "macro", "package_name": "dbt", "path": "macros/unit_test_sql/get_fixture_sql.sql", "original_file_path": "macros/unit_test_sql/get_fixture_sql.sql", "unique_id": "macro.dbt.format_row", "macro_sql": "\n\n{%- macro format_row(row, column_name_to_data_types) -%}\n    {#-- generate case-insensitive formatted row --#}\n    {% set formatted_row = {} %}\n    {%- for column_name, column_value in row.items() -%}\n        {% set column_name = column_name|lower %}\n\n        {%- if column_name not in column_name_to_data_types %}\n            {#-- if user-provided row contains column name that relation does not contain, raise an error --#}\n            {% set fixture_name = \"expected output\" if model.resource_type == 'unit_test' else (\"'\" ~ model.name ~ \"'\") %}\n            {{ exceptions.raise_compiler_error(\n                \"Invalid column name: '\" ~ column_name ~ \"' in unit test fixture for \" ~ fixture_name ~ \".\"\n                \"\\nAccepted columns for \" ~ fixture_name ~ \" are: \" ~ (column_name_to_data_types.keys()|list)\n            ) }}\n        {%- endif -%}\n\n        {%- set column_type = column_name_to_data_types[column_name] %}\n\n        {#-- sanitize column_value: wrap yaml strings in quotes, apply cast --#}\n        {%- set column_value_clean = column_value -%}\n        {%- if column_value is string -%}\n            {%- set column_value_clean = dbt.string_literal(dbt.escape_single_quotes(column_value)) -%}\n        {%- elif column_value is none -%}\n            {%- set column_value_clean = 'null' -%}\n        {%- endif -%}\n\n        {%- set row_update = {column_name: safe_cast(column_value_clean, column_type) } -%}\n        {%- do formatted_row.update(row_update) -%}\n    {%- endfor -%}\n    {{ return(formatted_row) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.string_literal", "macro.dbt.escape_single_quotes", "macro.dbt.safe_cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2895029, "supported_languages": null}, "macro.dbt.validate_fixture_rows": {"name": "validate_fixture_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros/unit_test_sql/get_fixture_sql.sql", "original_file_path": "macros/unit_test_sql/get_fixture_sql.sql", "unique_id": "macro.dbt.validate_fixture_rows", "macro_sql": "{%- macro validate_fixture_rows(rows, row_number) -%}\n  {{ return(adapter.dispatch('validate_fixture_rows', 'dbt')(rows, row_number)) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__validate_fixture_rows"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2899086, "supported_languages": null}, "macro.dbt.default__validate_fixture_rows": {"name": "default__validate_fixture_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros/unit_test_sql/get_fixture_sql.sql", "original_file_path": "macros/unit_test_sql/get_fixture_sql.sql", "unique_id": "macro.dbt.default__validate_fixture_rows", "macro_sql": "{%- macro default__validate_fixture_rows(rows, row_number) -%}\n  {# This is an abstract method for adapter overrides as needed #}\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2900996, "supported_languages": null}, "macro.dbt.any_value": {"name": "any_value", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/any_value.sql", "original_file_path": "macros/utils/any_value.sql", "unique_id": "macro.dbt.any_value", "macro_sql": "{% macro any_value(expression) -%}\n    {{ return(adapter.dispatch('any_value', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__any_value"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2905693, "supported_languages": null}, "macro.dbt.default__any_value": {"name": "default__any_value", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/any_value.sql", "original_file_path": "macros/utils/any_value.sql", "unique_id": "macro.dbt.default__any_value", "macro_sql": "{% macro default__any_value(expression) -%}\n\n    any_value({{ expression }})\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2907772, "supported_languages": null}, "macro.dbt.array_append": {"name": "array_append", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/array_append.sql", "original_file_path": "macros/utils/array_append.sql", "unique_id": "macro.dbt.array_append", "macro_sql": "{% macro array_append(array, new_element) -%}\n  {{ return(adapter.dispatch('array_append', 'dbt')(array, new_element)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__array_append"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.291341, "supported_languages": null}, "macro.dbt.default__array_append": {"name": "default__array_append", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/array_append.sql", "original_file_path": "macros/utils/array_append.sql", "unique_id": "macro.dbt.default__array_append", "macro_sql": "{% macro default__array_append(array, new_element) -%}\n    array_append({{ array }}, {{ new_element }})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2916198, "supported_languages": null}, "macro.dbt.array_concat": {"name": "array_concat", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/array_concat.sql", "original_file_path": "macros/utils/array_concat.sql", "unique_id": "macro.dbt.array_concat", "macro_sql": "{% macro array_concat(array_1, array_2) -%}\n  {{ return(adapter.dispatch('array_concat', 'dbt')(array_1, array_2)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__array_concat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2921329, "supported_languages": null}, "macro.dbt.default__array_concat": {"name": "default__array_concat", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/array_concat.sql", "original_file_path": "macros/utils/array_concat.sql", "unique_id": "macro.dbt.default__array_concat", "macro_sql": "{% macro default__array_concat(array_1, array_2) -%}\n    array_cat({{ array_1 }}, {{ array_2 }})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2923958, "supported_languages": null}, "macro.dbt.array_construct": {"name": "array_construct", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/array_construct.sql", "original_file_path": "macros/utils/array_construct.sql", "unique_id": "macro.dbt.array_construct", "macro_sql": "{% macro array_construct(inputs=[], data_type=api.Column.translate_type('integer')) -%}\n  {{ return(adapter.dispatch('array_construct', 'dbt')(inputs, data_type)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__array_construct"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2930732, "supported_languages": null}, "macro.dbt.default__array_construct": {"name": "default__array_construct", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/array_construct.sql", "original_file_path": "macros/utils/array_construct.sql", "unique_id": "macro.dbt.default__array_construct", "macro_sql": "{% macro default__array_construct(inputs, data_type) -%}\n    {% if inputs|length > 0 %}\n    array[ {{ inputs|join(' , ') }} ]\n    {% else %}\n    array[]::{{data_type}}[]\n    {% endif %}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2935276, "supported_languages": null}, "macro.dbt.bool_or": {"name": "bool_or", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/bool_or.sql", "original_file_path": "macros/utils/bool_or.sql", "unique_id": "macro.dbt.bool_or", "macro_sql": "{% macro bool_or(expression) -%}\n    {{ return(adapter.dispatch('bool_or', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__bool_or"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2939968, "supported_languages": null}, "macro.dbt.default__bool_or": {"name": "default__bool_or", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/bool_or.sql", "original_file_path": "macros/utils/bool_or.sql", "unique_id": "macro.dbt.default__bool_or", "macro_sql": "{% macro default__bool_or(expression) -%}\n\n    bool_or({{ expression }})\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2942047, "supported_languages": null}, "macro.dbt.cast": {"name": "cast", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/cast.sql", "original_file_path": "macros/utils/cast.sql", "unique_id": "macro.dbt.cast", "macro_sql": "{% macro cast(field, type) %}\n  {{ return(adapter.dispatch('cast', 'dbt') (field, type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2947192, "supported_languages": null}, "macro.dbt.default__cast": {"name": "default__cast", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/cast.sql", "original_file_path": "macros/utils/cast.sql", "unique_id": "macro.dbt.default__cast", "macro_sql": "{% macro default__cast(field, type) %}\n    cast({{field}} as {{type}})\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2949753, "supported_languages": null}, "macro.dbt.cast_bool_to_text": {"name": "cast_bool_to_text", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/cast_bool_to_text.sql", "original_file_path": "macros/utils/cast_bool_to_text.sql", "unique_id": "macro.dbt.cast_bool_to_text", "macro_sql": "{% macro cast_bool_to_text(field) %}\n  {{ adapter.dispatch('cast_bool_to_text', 'dbt') (field) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__cast_bool_to_text"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2954302, "supported_languages": null}, "macro.dbt.default__cast_bool_to_text": {"name": "default__cast_bool_to_text", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/cast_bool_to_text.sql", "original_file_path": "macros/utils/cast_bool_to_text.sql", "unique_id": "macro.dbt.default__cast_bool_to_text", "macro_sql": "{% macro default__cast_bool_to_text(field) %}\n    cast({{ field }} as {{ api.Column.translate_type('string') }})\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.295754, "supported_languages": null}, "macro.dbt.concat": {"name": "concat", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/concat.sql", "original_file_path": "macros/utils/concat.sql", "unique_id": "macro.dbt.concat", "macro_sql": "{% macro concat(fields) -%}\n  {{ return(adapter.dispatch('concat', 'dbt')(fields)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__concat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.296222, "supported_languages": null}, "macro.dbt.default__concat": {"name": "default__concat", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/concat.sql", "original_file_path": "macros/utils/concat.sql", "unique_id": "macro.dbt.default__concat", "macro_sql": "{% macro default__concat(fields) -%}\n    {{ fields|join(' || ') }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2964687, "supported_languages": null}, "macro.dbt.type_string": {"name": "type_string", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.type_string", "macro_sql": "\n\n{%- macro type_string() -%}\n  {{ return(adapter.dispatch('type_string', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_string"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2977011, "supported_languages": null}, "macro.dbt.default__type_string": {"name": "default__type_string", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.default__type_string", "macro_sql": "{% macro default__type_string() %}\n    {{ return(api.Column.translate_type(\"string\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2979958, "supported_languages": null}, "macro.dbt.type_timestamp": {"name": "type_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.type_timestamp", "macro_sql": "\n\n{%- macro type_timestamp() -%}\n  {{ return(adapter.dispatch('type_timestamp', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.298439, "supported_languages": null}, "macro.dbt.default__type_timestamp": {"name": "default__type_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.default__type_timestamp", "macro_sql": "{% macro default__type_timestamp() %}\n    {{ return(api.Column.translate_type(\"timestamp\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2987335, "supported_languages": null}, "macro.dbt.type_float": {"name": "type_float", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.type_float", "macro_sql": "\n\n{%- macro type_float() -%}\n  {{ return(adapter.dispatch('type_float', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_float"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2990556, "supported_languages": null}, "macro.dbt.default__type_float": {"name": "default__type_float", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.default__type_float", "macro_sql": "{% macro default__type_float() %}\n    {{ return(api.Column.translate_type(\"float\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2993448, "supported_languages": null}, "macro.dbt.type_numeric": {"name": "type_numeric", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.type_numeric", "macro_sql": "\n\n{%- macro type_numeric() -%}\n  {{ return(adapter.dispatch('type_numeric', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_numeric"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2996612, "supported_languages": null}, "macro.dbt.default__type_numeric": {"name": "default__type_numeric", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.default__type_numeric", "macro_sql": "{% macro default__type_numeric() %}\n    {{ return(api.Column.numeric_type(\"numeric\", 28, 6)) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.2999935, "supported_languages": null}, "macro.dbt.type_bigint": {"name": "type_bigint", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.type_bigint", "macro_sql": "\n\n{%- macro type_bigint() -%}\n  {{ return(adapter.dispatch('type_bigint', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_bigint"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3002968, "supported_languages": null}, "macro.dbt.default__type_bigint": {"name": "default__type_bigint", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.default__type_bigint", "macro_sql": "{% macro default__type_bigint() %}\n    {{ return(api.Column.translate_type(\"bigint\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3005805, "supported_languages": null}, "macro.dbt.type_int": {"name": "type_int", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.type_int", "macro_sql": "\n\n{%- macro type_int() -%}\n  {{ return(adapter.dispatch('type_int', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_int"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3008804, "supported_languages": null}, "macro.dbt.default__type_int": {"name": "default__type_int", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.default__type_int", "macro_sql": "{%- macro default__type_int() -%}\n  {{ return(api.Column.translate_type(\"integer\")) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3011556, "supported_languages": null}, "macro.dbt.type_boolean": {"name": "type_boolean", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.type_boolean", "macro_sql": "\n\n{%- macro type_boolean() -%}\n  {{ return(adapter.dispatch('type_boolean', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_boolean"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.301458, "supported_languages": null}, "macro.dbt.default__type_boolean": {"name": "default__type_boolean", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/data_types.sql", "original_file_path": "macros/utils/data_types.sql", "unique_id": "macro.dbt.default__type_boolean", "macro_sql": "{%- macro default__type_boolean() -%}\n  {{ return(api.Column.translate_type(\"boolean\")) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.301736, "supported_languages": null}, "macro.dbt.date": {"name": "date", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date.sql", "original_file_path": "macros/utils/date.sql", "unique_id": "macro.dbt.date", "macro_sql": "{% macro date(year, month, day) %}\n  {{ return(adapter.dispatch('date', 'dbt') (year, month, day)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__date"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3023314, "supported_languages": null}, "macro.dbt.default__date": {"name": "default__date", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date.sql", "original_file_path": "macros/utils/date.sql", "unique_id": "macro.dbt.default__date", "macro_sql": "{% macro default__date(year, month, day) -%}\n    {%- set dt = modules.datetime.date(year, month, day) -%}\n    {%- set iso_8601_formatted_date = dt.strftime('%Y-%m-%d') -%}\n    to_date('{{ iso_8601_formatted_date }}', 'YYYY-MM-DD')\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3028588, "supported_languages": null}, "macro.dbt.get_intervals_between": {"name": "get_intervals_between", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date_spine.sql", "original_file_path": "macros/utils/date_spine.sql", "unique_id": "macro.dbt.get_intervals_between", "macro_sql": "{% macro get_intervals_between(start_date, end_date, datepart) -%}\n    {{ return(adapter.dispatch('get_intervals_between', 'dbt')(start_date, end_date, datepart)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_intervals_between"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.304325, "supported_languages": null}, "macro.dbt.default__get_intervals_between": {"name": "default__get_intervals_between", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date_spine.sql", "original_file_path": "macros/utils/date_spine.sql", "unique_id": "macro.dbt.default__get_intervals_between", "macro_sql": "{% macro default__get_intervals_between(start_date, end_date, datepart) -%}\n    {%- call statement('get_intervals_between', fetch_result=True) %}\n\n        select {{ dbt.datediff(start_date, end_date, datepart) }}\n\n    {%- endcall -%}\n\n    {%- set value_list = load_result('get_intervals_between') -%}\n\n    {%- if value_list and value_list['data'] -%}\n        {%- set values = value_list['data'] | map(attribute=0) | list %}\n        {{ return(values[0]) }}\n    {%- else -%}\n        {{ return(1) }}\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.datediff"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3061671, "supported_languages": null}, "macro.dbt.date_spine": {"name": "date_spine", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date_spine.sql", "original_file_path": "macros/utils/date_spine.sql", "unique_id": "macro.dbt.date_spine", "macro_sql": "{% macro date_spine(datepart, start_date, end_date) %}\n    {{ return(adapter.dispatch('date_spine', 'dbt')(datepart, start_date, end_date)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__date_spine"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3069377, "supported_languages": null}, "macro.dbt.default__date_spine": {"name": "default__date_spine", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date_spine.sql", "original_file_path": "macros/utils/date_spine.sql", "unique_id": "macro.dbt.default__date_spine", "macro_sql": "{% macro default__date_spine(datepart, start_date, end_date) %}\n\n\n    {# call as follows:\n\n    date_spine(\n        \"day\",\n        \"to_date('01/01/2016', 'mm/dd/yyyy')\",\n        \"dbt.dateadd(week, 1, current_date)\"\n    ) #}\n\n\n    with rawdata as (\n\n        {{dbt.generate_series(\n            dbt.get_intervals_between(start_date, end_date, datepart)\n        )}}\n\n    ),\n\n    all_periods as (\n\n        select (\n            {{\n                dbt.dateadd(\n                    datepart,\n                    \"row_number() over (order by 1) - 1\",\n                    start_date\n                )\n            }}\n        ) as date_{{datepart}}\n        from rawdata\n\n    ),\n\n    filtered as (\n\n        select *\n        from all_periods\n        where date_{{datepart}} <= {{ end_date }}\n\n    )\n\n    select * from filtered\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.generate_series", "macro.dbt.get_intervals_between", "macro.dbt.dateadd"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.308111, "supported_languages": null}, "macro.dbt.date_trunc": {"name": "date_trunc", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date_trunc.sql", "original_file_path": "macros/utils/date_trunc.sql", "unique_id": "macro.dbt.date_trunc", "macro_sql": "{% macro date_trunc(datepart, date) -%}\n  {{ return(adapter.dispatch('date_trunc', 'dbt') (datepart, date)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__date_trunc"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3090484, "supported_languages": null}, "macro.dbt.default__date_trunc": {"name": "default__date_trunc", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/date_trunc.sql", "original_file_path": "macros/utils/date_trunc.sql", "unique_id": "macro.dbt.default__date_trunc", "macro_sql": "{% macro default__date_trunc(datepart, date) -%}\n    date_trunc('{{datepart}}', {{date}})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.309509, "supported_languages": null}, "macro.dbt.dateadd": {"name": "dateadd", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/dateadd.sql", "original_file_path": "macros/utils/dateadd.sql", "unique_id": "macro.dbt.dateadd", "macro_sql": "{% macro dateadd(datepart, interval, from_date_or_timestamp) %}\n  {{ return(adapter.dispatch('dateadd', 'dbt')(datepart, interval, from_date_or_timestamp)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__dateadd"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3102782, "supported_languages": null}, "macro.dbt.default__dateadd": {"name": "default__dateadd", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/dateadd.sql", "original_file_path": "macros/utils/dateadd.sql", "unique_id": "macro.dbt.default__dateadd", "macro_sql": "{% macro default__dateadd(datepart, interval, from_date_or_timestamp) %}\n\n    dateadd(\n        {{ datepart }},\n        {{ interval }},\n        {{ from_date_or_timestamp }}\n        )\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3107417, "supported_languages": null}, "macro.dbt.datediff": {"name": "datediff", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/datediff.sql", "original_file_path": "macros/utils/datediff.sql", "unique_id": "macro.dbt.datediff", "macro_sql": "{% macro datediff(first_date, second_date, datepart) %}\n  {{ return(adapter.dispatch('datediff', 'dbt')(first_date, second_date, datepart)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__datediff"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3113306, "supported_languages": null}, "macro.dbt.default__datediff": {"name": "default__datediff", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/datediff.sql", "original_file_path": "macros/utils/datediff.sql", "unique_id": "macro.dbt.default__datediff", "macro_sql": "{% macro default__datediff(first_date, second_date, datepart) -%}\n\n    datediff(\n        {{ datepart }},\n        {{ first_date }},\n        {{ second_date }}\n        )\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3116608, "supported_languages": null}, "macro.dbt.equals": {"name": "equals", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/equals.sql", "original_file_path": "macros/utils/equals.sql", "unique_id": "macro.dbt.equals", "macro_sql": "{% macro equals(expr1, expr2) %}\n    {{ return(adapter.dispatch('equals', 'dbt') (expr1, expr2)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.312251, "supported_languages": null}, "macro.dbt.default__equals": {"name": "default__equals", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/equals.sql", "original_file_path": "macros/utils/equals.sql", "unique_id": "macro.dbt.default__equals", "macro_sql": "{% macro default__equals(expr1, expr2) -%}\n{%- if adapter.behavior.enable_truthy_nulls_equals_macro.no_warn %}\n    case when (({{ expr1 }} = {{ expr2 }}) or ({{ expr1 }} is null and {{ expr2 }} is null))\n        then 0\n        else 1\n    end = 0\n{%- else -%}\n    ({{ expr1 }} = {{ expr2 }})\n{%- endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.312796, "supported_languages": null}, "macro.dbt.escape_single_quotes": {"name": "escape_single_quotes", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/escape_single_quotes.sql", "original_file_path": "macros/utils/escape_single_quotes.sql", "unique_id": "macro.dbt.escape_single_quotes", "macro_sql": "{% macro escape_single_quotes(expression) %}\n      {{ return(adapter.dispatch('escape_single_quotes', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__escape_single_quotes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.313304, "supported_languages": null}, "macro.dbt.default__escape_single_quotes": {"name": "default__escape_single_quotes", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/escape_single_quotes.sql", "original_file_path": "macros/utils/escape_single_quotes.sql", "unique_id": "macro.dbt.default__escape_single_quotes", "macro_sql": "{% macro default__escape_single_quotes(expression) -%}\n{{ expression | replace(\"'\",\"''\") }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.313576, "supported_languages": null}, "macro.dbt.except": {"name": "except", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/except.sql", "original_file_path": "macros/utils/except.sql", "unique_id": "macro.dbt.except", "macro_sql": "{% macro except() %}\n  {{ return(adapter.dispatch('except', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__except"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3140037, "supported_languages": null}, "macro.dbt.default__except": {"name": "default__except", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/except.sql", "original_file_path": "macros/utils/except.sql", "unique_id": "macro.dbt.default__except", "macro_sql": "{% macro default__except() %}\n\n    except\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3141627, "supported_languages": null}, "macro.dbt.get_powers_of_two": {"name": "get_powers_of_two", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/generate_series.sql", "original_file_path": "macros/utils/generate_series.sql", "unique_id": "macro.dbt.get_powers_of_two", "macro_sql": "{% macro get_powers_of_two(upper_bound) %}\n    {{ return(adapter.dispatch('get_powers_of_two', 'dbt')(upper_bound)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_powers_of_two"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.314941, "supported_languages": null}, "macro.dbt.default__get_powers_of_two": {"name": "default__get_powers_of_two", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/generate_series.sql", "original_file_path": "macros/utils/generate_series.sql", "unique_id": "macro.dbt.default__get_powers_of_two", "macro_sql": "{% macro default__get_powers_of_two(upper_bound) %}\n\n    {% if upper_bound <= 0 %}\n    {{ exceptions.raise_compiler_error(\"upper bound must be positive\") }}\n    {% endif %}\n\n    {% for _ in range(1, 100) %}\n       {% if upper_bound <= 2 ** loop.index %}{{ return(loop.index) }}{% endif %}\n    {% endfor %}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3157024, "supported_languages": null}, "macro.dbt.generate_series": {"name": "generate_series", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/generate_series.sql", "original_file_path": "macros/utils/generate_series.sql", "unique_id": "macro.dbt.generate_series", "macro_sql": "{% macro generate_series(upper_bound) %}\n    {{ return(adapter.dispatch('generate_series', 'dbt')(upper_bound)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_series"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3160577, "supported_languages": null}, "macro.dbt.default__generate_series": {"name": "default__generate_series", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/generate_series.sql", "original_file_path": "macros/utils/generate_series.sql", "unique_id": "macro.dbt.default__generate_series", "macro_sql": "{% macro default__generate_series(upper_bound) %}\n\n    {% set n = dbt.get_powers_of_two(upper_bound) %}\n\n    with p as (\n        select 0 as generated_number union all select 1\n    ), unioned as (\n\n    select\n\n    {% for i in range(n) %}\n    p{{i}}.generated_number * power(2, {{i}})\n    {% if not loop.last %} + {% endif %}\n    {% endfor %}\n    + 1\n    as generated_number\n\n    from\n\n    {% for i in range(n) %}\n    p as p{{i}}\n    {% if not loop.last %} cross join {% endif %}\n    {% endfor %}\n\n    )\n\n    select *\n    from unioned\n    where generated_number <= {{upper_bound}}\n    order by generated_number\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_powers_of_two"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3169954, "supported_languages": null}, "macro.dbt.hash": {"name": "hash", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/hash.sql", "original_file_path": "macros/utils/hash.sql", "unique_id": "macro.dbt.hash", "macro_sql": "{% macro hash(field) -%}\n  {{ return(adapter.dispatch('hash', 'dbt') (field)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__hash"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3174896, "supported_languages": null}, "macro.dbt.default__hash": {"name": "default__hash", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/hash.sql", "original_file_path": "macros/utils/hash.sql", "unique_id": "macro.dbt.default__hash", "macro_sql": "{% macro default__hash(field) -%}\n    md5(cast({{ field }} as {{ api.Column.translate_type('string') }}))\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.317796, "supported_languages": null}, "macro.dbt.intersect": {"name": "intersect", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/intersect.sql", "original_file_path": "macros/utils/intersect.sql", "unique_id": "macro.dbt.intersect", "macro_sql": "{% macro intersect() %}\n  {{ return(adapter.dispatch('intersect', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__intersect"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3182263, "supported_languages": null}, "macro.dbt.default__intersect": {"name": "default__intersect", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/intersect.sql", "original_file_path": "macros/utils/intersect.sql", "unique_id": "macro.dbt.default__intersect", "macro_sql": "{% macro default__intersect() %}\n\n    intersect\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3183832, "supported_languages": null}, "macro.dbt.last_day": {"name": "last_day", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/last_day.sql", "original_file_path": "macros/utils/last_day.sql", "unique_id": "macro.dbt.last_day", "macro_sql": "{% macro last_day(date, datepart) %}\n  {{ return(adapter.dispatch('last_day', 'dbt') (date, datepart)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__last_day"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3189542, "supported_languages": null}, "macro.dbt.default_last_day": {"name": "default_last_day", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/last_day.sql", "original_file_path": "macros/utils/last_day.sql", "unique_id": "macro.dbt.default_last_day", "macro_sql": "\n\n{%- macro default_last_day(date, datepart) -%}\n    cast(\n        {{dbt.dateadd('day', '-1',\n        dbt.dateadd(datepart, '1', dbt.date_trunc(datepart, date))\n        )}}\n        as date)\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.dateadd", "macro.dbt.date_trunc"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3195972, "supported_languages": null}, "macro.dbt.default__last_day": {"name": "default__last_day", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/last_day.sql", "original_file_path": "macros/utils/last_day.sql", "unique_id": "macro.dbt.default__last_day", "macro_sql": "{% macro default__last_day(date, datepart) -%}\n    {{dbt.default_last_day(date, datepart)}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default_last_day"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3198912, "supported_languages": null}, "macro.dbt.length": {"name": "length", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/length.sql", "original_file_path": "macros/utils/length.sql", "unique_id": "macro.dbt.length", "macro_sql": "{% macro length(expression) -%}\n    {{ return(adapter.dispatch('length', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__length"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3203623, "supported_languages": null}, "macro.dbt.default__length": {"name": "default__length", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/length.sql", "original_file_path": "macros/utils/length.sql", "unique_id": "macro.dbt.default__length", "macro_sql": "{% macro default__length(expression) %}\n\n    length(\n        {{ expression }}\n    )\n\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.320574, "supported_languages": null}, "macro.dbt.listagg": {"name": "listagg", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/listagg.sql", "original_file_path": "macros/utils/listagg.sql", "unique_id": "macro.dbt.listagg", "macro_sql": "{% macro listagg(measure, delimiter_text=\"','\", order_by_clause=none, limit_num=none) -%}\n    {{ return(adapter.dispatch('listagg', 'dbt') (measure, delimiter_text, order_by_clause, limit_num)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__listagg"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.321374, "supported_languages": null}, "macro.dbt.default__listagg": {"name": "default__listagg", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/listagg.sql", "original_file_path": "macros/utils/listagg.sql", "unique_id": "macro.dbt.default__listagg", "macro_sql": "{% macro default__listagg(measure, delimiter_text, order_by_clause, limit_num) -%}\n\n    {% if limit_num -%}\n    array_to_string(\n        array_slice(\n            array_agg(\n                {{ measure }}\n            ){% if order_by_clause -%}\n            within group ({{ order_by_clause }})\n            {%- endif %}\n            ,0\n            ,{{ limit_num }}\n        ),\n        {{ delimiter_text }}\n        )\n    {%- else %}\n    listagg(\n        {{ measure }},\n        {{ delimiter_text }}\n        )\n        {% if order_by_clause -%}\n        within group ({{ order_by_clause }})\n        {%- endif %}\n    {%- endif %}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3221207, "supported_languages": null}, "macro.dbt.string_literal": {"name": "string_literal", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/literal.sql", "original_file_path": "macros/utils/literal.sql", "unique_id": "macro.dbt.string_literal", "macro_sql": "{%- macro string_literal(value) -%}\n  {{ return(adapter.dispatch('string_literal', 'dbt') (value)) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__string_literal"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3225944, "supported_languages": null}, "macro.dbt.default__string_literal": {"name": "default__string_literal", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/literal.sql", "original_file_path": "macros/utils/literal.sql", "unique_id": "macro.dbt.default__string_literal", "macro_sql": "{% macro default__string_literal(value) -%}\n    '{{ value }}'\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.322804, "supported_languages": null}, "macro.dbt.position": {"name": "position", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/position.sql", "original_file_path": "macros/utils/position.sql", "unique_id": "macro.dbt.position", "macro_sql": "{% macro position(substring_text, string_text) -%}\n    {{ return(adapter.dispatch('position', 'dbt') (substring_text, string_text)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__position"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3233275, "supported_languages": null}, "macro.dbt.default__position": {"name": "default__position", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/position.sql", "original_file_path": "macros/utils/position.sql", "unique_id": "macro.dbt.default__position", "macro_sql": "{% macro default__position(substring_text, string_text) %}\n\n    position(\n        {{ substring_text }} in {{ string_text }}\n    )\n\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3236086, "supported_languages": null}, "macro.dbt.replace": {"name": "replace", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/replace.sql", "original_file_path": "macros/utils/replace.sql", "unique_id": "macro.dbt.replace", "macro_sql": "{% macro replace(field, old_chars, new_chars) -%}\n    {{ return(adapter.dispatch('replace', 'dbt') (field, old_chars, new_chars)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__replace"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3241804, "supported_languages": null}, "macro.dbt.default__replace": {"name": "default__replace", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/replace.sql", "original_file_path": "macros/utils/replace.sql", "unique_id": "macro.dbt.default__replace", "macro_sql": "{% macro default__replace(field, old_chars, new_chars) %}\n\n    replace(\n        {{ field }},\n        {{ old_chars }},\n        {{ new_chars }}\n    )\n\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3244996, "supported_languages": null}, "macro.dbt.right": {"name": "right", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/right.sql", "original_file_path": "macros/utils/right.sql", "unique_id": "macro.dbt.right", "macro_sql": "{% macro right(string_text, length_expression) -%}\n    {{ return(adapter.dispatch('right', 'dbt') (string_text, length_expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__right"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3250246, "supported_languages": null}, "macro.dbt.default__right": {"name": "default__right", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/right.sql", "original_file_path": "macros/utils/right.sql", "unique_id": "macro.dbt.default__right", "macro_sql": "{% macro default__right(string_text, length_expression) %}\n\n    right(\n        {{ string_text }},\n        {{ length_expression }}\n    )\n\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3252933, "supported_languages": null}, "macro.dbt.safe_cast": {"name": "safe_cast", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/safe_cast.sql", "original_file_path": "macros/utils/safe_cast.sql", "unique_id": "macro.dbt.safe_cast", "macro_sql": "{% macro safe_cast(field, type) %}\n  {{ return(adapter.dispatch('safe_cast', 'dbt') (field, type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__safe_cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.325827, "supported_languages": null}, "macro.dbt.default__safe_cast": {"name": "default__safe_cast", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/safe_cast.sql", "original_file_path": "macros/utils/safe_cast.sql", "unique_id": "macro.dbt.default__safe_cast", "macro_sql": "{% macro default__safe_cast(field, type) %}\n    {# most databases don't support this function yet\n    so we just need to use cast #}\n    cast({{field}} as {{type}})\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.326102, "supported_languages": null}, "macro.dbt.split_part": {"name": "split_part", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/split_part.sql", "original_file_path": "macros/utils/split_part.sql", "unique_id": "macro.dbt.split_part", "macro_sql": "{% macro split_part(string_text, delimiter_text, part_number) %}\n  {{ return(adapter.dispatch('split_part', 'dbt') (string_text, delimiter_text, part_number)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_postgres.postgres__split_part"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3267736, "supported_languages": null}, "macro.dbt.default__split_part": {"name": "default__split_part", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/split_part.sql", "original_file_path": "macros/utils/split_part.sql", "unique_id": "macro.dbt.default__split_part", "macro_sql": "{% macro default__split_part(string_text, delimiter_text, part_number) %}\n\n    split_part(\n        {{ string_text }},\n        {{ delimiter_text }},\n        {{ part_number }}\n        )\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3270984, "supported_languages": null}, "macro.dbt._split_part_negative": {"name": "_split_part_negative", "resource_type": "macro", "package_name": "dbt", "path": "macros/utils/split_part.sql", "original_file_path": "macros/utils/split_part.sql", "unique_id": "macro.dbt._split_part_negative", "macro_sql": "{% macro _split_part_negative(string_text, delimiter_text, part_number) %}\n\n    split_part(\n        {{ string_text }},\n        {{ delimiter_text }},\n          length({{ string_text }})\n          - length(\n              replace({{ string_text }},  {{ delimiter_text }}, '')\n          ) + 2 + {{ part_number }}\n        )\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3275304, "supported_languages": null}, "macro.dbt.test_unique": {"name": "test_unique", "resource_type": "macro", "package_name": "dbt", "path": "tests/generic/builtin.sql", "original_file_path": "tests/generic/builtin.sql", "unique_id": "macro.dbt.test_unique", "macro_sql": "{% test unique(model, column_name) %}\n    {% set macro = adapter.dispatch('test_unique', 'dbt') %}\n    {{ macro(model, column_name) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_unique"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3283257, "supported_languages": null}, "macro.dbt.test_not_null": {"name": "test_not_null", "resource_type": "macro", "package_name": "dbt", "path": "tests/generic/builtin.sql", "original_file_path": "tests/generic/builtin.sql", "unique_id": "macro.dbt.test_not_null", "macro_sql": "{% test not_null(model, column_name) %}\n    {% set macro = adapter.dispatch('test_not_null', 'dbt') %}\n    {{ macro(model, column_name) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_not_null"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3291945, "supported_languages": null}, "macro.dbt.test_accepted_values": {"name": "test_accepted_values", "resource_type": "macro", "package_name": "dbt", "path": "tests/generic/builtin.sql", "original_file_path": "tests/generic/builtin.sql", "unique_id": "macro.dbt.test_accepted_values", "macro_sql": "{% test accepted_values(model, column_name, values, quote=True) %}\n    {% set macro = adapter.dispatch('test_accepted_values', 'dbt') %}\n    {{ macro(model, column_name, values, quote) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_accepted_values"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3297791, "supported_languages": null}, "macro.dbt.test_relationships": {"name": "test_relationships", "resource_type": "macro", "package_name": "dbt", "path": "tests/generic/builtin.sql", "original_file_path": "tests/generic/builtin.sql", "unique_id": "macro.dbt.test_relationships", "macro_sql": "{% test relationships(model, column_name, to, field) %}\n    {% set macro = adapter.dispatch('test_relationships', 'dbt') %}\n    {{ macro(model, column_name, to, field) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_relationships"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1752938914.3303242, "supported_languages": null}}, "docs": {"doc.dbt.__overview__": {"name": "__overview__", "resource_type": "doc", "package_name": "dbt", "path": "overview.md", "original_file_path": "docs/overview.md", "unique_id": "doc.dbt.__overview__", "block_contents": "### Welcome!\n\nWelcome to the auto-generated documentation for your dbt project!\n\n### Navigation\n\nYou can use the `Project` and `Database` navigation tabs on the left side of the window to explore the models\nin your project.\n\n#### Project Tab\nThe `Project` tab mirrors the directory structure of your dbt project. In this tab, you can see all of the\nmodels defined in your dbt project, as well as models imported from dbt packages.\n\n#### Database Tab\nThe `Database` tab also exposes your models, but in a format that looks more like a database explorer. This view\nshows relations (tables and views) grouped into database schemas. Note that ephemeral models are _not_ shown\nin this interface, as they do not exist in the database.\n\n### Graph Exploration\nYou can click the blue icon on the bottom-right corner of the page to view the lineage graph of your models.\n\nOn model pages, you'll see the immediate parents and children of the model you're exploring. By clicking the `Expand`\nbutton at the top-right of this lineage pane, you'll be able to see all of the models that are used to build,\nor are built from, the model you're exploring.\n\nOnce expanded, you'll be able to use the `--select` and `--exclude` model selection syntax to filter the\nmodels in the graph. For more information on model selection, check out the [dbt docs](https://docs.getdbt.com/docs/model-selection-syntax).\n\nNote that you can also right-click on models to interactively filter and explore the graph.\n\n---\n\n### More information\n\n- [What is dbt](https://docs.getdbt.com/docs/introduction)?\n- Read the [dbt viewpoint](https://docs.getdbt.com/docs/viewpoint)\n- [Installation](https://docs.getdbt.com/docs/installation)\n- Join the [dbt Community](https://www.getdbt.com/community/) for questions and discussion"}}, "exposures": {}, "metrics": {}, "groups": {}, "selectors": {}, "disabled": {}, "parent_map": {"model.smi_report.mart_verticals_to_charts": ["model.smi_report.core_pages_to_charts", "model.smi_report.core_vertical_graphs_verticals"], "model.smi_report.stg_chapters": [], "model.smi_report.stg_charts": [], "model.smi_report.stg_geos": [], "model.smi_report.stg_chart_types": [], "model.smi_report.stg_chart_kpis": [], "model.smi_report.stg_kpis": [], "model.smi_report.stg_units": [], "model.smi_report.stg_pages": [], "model.smi_report.stg_verticals": [], "model.smi_report.stg_kpis_values": [], "model.smi_report.stg_vertical_graphs": [], "model.smi_report.core_kpis_to_values": ["model.smi_report.stg_kpis", "model.smi_report.stg_kpis_values"], "model.smi_report.core_pages_to_agg_values": ["model.smi_report.core_array_agg_values", "model.smi_report.core_pages_to_charts", "model.smi_report.stg_chart_kpis"], "model.smi_report.core_pages_to_charts": ["model.smi_report.stg_chapters", "model.smi_report.stg_chart_types", "model.smi_report.stg_charts", "model.smi_report.stg_geos", "model.smi_report.stg_pages", "model.smi_report.stg_units"], "model.smi_report.core_vertical_graphs_verticals": ["model.smi_report.stg_vertical_graphs", "model.smi_report.stg_verticals"], "test.smi_report.not_null_core_kpis_to_values_kpi_id.8ab724756d": ["model.smi_report.core_kpis_to_values"], "test.smi_report.not_null_core_kpis_to_values_value.ee02c5f8c6": ["model.smi_report.core_kpis_to_values"], "model.smi_report.core_array_agg_values": ["model.smi_report.core_kpis_to_values"]}, "child_map": {"model.smi_report.mart_verticals_to_charts": [], "model.smi_report.stg_chapters": ["model.smi_report.core_pages_to_charts"], "model.smi_report.stg_charts": ["model.smi_report.core_pages_to_charts"], "model.smi_report.stg_geos": ["model.smi_report.core_pages_to_charts"], "model.smi_report.stg_chart_types": ["model.smi_report.core_pages_to_charts"], "model.smi_report.stg_chart_kpis": ["model.smi_report.core_pages_to_agg_values"], "model.smi_report.stg_kpis": ["model.smi_report.core_kpis_to_values"], "model.smi_report.stg_units": ["model.smi_report.core_pages_to_charts"], "model.smi_report.stg_pages": ["model.smi_report.core_pages_to_charts"], "model.smi_report.stg_verticals": ["model.smi_report.core_vertical_graphs_verticals"], "model.smi_report.stg_kpis_values": ["model.smi_report.core_kpis_to_values"], "model.smi_report.stg_vertical_graphs": ["model.smi_report.core_vertical_graphs_verticals"], "model.smi_report.core_kpis_to_values": ["model.smi_report.core_array_agg_values", "test.smi_report.not_null_core_kpis_to_values_kpi_id.8ab724756d", "test.smi_report.not_null_core_kpis_to_values_value.ee02c5f8c6"], "model.smi_report.core_pages_to_agg_values": [], "model.smi_report.core_pages_to_charts": ["model.smi_report.core_pages_to_agg_values", "model.smi_report.mart_verticals_to_charts"], "model.smi_report.core_vertical_graphs_verticals": ["model.smi_report.mart_verticals_to_charts"], "test.smi_report.not_null_core_kpis_to_values_kpi_id.8ab724756d": [], "test.smi_report.not_null_core_kpis_to_values_value.ee02c5f8c6": [], "model.smi_report.core_array_agg_values": ["model.smi_report.core_pages_to_agg_values"]}, "group_map": {}, "saved_queries": {}, "semantic_models": {}, "unit_tests": {}}