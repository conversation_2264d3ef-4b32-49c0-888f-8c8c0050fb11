{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.10.4", "generated_at": "2025-07-19T19:58:43.716068Z", "invocation_id": "f10ea801-a0ef-420d-bd7a-507cc946e552", "invocation_started_at": "2025-07-19T19:57:33.266842Z", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2025-07-19T19:57:34.575811Z", "completed_at": "2025-07-19T19:57:34.586063Z"}, {"name": "execute", "started_at": "2025-07-19T19:57:34.586513Z", "completed_at": "2025-07-19T19:58:43.676180Z"}], "thread_id": "Thread-1", "execution_time": 69.10220527648926, "adapter_response": {"_message": "SELECT 478350", "code": "SELECT", "rows_affected": 478350}, "message": "SELECT 478350", "failures": null, "unique_id": "model.smi_report.mart_mongodb", "compiled": true, "compiled_code": "-- models/marts/mart_mongodb.sql\n\n\n\n-- Final mart for MongoDB export\n-- Combines chart data with aggregated values, filtered by geographic and chart priorities\n-- This model provides the complete dataset for PowerPoint report generation\n\nwith filtered_data as (\n    SELECT\n        page_id,\n        page_name,\n        geo_name,\n        outlook_name,\n        chapter_name,\n        chart_id,\n        SPLIT_PART(chart_key, '_', 1) as chart_key,\n        chart_type,\n        order_chart,\n        min_year,\n        max_year,\n        unit,\n        unit_type,\n        scale,\n        number_decimal,\n        chart_title,\n        kpi_id,\n        name as kpi_name,\n        value_time_list,\n        value_list\n    FROM \"postgres\".\"public\".\"core_pages_to_agg_values\"\n    WHERE geo_name IN ('Worldwide','Asia','Europe','South America','North America','Africa','Australia & Oceania','Australia','Canada','United States','Saudi Arabia','Brazil','China','India','Japan','Singapore','South Korea','France','Germany','Ireland','Italy','Spain','Sweden','Switzerland','United Kingdom','New Zealand','Russia','Mainland China')\n)\n\nSELECT * FROM filtered_data", "relation_name": "\"postgres\".\"public\".\"mart_mongodb\"", "batch_results": null}], "elapsed_time": 69.47633099555969, "args": {"log_format": "default", "validate_macro_args": false, "which": "run", "log_level_file": "info", "show_resource_report": false, "cache_selected_only": false, "use_fast_test_edges": false, "require_explicit_package_overrides_for_builtin_materializations": true, "partial_parse_file_diff": true, "use_colors_file": true, "log_format_file": "debug", "indirect_selection": "eager", "invocation_command": "dbt run --select mart_mongodb --profiles-dir . --log-level info", "populate_cache": true, "require_nested_cumulative_type_params": false, "warn_error_options": {"error": [], "warn": [], "silence": []}, "use_colors": true, "project_dir": "/home/<USER>/market-insights-reports/dbt", "require_batched_execution_for_custom_microbatch_strategy": false, "introspect": true, "select": ["mart_mongodb"], "state_modified_compare_more_unrendered_values": false, "quiet": false, "skip_nodes_if_on_run_start_fails": false, "printer_width": 80, "defer": false, "strict_mode": false, "favor_state": false, "profiles_dir": ".", "empty": false, "macro_debugging": false, "version_check": true, "send_anonymous_usage_stats": true, "log_path": "/home/<USER>/market-insights-reports/dbt/logs", "require_all_warnings_handled_by_warn_error": false, "write_json": true, "log_file_max_bytes": 10485760, "require_resource_names_without_spaces": true, "require_yaml_configuration_for_mf_time_spines": false, "state_modified_compare_vars": false, "exclude": [], "show_all_deprecations": false, "log_level": "info", "static_parser": true, "print": true, "upload_to_artifacts_ingest_api": false, "source_freshness_run_project_hooks": true, "partial_parse": true, "vars": {}}}