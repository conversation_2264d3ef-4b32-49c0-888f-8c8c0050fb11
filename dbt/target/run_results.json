{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.10.4", "generated_at": "2025-07-19T17:53:04.015314Z", "invocation_id": "5b66de22-42c1-4bf9-b604-72164be35cbd", "invocation_started_at": "2025-07-19T15:41:20.187398Z", "env": {}}, "results": [{"status": "error", "timing": [{"name": "compile", "started_at": "2025-07-19T15:41:21.437729Z", "completed_at": "2025-07-19T15:41:21.448509Z"}, {"name": "execute", "started_at": "2025-07-19T15:41:21.448936Z", "completed_at": "2025-07-19T17:53:03.961066Z"}], "thread_id": "Thread-1", "execution_time": 7902.526763677597, "adapter_response": {}, "message": "Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)\n  could not receive data from server: Connection timed out\n  SSL SYSCALL error: Connection timed out", "failures": null, "unique_id": "model.smi_report.core_array_agg_values", "compiled": true, "compiled_code": "-- models/core/core_array_agg_values.sql\n\n-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table\n\n\n\nwith kv as (\n    select * from \"postgres\".\"public\".\"core_kpis_to_values\"\n),\n\n-- Filter and prepare data for aggregation\nkv_filtered as (\n    select\n        kv.kpi_id,\n        kv.name,\n        kv.kpi_key,\n        kv.geo_id,\n        kv.unit_id,\n        kv.value_time,\n        kv.value,\n        kv.time_updated\n    from kv\n    where kv.value is not null\n),\n\naav as (\n    SELECT\n        kv_filtered.kpi_id,\n        kv_filtered.name,\n        kv_filtered.kpi_key,\n        kv_filtered.geo_id,\n        kv_filtered.unit_id,\n        array_agg(kv_filtered.value_time ORDER BY kv_filtered.value_time) AS value_time_list,\n        array_agg(kv_filtered.value ORDER BY kv_filtered.value_time) AS value_list,\n        max(kv_filtered.time_updated) as time_updated\n    FROM\n        kv_filtered\n    GROUP BY\n        kv_filtered.kpi_id, kv_filtered.name, kv_filtered.kpi_key, kv_filtered.geo_id, kv_filtered.unit_id\n    HAVING\n        array_length(array_agg(kv_filtered.value) FILTER (WHERE kv_filtered.value IS NOT NULL), 1) > 0\n)\n\nSELECT * FROM aav", "relation_name": "\"postgres\".\"public\".\"core_array_agg_values\"", "batch_results": null}], "elapsed_time": 7902.750782489777, "args": {"log_file_max_bytes": 10485760, "favor_state": false, "defer": false, "strict_mode": false, "require_batched_execution_for_custom_microbatch_strategy": false, "select": ["core_array_agg_values"], "indirect_selection": "eager", "require_resource_names_without_spaces": true, "write_json": true, "project_dir": "/home/<USER>/market-insights-reports/dbt", "which": "run", "validate_macro_args": false, "invocation_command": "dbt run --select core_array_agg_values --profiles-dir . --log-level info", "vars": {}, "warn_error_options": {"error": [], "warn": [], "silence": []}, "introspect": true, "require_explicit_package_overrides_for_builtin_materializations": true, "show_resource_report": false, "partial_parse_file_diff": true, "require_all_warnings_handled_by_warn_error": false, "partial_parse": true, "log_level": "info", "require_nested_cumulative_type_params": false, "print": true, "log_format_file": "debug", "log_level_file": "info", "macro_debugging": false, "state_modified_compare_more_unrendered_values": false, "use_colors": true, "version_check": true, "printer_width": 80, "use_fast_test_edges": false, "use_colors_file": true, "show_all_deprecations": false, "log_path": "/home/<USER>/market-insights-reports/dbt/logs", "log_format": "default", "upload_to_artifacts_ingest_api": false, "state_modified_compare_vars": false, "empty": false, "exclude": [], "populate_cache": true, "skip_nodes_if_on_run_start_fails": false, "static_parser": true, "send_anonymous_usage_stats": true, "cache_selected_only": false, "source_freshness_run_project_hooks": true, "require_yaml_configuration_for_mf_time_spines": false, "profiles_dir": ".", "quiet": false}}