{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.10.4", "generated_at": "2025-07-19T15:39:47.108868Z", "invocation_id": "23955220-5d20-48b1-a43b-1cfb8f4a562e", "invocation_started_at": "2025-07-19T15:39:46.073271Z", "env": {}}, "results": [{"status": "error", "timing": [{"name": "compile", "started_at": "2025-07-19T15:39:46.963780Z", "completed_at": "2025-07-19T15:39:46.979448Z"}, {"name": "execute", "started_at": "2025-07-19T15:39:46.979891Z", "completed_at": "2025-07-19T15:39:47.077547Z"}], "thread_id": "Thread-1", "execution_time": 0.11716508865356445, "adapter_response": {}, "message": "Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)\n  aggregate functions are not allowed in WHERE\n  LINE 23:         where time_updated > (select max(time_updated) from ...\n                                                ^", "failures": null, "unique_id": "model.smi_report.core_array_agg_values", "compiled": true, "compiled_code": "-- models/core/core_array_agg_values.sql\n\n-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table\n\n\n\nwith kv as (\n    select * from \"postgres\".\"public\".\"core_kpis_to_values\"\n    \n        -- Only process new/updated records in incremental runs\n        where time_updated > (select max(time_updated) from \"postgres\".\"public\".\"core_array_agg_values\")\n    \n),\n\n-- Filter and prepare data for aggregation\nkv_filtered as (\n    select\n        kv.kpi_id,\n        kv.name,\n        kv.kpi_key,\n        kv.geo_id,\n        kv.unit_id,\n        kv.value_time,\n        kv.value,\n        kv.time_updated\n    from kv\n    where kv.value is not null\n),\n\naav as (\n    SELECT\n        kv_filtered.kpi_id,\n        kv_filtered.name,\n        kv_filtered.kpi_key,\n        kv_filtered.geo_id,\n        kv_filtered.unit_id,\n        array_agg(kv_filtered.value_time ORDER BY kv_filtered.value_time) AS value_time_list,\n        array_agg(kv_filtered.value ORDER BY kv_filtered.value_time) AS value_list,\n        max(kv_filtered.time_updated) as time_updated\n    FROM\n        kv_filtered\n    GROUP BY\n        kv_filtered.kpi_id, kv_filtered.name, kv_filtered.kpi_key, kv_filtered.geo_id, kv_filtered.unit_id\n    HAVING\n        array_length(array_agg(kv_filtered.value) FILTER (WHERE kv_filtered.value IS NOT NULL), 1) > 0\n)\n\nSELECT * FROM aav", "relation_name": "\"postgres\".\"public\".\"core_array_agg_values\"", "batch_results": null}], "elapsed_time": 0.3438379764556885, "args": {"log_format_file": "debug", "require_all_warnings_handled_by_warn_error": false, "which": "run", "validate_macro_args": false, "show_resource_report": false, "source_freshness_run_project_hooks": true, "require_nested_cumulative_type_params": false, "indirect_selection": "eager", "cache_selected_only": false, "state_modified_compare_vars": false, "empty": false, "exclude": [], "invocation_command": "dbt run --select core_array_agg_values --profiles-dir . --log-level info", "upload_to_artifacts_ingest_api": false, "partial_parse": true, "macro_debugging": false, "skip_nodes_if_on_run_start_fails": false, "partial_parse_file_diff": true, "send_anonymous_usage_stats": true, "require_batched_execution_for_custom_microbatch_strategy": false, "strict_mode": false, "use_colors": true, "printer_width": 80, "static_parser": true, "project_dir": "/home/<USER>/market-insights-reports/dbt", "state_modified_compare_more_unrendered_values": false, "quiet": false, "log_level_file": "info", "require_explicit_package_overrides_for_builtin_materializations": true, "show_all_deprecations": false, "log_path": "/home/<USER>/market-insights-reports/dbt/logs", "write_json": true, "use_fast_test_edges": false, "use_colors_file": true, "log_file_max_bytes": 10485760, "populate_cache": true, "profiles_dir": ".", "log_level": "info", "require_resource_names_without_spaces": true, "select": ["core_array_agg_values"], "defer": false, "version_check": true, "log_format": "default", "warn_error_options": {"error": [], "warn": [], "silence": []}, "favor_state": false, "vars": {}, "require_yaml_configuration_for_mf_time_spines": false, "introspect": true, "print": true}}