{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.10.4", "generated_at": "2025-07-21T11:57:03.922116Z", "invocation_id": "91f2bc2f-b7cc-4064-acb4-ace435b43189", "invocation_started_at": "2025-07-21T11:57:02.405792Z", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2025-07-21T11:57:03.709022Z", "completed_at": "2025-07-21T11:57:03.728839Z"}, {"name": "execute", "started_at": "2025-07-21T11:57:03.729475Z", "completed_at": "2025-07-21T11:57:03.890884Z"}], "thread_id": "Thread-1", "execution_time": 0.18406891822814941, "adapter_response": {"_message": "CREATE VIEW", "code": "CREATE VIEW", "rows_affected": -1}, "message": "CREATE VIEW", "failures": null, "unique_id": "model.smi_report.core_pages_to_charts", "compiled": true, "compiled_code": "-- models/core/core_pages_to_charts.sql\n\nwith pages as (\n    select * from \"postgres\".\"public\".\"stg_pages\"\n),\n\nchapters as (\n    select * from \"postgres\".\"public\".\"stg_chapters\"\n),\n\ncharts as (\n    select * from \"postgres\".\"public\".\"stg_charts\"\n    WHERE SPLIT_PART(chart_key, '_', 1) IN ('revenue','arpu','usersPenetration','volume','pricePerUnit','spendPerEmployee','revenueShare','users','valueAdded','output','numberEmployees','deliveries','averageDealSize','energyProduction','energyProductionGrowth','growthFactor','transactionValue','revenuePerPharmacy','scenarioRevenue','premium','freightTransported','transportationEfficiency','production','onlineRevenueShare','exportValue','importValue','grossProductionValue','medications','revenueIndustryShare','realEstateVolume','productionVolume','revenueDesktopMobileShare','revenueTherapeuticArea','lease','interestIncome','deposits','loans','osShare','averageLenghtOfStay','averageTransactionSize','numberOfTransactions','atpu','aum','containerTransport','shareEmissions','transactionValueOutwardRemittances','transactionValueInwardRemittances','realEstateValue','mobileSubsriptions','advisors','revenueGrowth','revenuePerCapita','revenueInApp','downloads','revenueRetailPlatform','revenueRetailPlatformDesktopMobileShare','marketCapitalization','marketVolume','numberOfTrades','netWorth','claimPayments','atpuOutwardRemittances','atpuInwardRemittances','productionGrowth','productionVolumeGrowth','airlinesRevenue','airportVolume','cost','costGrowth','containerPortThroughput','revenueOther','revenueGrowthOther','revenueBrand','premiumGrowth','reserves','energyEmissionIntencity','tradeNetVolume')\n),\n\nchart_types as (\n    select * from \"postgres\".\"public\".\"stg_chart_types\"\n),\n\nunits as (\n    select * from \"postgres\".\"public\".\"stg_units\"\n),\n\ngeos as (\n    select * from \"postgres\".\"public\".\"stg_geos\"\n),\n\npages_to_charts as (\n    select\n        pages.id as page_id,\n        vertical_graph_id,\n        pages.name as page_name,\n        pages.geo_id,\n        geos.name as geo_name,\n        geos.iso as geo_iso,\n        outlook_name,\n        definition,\n        key_take_away,\n        in_scope,\n        out_scope,\n        chapters.id as chapter_id,\n        chapters.name as chapter_name,\n        order_chapter,\n        charts.id as chart_id,\n        chart_key,\n        chart_types.chart_type,\n        order_chart,\n        charts.min_year,\n        charts.max_year,\n        units.magnitude as unit,\n        units.unit_type,\n        scale,\n        number_decimal,\n        charts.main_title as chart_title,\n        charts.description,\n        charts.info,\n        charts.time_updated\n    from pages\n    left join geos on pages.geo_id = geos.id\n    left join chapters on pages.id = chapters.page_id\n    left join charts on chapters.id = charts.chapter_id\n    left join chart_types on charts.chart_type = chart_types.id\n    left join units on charts.unit_id = units.id\n    WHERE geos.name IN ('Worldwide','Asia','Europe','South America','North America','Africa','Australia & Oceania','Australia','Canada','United States','Saudi Arabia','Brazil','China','India','Japan','Singapore','South Korea','France','Germany','Ireland','Italy','Spain','Sweden','Switzerland','United Kingdom','New Zealand','Russia','Mainland China')\n)\n\nselect * from pages_to_charts", "relation_name": "\"postgres\".\"public\".\"core_pages_to_charts\"", "batch_results": null}], "elapsed_time": 0.4090292453765869, "args": {"require_batched_execution_for_custom_microbatch_strategy": false, "log_file_max_bytes": 10485760, "select": ["core_pages_to_charts"], "log_level_file": "info", "send_anonymous_usage_stats": true, "strict_mode": false, "validate_macro_args": false, "use_colors_file": true, "macro_debugging": false, "log_level": "info", "exclude": [], "require_yaml_configuration_for_mf_time_spines": false, "quiet": false, "skip_nodes_if_on_run_start_fails": false, "profiles_dir": ".", "use_fast_test_edges": false, "state_modified_compare_vars": false, "cache_selected_only": false, "source_freshness_run_project_hooks": true, "write_json": true, "use_colors": true, "upload_to_artifacts_ingest_api": false, "defer": false, "favor_state": false, "project_dir": "/home/<USER>/market-insights-reports/dbt", "log_path": "/home/<USER>/market-insights-reports/dbt/logs", "partial_parse": true, "show_all_deprecations": false, "require_explicit_package_overrides_for_builtin_materializations": true, "print": true, "state_modified_compare_more_unrendered_values": false, "indirect_selection": "eager", "partial_parse_file_diff": true, "require_nested_cumulative_type_params": false, "warn_error_options": {"error": [], "warn": [], "silence": []}, "vars": {}, "static_parser": true, "require_all_warnings_handled_by_warn_error": false, "version_check": true, "populate_cache": true, "require_resource_names_without_spaces": true, "invocation_command": "dbt run --select core_pages_to_charts --profiles-dir . --log-level info", "show_resource_report": false, "log_format_file": "debug", "empty": false, "log_format": "default", "which": "run", "introspect": true, "printer_width": 80}}