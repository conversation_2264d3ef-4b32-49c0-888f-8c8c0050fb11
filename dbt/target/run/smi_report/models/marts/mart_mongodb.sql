
  
    

  create  table "postgres"."public"."mart_mongodb__dbt_tmp"
  
  
    as
  
  (
    -- models/marts/mart_mongodb.sql



-- Final mart for MongoDB export
-- Combines chart data with aggregated values, filtered by geographic and chart priorities
-- This model provides the complete dataset for PowerPoint report generation

with filtered_data as (
    SELECT
        page_id,
        page_name,
        geo_name,
        outlook_name,
        chapter_name,
        chart_id,
        SPLIT_PART(chart_key, '_', 1) as chart_key,
        chart_type,
        order_chart,
        min_year,
        max_year,
        unit,
        unit_type,
        scale,
        number_decimal,
        chart_title,
        kpi_id,
        name as kpi_name,
        value_time_list,
        value_list
    FROM "postgres"."public"."core_pages_to_agg_values"
    WHERE geo_name IN ('Worldwide','Asia','Europe','South America','North America','Africa','Australia & Oceania','Australia','Canada','United States','Saudi Arabia','Brazil','China','India','Japan','Singapore','South Korea','France','Germany','Ireland','Italy','Spain','Sweden','Switzerland','United Kingdom','New Zealand','Russia','Mainland China')
)

SELECT * FROM filtered_data
  );
  