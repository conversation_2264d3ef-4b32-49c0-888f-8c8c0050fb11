
      
        delete from "postgres"."public"."core_array_agg_values" as DBT_INTERNAL_DEST
        where (kpi_id, geo_id) in (
            select distinct kpi_id, geo_id
            from "core_array_agg_values__dbt_tmp192918411822" as DBT_INTERNAL_SOURCE
        );

    

    insert into "postgres"."public"."core_array_agg_values" ("kpi_id", "name", "kpi_key", "geo_id", "unit_id", "value_time_list", "value_list")
    (
        select "kpi_id", "name", "kpi_key", "geo_id", "unit_id", "value_time_list", "value_list"
        from "core_array_agg_values__dbt_tmp192918411822"
    )
  