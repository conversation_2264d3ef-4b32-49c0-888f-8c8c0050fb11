
  create view "postgres"."public"."stg_kpis__dbt_tmp"
    
    
  as (
    -- models/staging/stg_kpis.sql

with source as (

    select * from public.kpis

),

cleaned as (

    select
        id,
        "kpiKey" as kpi_key,
        "idBrand" as brand_id,
        "idVerticalGraph" as vertical_graph_id,
        name,
        "kpiType" as kpi_type,
        "isSummable" as is_summable,
        "isCurrency" as is_currency
    from source

)

select * from cleaned
  );