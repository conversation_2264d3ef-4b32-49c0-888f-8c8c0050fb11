
  create view "postgres"."public"."stg_pages__dbt_tmp"
    
    
  as (
    -- models/staging/stg_pages.sql

with source as (

    select * from public.pages

),

cleaned as (

    select
        id,
        "idVerticalGraph" as vertical_graph_id,
        "idPlatform" as platform_id,
        "idGeo" as geo_id,
        name,
        "outlookName" as outlook_name,
        definition,
        "keyTakeAway" as key_take_away,
        "inScope" as in_scope,
        "outOfScope" as out_scope,
        "searchTags" as search_tags,
        "placeholderScript" as placeholder_script,
        "metaDescription" as meta_description
    from source
    where "idPlatform" = 2
    and "isActive" = 1
)

select * from cleaned
  );