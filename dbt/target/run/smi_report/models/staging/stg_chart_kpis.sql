
  create view "postgres"."public"."stg_chart_kpis__dbt_tmp"
    
    
  as (
    -- models/staging/stg_chart_kpis.sql

with source as (

    select * from public."chartsKpis"

),

cleaned as (

    select
        id,
        "idChart" as chart_id,
        "idKpi" as kpi_id,
        "aggregationType" as aggregation_type,
        "orderChart" as order_chart
    from source

)

select * from cleaned
  );