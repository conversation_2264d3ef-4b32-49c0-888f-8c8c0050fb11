
  create view "postgres"."public"."stg_vertical_graphs__dbt_tmp"
    
    
  as (
    -- models/staging/stg_vertical_graphs.sql

with source as (

    select * from public."verticalGraphs"

),

cleaned as (

    select
        id,
        "idVertical" as vertical_id,
        "idVerticalParent" as vertical_parent_id,
        "marketName" as market_name,
        "typeVertical" as type_vertical
    from source
    where "typeVertical" in ('outlook', 'market')
)

select * from cleaned
  );