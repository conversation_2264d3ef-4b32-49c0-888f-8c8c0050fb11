-- models/core/core_pages_to_charts.sql

with pages as (
    select * from "postgres"."public"."stg_pages"
),

chapters as (
    select * from "postgres"."public"."stg_chapters"
),

charts as (
    select * from "postgres"."public"."stg_charts"
    WHERE SPLIT_PART(chart_key, '_', 1) IN ('revenue','arpu','usersPenetration','volume','pricePerUnit','spendPerEmployee','revenueShare','users','valueAdded','output','numberEmployees','deliveries','averageDealSize','energyProduction','energyProductionGrowth','growthFactor','transactionValue','revenuePerPharmacy','scenarioRevenue','premium','freightTransported','transportationEfficiency','production','onlineRevenueShare','exportValue','importValue','grossProductionValue','medications','revenueIndustryShare','realEstateVolume','productionVolume','revenueDesktopMobileShare','revenueTherapeuticArea','lease','interestIncome','deposits','loans','osShare','averageLenghtOfStay','averageTransactionSize','numberOfTransactions','atpu','aum','containerTransport','shareEmissions','transactionValueOutwardRemittances','transactionValueInwardRemittances','realEstateValue','mobileSubsriptions','advisors','revenueGrowth','revenuePerCapita','revenueInApp','downloads','revenueRetailPlatform','revenueRetailPlatformDesktopMobileShare','marketCapitalization','marketVolume','numberOfTrades','netWorth','claimPayments','atpuOutwardRemittances','atpuInwardRemittances','productionGrowth','productionVolumeGrowth','airlinesRevenue','airportVolume','cost','costGrowth','containerPortThroughput','revenueOther','revenueGrowthOther','revenueBrand','premiumGrowth','reserves','energyEmissionIntencity','tradeNetVolume')
),

chart_types as (
    select * from "postgres"."public"."stg_chart_types"
),

units as (
    select * from "postgres"."public"."stg_units"
),

geos as (
    select * from "postgres"."public"."stg_geos"
),

pages_to_charts as (
    select
        pages.id as page_id,
        vertical_graph_id,
        pages.name as page_name,
        pages.geo_id,
        geos.name as geo_name,
        geos.iso as geo_iso,
        outlook_name,
        definition,
        key_take_away,
        in_scope,
        out_scope,
        chapters.id as chapter_id,
        chapters.name as chapter_name,
        order_chapter,
        charts.id as chart_id,
        chart_key,
        chart_types.chart_type,
        order_chart,
        charts.min_year,
        charts.max_year,
        units.magnitude as unit,
        units.unit_type,
        scale,
        number_decimal,
        charts.main_title as chart_title,
        charts.description,
        charts.info,
        charts.time_updated
    from pages
    left join geos on pages.geo_id = geos.id
    left join chapters on pages.id = chapters.page_id
    left join charts on chapters.id = charts.chapter_id
    left join chart_types on charts.chart_type = chart_types.id
    left join units on charts.unit_id = units.id
)

select * from pages_to_charts