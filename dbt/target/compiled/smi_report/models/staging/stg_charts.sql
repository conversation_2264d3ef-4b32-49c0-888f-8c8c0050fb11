-- models/staging/stg_charts.sql

with source as (

    select * from public.charts

),

cleaned as (

    select
        id,
        "idParent" as parent_id,
        "idChapter" as chapter_id,
        "chartType" as chart_type,
        "chartKey" as chart_key,
        "orderChart" as order_chart,
        "minYear" as min_year,
        "maxYear" as max_year,
        "idUnit" as unit_id,
        scale,
        "numberDecimal" as number_decimal,
        "mainTitle" as main_title,
        description,
        info,
        "timeUpdated" as time_updated
    from source

)

select * from cleaned