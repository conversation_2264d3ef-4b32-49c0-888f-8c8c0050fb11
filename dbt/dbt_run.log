Sat Jul 19 19:57:31 UTC 2025: Starting selective dbt run with args: --select mart_mongodb
[0m19:57:33  Running with dbt=1.10.4
[0m19:57:33  Registered adapter: postgres=1.9.0
[0m19:57:34  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m19:57:34  Found 18 models, 2 data tests, 436 macros
[0m19:57:34  
[0m19:57:34  Concurrency: 1 threads (target='dev')
[0m19:57:34  
[0m19:57:34  1 of 1 START sql table model public.mart_mongodb ............................... [RUN]
[0m19:58:43  1 of 1 OK created sql table model public.mart_mongodb .......................... [[32mSELECT 478350[0m in 69.10s]
[0m19:58:43  
[0m19:58:43  Finished running 1 table model in 0 hours 1 minutes and 9.48 seconds (69.48s).
[0m19:58:43  
[0m19:58:43  [32mCompleted successfully[0m
[0m19:58:43  
[0m19:58:43  Done. PASS=1 WARN=0 ERROR=0 SKIP=0 NO-OP=0 TOTAL=1
Sat Jul 19 19:58:44 UTC 2025: dbt run completed successfully.
