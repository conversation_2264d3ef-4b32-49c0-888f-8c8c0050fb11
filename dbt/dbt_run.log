Sat Jul 19 19:19:21 UTC 2025: Starting dbt run for ALL models...
Sat Jul 19 19:19:21 UTC 2025: Starting dbt run attempt 1...
[0m19:19:22  Running with dbt=1.10.4
[0m19:19:23  Registered adapter: postgres=1.9.0
[0m19:19:23  Unable to do partial parsing because a project config has changed
[0m19:19:24  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m19:19:24  Found 18 models, 2 data tests, 436 macros
[0m19:19:24  
[0m19:19:24  Concurrency: 1 threads (target='dev')
[0m19:19:24  
[0m19:19:25  1 of 18 START sql view model public.stg_chapters ............................... [RUN]
[0m19:19:25  1 of 18 OK created sql view model public.stg_chapters .......................... [[32mCREATE VIEW[0m in 0.15s]
[0m19:19:25  2 of 18 START sql view model public.stg_chart_kpis ............................. [RUN]
[0m19:19:25  2 of 18 OK created sql view model public.stg_chart_kpis ........................ [[32mCREATE VIEW[0m in 0.06s]
[0m19:19:25  3 of 18 START sql view model public.stg_chart_types ............................ [RUN]
[0m19:19:25  3 of 18 OK created sql view model public.stg_chart_types ....................... [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  4 of 18 START sql view model public.stg_charts ................................. [RUN]
[0m19:19:25  4 of 18 OK created sql view model public.stg_charts ............................ [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  5 of 18 START sql view model public.stg_geos ................................... [RUN]
[0m19:19:25  5 of 18 OK created sql view model public.stg_geos .............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  6 of 18 START sql view model public.stg_kpis ................................... [RUN]
[0m19:19:25  6 of 18 OK created sql view model public.stg_kpis .............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  7 of 18 START sql view model public.stg_kpis_values ............................ [RUN]
[0m19:19:25  7 of 18 OK created sql view model public.stg_kpis_values ....................... [[32mCREATE VIEW[0m in 0.06s]
[0m19:19:25  8 of 18 START sql view model public.stg_pages .................................. [RUN]
[0m19:19:25  8 of 18 OK created sql view model public.stg_pages ............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  9 of 18 START sql view model public.stg_units .................................. [RUN]
[0m19:19:25  9 of 18 OK created sql view model public.stg_units ............................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  10 of 18 START sql view model public.stg_vertical_graphs ....................... [RUN]
[0m19:19:25  10 of 18 OK created sql view model public.stg_vertical_graphs .................. [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  11 of 18 START sql view model public.stg_verticals ............................. [RUN]
[0m19:19:25  11 of 18 OK created sql view model public.stg_verticals ........................ [[32mCREATE VIEW[0m in 0.05s]
[0m19:19:25  12 of 18 START sql incremental model public.core_kpis_to_values ................ [RUN]
[0m19:29:18  12 of 18 OK created sql incremental model public.core_kpis_to_values ........... [[32mINSERT 0 59445068[0m in 592.35s]
[0m19:29:18  13 of 18 START sql view model public.core_pages_to_charts ...................... [RUN]
[0m19:29:18  13 of 18 OK created sql view model public.core_pages_to_charts ................. [[32mCREATE VIEW[0m in 0.13s]
[0m19:29:18  14 of 18 START sql view model public.core_vertical_graphs_verticals ............ [RUN]
[0m19:29:18  14 of 18 OK created sql view model public.core_vertical_graphs_verticals ....... [[32mCREATE VIEW[0m in 0.05s]
[0m19:29:18  15 of 18 START sql incremental model public.core_array_agg_values .............. [RUN]
[0m19:39:03  15 of 18 OK created sql incremental model public.core_array_agg_values ......... [[32mINSERT 0 4854651[0m in 584.82s]
[0m19:39:03  16 of 18 START sql table model public.mart_verticals_to_charts ................. [RUN]
[0m19:39:53  16 of 18 OK created sql table model public.mart_verticals_to_charts ............ [[32mSELECT 1528978[0m in 50.56s]
[0m19:39:53  17 of 18 START sql incremental model public.core_pages_to_agg_values ........... [RUN]
