Sat Jul 19 19:57:31 UTC 2025: Starting selective dbt run with args: --select mart_mongodb
[0m19:57:33  Running with dbt=1.10.4
[0m19:57:33  Registered adapter: postgres=1.9.0
[0m19:57:34  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m19:57:34  Found 18 models, 2 data tests, 436 macros
[0m19:57:34  
[0m19:57:34  Concurrency: 1 threads (target='dev')
[0m19:57:34  
[0m19:57:34  1 of 1 START sql table model public.mart_mongodb ............................... [RUN]
