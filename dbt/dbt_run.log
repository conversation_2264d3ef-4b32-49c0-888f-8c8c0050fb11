nohup: ignoring input
[0m09:45:11  Running with dbt=1.10.4
[0m09:45:11  Registered adapter: postgres=1.9.0
[0m09:45:11  Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m09:45:12  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m09:45:12  Found 17 models, 2 data tests, 434 macros
[0m09:45:12  
[0m09:45:12  Concurrency: 4 threads (target='dev')
[0m09:45:12  
[0m09:45:13  1 of 17 START sql view model public.stg_chapters ............................... [RUN]
[0m09:45:13  2 of 17 START sql view model public.stg_chart_kpis ............................. [RUN]
[0m09:45:13  3 of 17 START sql view model public.stg_chart_types ............................ [RUN]
[0m09:45:13  4 of 17 START sql view model public.stg_charts ................................. [RUN]
[0m09:45:13  2 of 17 OK created sql view model public.stg_chart_kpis ........................ [[32mCREATE VIEW[0m in 0.27s]
[0m09:45:13  1 of 17 OK created sql view model public.stg_chapters .......................... [[32mCREATE VIEW[0m in 0.28s]
[0m09:45:13  4 of 17 OK created sql view model public.stg_charts ............................ [[32mCREATE VIEW[0m in 0.28s]
[0m09:45:13  3 of 17 OK created sql view model public.stg_chart_types ....................... [[32mCREATE VIEW[0m in 0.28s]
[0m09:45:13  5 of 17 START sql view model public.stg_geos ................................... [RUN]
[0m09:45:13  6 of 17 START sql view model public.stg_kpis ................................... [RUN]
[0m09:45:13  7 of 17 START sql view model public.stg_kpis_values ............................ [RUN]
[0m09:45:13  8 of 17 START sql view model public.stg_pages .................................. [RUN]
[0m09:45:13  5 of 17 OK created sql view model public.stg_geos .............................. [[32mCREATE VIEW[0m in 0.16s]
[0m09:45:13  6 of 17 OK created sql view model public.stg_kpis .............................. [[32mCREATE VIEW[0m in 0.16s]
[0m09:45:13  9 of 17 START sql view model public.stg_units .................................. [RUN]
[0m09:45:13  7 of 17 OK created sql view model public.stg_kpis_values ....................... [[32mCREATE VIEW[0m in 0.17s]
[0m09:45:13  8 of 17 OK created sql view model public.stg_pages ............................. [[32mCREATE VIEW[0m in 0.18s]
[0m09:45:13  10 of 17 START sql view model public.stg_vertical_graphs ....................... [RUN]
[0m09:45:13  11 of 17 START sql view model public.stg_verticals ............................. [RUN]
[0m09:45:13  12 of 17 START sql incremental model public.core_kpis_to_values ................ [RUN]
[0m09:45:13  9 of 17 OK created sql view model public.stg_units ............................. [[32mCREATE VIEW[0m in 0.33s]
[0m09:45:13  13 of 17 START sql view model public.core_pages_to_charts ...................... [RUN]
[0m09:45:13  10 of 17 OK created sql view model public.stg_vertical_graphs .................. [[32mCREATE VIEW[0m in 0.34s]
[0m09:45:13  11 of 17 OK created sql view model public.stg_verticals ........................ [[32mCREATE VIEW[0m in 0.33s]
[0m09:45:13  14 of 17 START sql view model public.core_vertical_graphs_verticals ............ [RUN]
[0m09:45:13  13 of 17 OK created sql view model public.core_pages_to_charts ................. [[32mCREATE VIEW[0m in 0.10s]
[0m09:45:14  14 of 17 OK created sql view model public.core_vertical_graphs_verticals ....... [[32mCREATE VIEW[0m in 0.16s]
[0m09:45:14  15 of 17 START sql table model public.mart_verticals_to_charts ................. [RUN]
[0m09:46:48  15 of 17 OK created sql table model public.mart_verticals_to_charts ............ [[32mSELECT 2095693[0m in 94.12s]
[0m10:01:05  12 of 17 OK created sql incremental model public.core_kpis_to_values ........... [[32mINSERT 0 59445068[0m in 951.91s]
[0m10:01:05  16 of 17 START sql incremental model public.core_array_agg_values .............. [RUN]
[0m12:02:56  16 of 17 ERROR creating sql incremental model public.core_array_agg_values ..... [[31mERROR[0m in 7311.18s]
[0m12:02:56  17 of 17 SKIP relation public.core_pages_to_agg_values ......................... [[33mSKIP[0m]
[0m12:02:56  
[0m12:02:56  Finished running 3 incremental models, 1 table model, 13 view models in 2 hours 17 minutes and 43.87 seconds (8263.87s).
[0m12:02:56  
[0m12:02:56  [31mCompleted with 1 error, 0 partial successes, and 0 warnings:[0m
[0m12:02:56  
[0m12:02:56  [31mFailure in model core_array_agg_values (models/core/core_array_agg_values.sql)[0m
[0m12:02:56    Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request.
  server closed the connection unexpectedly
  	This probably means the server terminated abnormally
  	before or while processing the request.
[0m12:02:56  
[0m12:02:56    compiled code at target/compiled/smi_report/models/core/core_array_agg_values.sql
[0m12:02:56  
[0m12:02:56  Done. PASS=15 WARN=0 ERROR=1 SKIP=1 NO-OP=0 TOTAL=17
