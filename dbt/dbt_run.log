Mon Jul 21 11:57:00 UTC 2025: Starting selective dbt run with args: --select core_pages_to_charts
[0m11:57:02  Running with dbt=1.10.4
[0m11:57:02  Registered adapter: postgres=1.9.0
[0m11:57:03  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m11:57:03  Found 18 models, 2 data tests, 436 macros
[0m11:57:03  
[0m11:57:03  Concurrency: 1 threads (target='dev')
[0m11:57:03  
[0m11:57:03  1 of 1 START sql view model public.core_pages_to_charts ........................ [RUN]
[0m11:57:03  1 of 1 OK created sql view model public.core_pages_to_charts ................... [[32mCREATE VIEW[0m in 0.18s]
[0m11:57:03  
[0m11:57:03  Finished running 1 view model in 0 hours 0 minutes and 0.41 seconds (0.41s).
[0m11:57:03  
[0m11:57:03  [32mCompleted successfully[0m
[0m11:57:03  
[0m11:57:03  Done. PASS=1 WARN=0 ERROR=0 SKIP=0 NO-OP=0 TOTAL=1
Mon Jul 21 11:57:04 UTC 2025: dbt run completed successfully.
