smi_report:
  outputs:
    dev:
      type: postgres
      host: smi-report-db.cisims9lnzeb.eu-central-1.rds.amazonaws.com
      user: "{{ env_var('RDS_USERNAME') }}"
      password: "{{ env_var('RDS_PASSWORD') }}"
      port: 5432
      dbname: "{{ env_var('RDS_DATABASE') }}"
      schema: public
      threads: 1
      keepalives_idle: 7200
      keepalives_interval: 30
      keepalives_count: 9
      connect_timeout: 60
      retries: 5
      search_path: "public"
      sslmode: prefer
    
    prod:
      type: postgres
      host: smi-report-db.cisims9lnzeb.eu-central-1.rds.amazonaws.com
      user: "{{ env_var('RDS_USERNAME') }}"
      password: "{{ env_var('RDS_PASSWORD') }}"
      port: 5432
      dbname: "{{ env_var('RDS_DATABASE') }}"
      schema: public
      threads: 2
      keepalives_idle: 7200
      keepalives_interval: 30
      keepalives_count: 9
      connect_timeout: 60
      retries: 5
      search_path: "public"
      sslmode: prefer

  target: dev 