{#
  Macro: get_geo_priorities()
  
  Purpose: Returns a comma-separated list of prioritized geographic regions for filtering in SQL models.
  
  Usage: WHERE geo_name IN ({{ get_geo_priorities() }})
  
  Returns: String of quoted geo names, e.g., 'Worldwide','Asia','Europe',...
  
  Synchronization: Keep in sync with any Python geo priority lists if created
  
  Models using this:
  - mart_mongodb.sql
  - Any model filtering by geographic priority
#}

{% macro get_geo_priorities() %}
  {%- set geo_priorities = [
    'Worldwide',
    'Asia',
    'Europe', 
    'South America',
    'North America',
    'Africa',
    'Australia & Oceania',
    'Australia',
    'Canada',
    'United States',
    'Saudi Arabia',
    'Brazil',
    'China',
    'India',
    'Japan',
    'Singapore',
    'South Korea',
    'France',
    'Germany',
    'Ireland',
    'Italy',
    'Spain',
    'Sweden',
    'Switzerland',
    'United Kingdom',
    'New Zealand',
    'Russia',
    'Mainland China'
  ] -%}
  
  {%- for geo in geo_priorities -%}
    '{{ geo }}'
    {%- if not loop.last -%},{%- endif -%}
  {%- endfor -%}
{% endmacro %}
