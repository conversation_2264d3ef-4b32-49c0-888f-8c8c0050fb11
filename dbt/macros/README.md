# dbt Macros Documentation

## Priority Management System

The project uses centralized priority macros to ensure consistent filtering across all models.

## Chart Priority Management

### Overview
The chart priority system ensures consistent chart filtering across all dbt models and downstream PowerPoint automation.

### get_chart_priorities() Macro

**File**: `get_chart_priorities.sql`

**Purpose**: Provides a centralized list of prioritized chart types for filtering in SQL models.

**Usage**:
```sql
-- Filter charts by priority in any model
WHERE SPLIT_PART(chart_key, '_', 1) IN ({{ get_chart_priorities() }})
```

**Models using this macro**:
- `core_pages_to_charts.sql` - Filters charts by priority before processing
- Any model that needs to filter charts by priority

### Chart Priority List

The macro contains 75 prioritized chart types in order of business importance:

1. **Revenue metrics**: `revenue`, `revenueGrowth`, `revenueShare`, etc.
2. **User metrics**: `arpu`, `users`, `usersPenetration`, etc.
3. **Volume metrics**: `volume`, `productionVolume`, `marketVolume`, etc.
4. **Financial metrics**: `cost`, `premium`, `deposits`, `loans`, etc.
5. **Industry-specific**: `medications`, `energyProduction`, `containerTransport`, etc.

### Synchronization with Python

The chart priorities are synchronized with the Python PowerPoint automation:

**dbt macro**: `dbt/macros/get_chart_priorities.sql`
**Python list**: `powerpoint_settings.py` → `chart_priority`

Both lists must be kept in sync manually when adding/removing chart types.

### Benefits

1. **Performance**: Only processes prioritized charts, reducing query time
2. **Consistency**: Same chart filtering logic across all models
3. **Maintainability**: Single place to update chart priorities
4. **Business Logic**: Ensures most important charts are always processed first

### Adding New Chart Types

To add a new chart type:

1. Add to `get_chart_priorities.sql` macro
2. Add to `powerpoint_settings.py` chart_priority list
3. Ensure both lists remain in the same order
4. Test with `dbt run --select model_name`

### Example Implementation

```sql
-- In core_pages_to_charts.sql
charts as (
    select * from {{ ref('stg_charts') }}
    WHERE SPLIT_PART(chart_key, '_', 1) IN ({{ get_chart_priorities() }})
),
```

This filters the charts table to only include charts whose first part of the chart_key (before the first underscore) matches one of the prioritized chart types.

## Geographic Priority Management

### get_geo_priorities() Macro

**File**: `get_geo_priorities.sql`

**Purpose**: Provides a centralized list of prioritized geographic regions for filtering in SQL models.

**Usage**:
```sql
-- Filter by priority geographies in any model
WHERE geo_name IN ({{ get_geo_priorities() }})
```

**Models using this macro**:
- **`core_pages_to_charts.sql`** - **PRIMARY USAGE** - Core model that applies geographic filtering to the entire pipeline
- `mart_mongodb.sql` - Final mart (inherits filtering from core model)
- Any model that needs to filter by geographic priority

### Geographic Priority List

The macro contains 28 prioritized geographic regions in order of business importance:

1. **Global**: `Worldwide`
2. **Continents**: `Asia`, `Europe`, `South America`, `North America`, `Africa`, `Australia & Oceania`
3. **Major Markets**: `United States`, `China`, `India`, `Japan`, `Germany`, `United Kingdom`, `France`
4. **Strategic Markets**: `Canada`, `Brazil`, `Australia`, `Singapore`, `South Korea`, `Saudi Arabia`
5. **European Markets**: `Ireland`, `Italy`, `Spain`, `Sweden`, `Switzerland`
6. **Other Important**: `New Zealand`, `Russia`, `Mainland China`

### Combined Usage

Both macros can be used together for comprehensive filtering:

```sql
-- Filter by both chart and geo priorities
SELECT *
FROM {{ ref('core_pages_to_agg_values') }}
WHERE geo_name IN ({{ get_geo_priorities() }})
  AND SPLIT_PART(chart_key, '_', 1) IN ({{ get_chart_priorities() }})
```
