{#
  Macro: get_chart_priorities()

  Purpose: Returns a comma-separated list of prioritized chart types for filtering in SQL models.

  Usage: WHERE SPLIT_PART(chart_key, '_', 1) IN ({{ get_chart_priorities() }})

  Returns: String of quoted chart type names, e.g., 'revenue','arpu','usersPenetration',...

  Synchronization: Keep in sync with powerpoint_settings.py chart_priority list

  Models using this:
  - core_pages_to_charts.sql
  - Any model filtering charts by business priority
#}

{% macro get_chart_priorities() %}
  {%- set chart_priorities = [
    'revenue',
    'arpu', 
    'usersPenetration',
    'volume',
    'pricePerUnit',
    'spendPerEmployee',
    'revenueShare',
    'users',
    'valueAdded',
    'output',
    'numberEmployees',
    'deliveries',
    'averageDealSize',
    'energyProduction',
    'energyProductionGrowth',
    'growthFactor',
    'transactionValue',
    'revenuePerPharmacy',
    'scenarioRevenue',
    'premium',
    'freightTransported',
    'transportationEfficiency',
    'production',
    'onlineRevenueShare',
    'exportValue',
    'importValue',
    'grossProductionValue',
    'medications',
    'revenueIndustryShare',
    'realEstateVolume',
    'productionVolume',
    'revenueDesktopMobileShare',
    'revenueTherapeuticArea',
    'lease',
    'interestIncome',
    'deposits',
    'loans',
    'osShare',
    'averageLenghtOfStay',
    'averageTransactionSize',
    'numberOfTransactions',
    'atpu',
    'aum',
    'containerTransport',
    'shareEmissions',
    'transactionValueOutwardRemittances',
    'transactionValueInwardRemittances',
    'realEstateValue',
    'mobileSubsriptions',
    'advisors',
    'revenueGrowth',
    'revenuePerCapita',
    'revenueInApp',
    'downloads',
    'revenueRetailPlatform',
    'revenueRetailPlatformDesktopMobileShare',
    'marketCapitalization',
    'marketVolume',
    'numberOfTrades',
    'netWorth',
    'claimPayments',
    'atpuOutwardRemittances',
    'atpuInwardRemittances',
    'productionGrowth',
    'productionVolumeGrowth',
    'airlinesRevenue',
    'airportVolume',
    'cost',
    'costGrowth',
    'containerPortThroughput',
    'revenueOther',
    'revenueGrowthOther',
    'revenueBrand',
    'premiumGrowth',
    'reserves',
    'energyEmissionIntencity',
    'tradeNetVolume'
  ] -%}
  
  {%- for priority in chart_priorities -%}
    '{{ priority }}'
    {%- if not loop.last -%},{%- endif -%}
  {%- endfor -%}
{% endmacro %}
