{% macro get_chart_priorities() %}
  {%- set chart_priorities = [
    'revenue',
    'arpu', 
    'usersPenetration',
    'volume',
    'pricePerUnit',
    'spendPerEmployee',
    'revenueShare',
    'users',
    'valueAdded',
    'output',
    'numberEmployees',
    'deliveries',
    'averageDealSize',
    'energyProduction',
    'energyProductionGrowth',
    'growthFactor',
    'transactionValue',
    'revenuePerPharmacy',
    'scenarioRevenue',
    'premium',
    'freightTransported',
    'transportationEfficiency',
    'production',
    'onlineRevenueShare',
    'exportValue',
    'importValue',
    'grossProductionValue',
    'medications',
    'revenueIndustryShare',
    'realEstateVolume',
    'productionVolume',
    'revenueDesktopMobileShare',
    'revenueTherapeuticArea',
    'lease',
    'interestIncome',
    'deposits',
    'loans',
    'osShare',
    'averageLenghtOfStay',
    'averageTransactionSize',
    'numberOfTransactions',
    'atpu',
    'aum',
    'containerTransport',
    'shareEmissions',
    'transactionValueOutwardRemittances',
    'transactionValueInwardRemittances',
    'realEstateValue',
    'mobileSubsriptions',
    'advisors',
    'revenueGrowth',
    'revenuePerCapita',
    'revenueInApp',
    'downloads',
    'revenueRetailPlatform',
    'revenueRetailPlatformDesktopMobileShare',
    'marketCapitalization',
    'marketVolume',
    'numberOfTrades',
    'netWorth',
    'claimPayments',
    'atpuOutwardRemittances',
    'atpuInwardRemittances',
    'productionGrowth',
    'productionVolumeGrowth',
    'airlinesRevenue',
    'airportVolume',
    'cost',
    'costGrowth',
    'containerPortThroughput',
    'revenueOther',
    'revenueGrowthOther',
    'revenueBrand',
    'premiumGrowth',
    'reserves',
    'energyEmissionIntencity',
    'tradeNetVolume'
  ] -%}
  
  {%- for priority in chart_priorities -%}
    '{{ priority }}'
    {%- if not loop.last -%},{%- endif -%}
  {%- endfor -%}
{% endmacro %}
