#!/bin/bash

# Load environment variables
source .env

# Export the variables so they're available to dbt
export RDS_USERNAME
export RDS_PASSWORD  
export RDS_DATABASE

# Log start time
echo "$(date): Starting dbt run for QUICK models (excluding long-running ones)..."

# Define the long-running models to exclude
EXCLUDE_MODELS="core_array_agg_values core_pages_to_agg_values mart_verticals_to_charts"

# Run all models except the long-running ones
if dbt run --exclude $EXCLUDE_MODELS --profiles-dir . --log-level info; then
    echo "$(date): Quick models completed successfully."
    exit 0
else
    echo "$(date): Quick models failed."
    exit 1
fi
