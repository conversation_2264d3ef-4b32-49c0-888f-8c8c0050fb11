#!/bin/bash

# Function to run in background
run_dbt_background() {
    # Load environment variables
    source .env

    # Export the variables so they're available to dbt
    export RDS_USERNAME
    export RDS_PASSWORD
    export RDS_DATABASE

    # Set environment variables for connection stability
    export PGCONNECT_TIMEOUT=60
    export PGSSLMODE=prefer

    # Log start time
    echo "$(date): Starting dbt run for QUICK models (excluding long-running ones)..."

    # Define the long-running models to exclude
    EXCLUDE_MODELS="core_array_agg_values core_pages_to_agg_values mart_verticals_to_charts"

    # Run all models except the long-running ones
    if dbt run --exclude $EXCLUDE_MODELS --profiles-dir . --log-level info; then
        echo "$(date): Quick models completed successfully."
        exit 0
    else
        echo "$(date): Quick models failed."
        exit 1
    fi
}

# Run in background with logging to single file
LOG_FILE="dbt_run.log"
echo "Starting quick models in background. Log file: $LOG_FILE"
echo "Monitor with: tail -f $LOG_FILE"

nohup bash -c "$(declare -f run_dbt_background); run_dbt_background" > "$LOG_FILE" 2>&1 &

echo "Background process started with PID: $!"
