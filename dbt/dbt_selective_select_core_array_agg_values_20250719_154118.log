Sat Jul 19 15:41:18 UTC 2025: Starting selective dbt run with args: --select core_array_agg_values
[0m15:41:20  Running with dbt=1.10.4
[0m15:41:20  Registered adapter: postgres=1.9.0
[0m15:41:21  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m15:41:21  Found 17 models, 2 data tests, 434 macros
[0m15:41:21  
[0m15:41:21  Concurrency: 1 threads (target='dev')
[0m15:41:21  
[0m15:41:21  1 of 1 START sql incremental model public.core_array_agg_values ................ [RUN]
[0m17:53:03  1 of 1 ERROR creating sql incremental model public.core_array_agg_values ....... [[31mERROR[0m in 7902.53s]
[0m17:53:04  
[0m17:53:04  Finished running 1 incremental model in 2 hours 11 minutes and 42.75 seconds (7902.75s).
[0m17:53:04  
[0m17:53:04  [31mCompleted with 1 error, 0 partial successes, and 0 warnings:[0m
[0m17:53:04  
[0m17:53:04  [31mFailure in model core_array_agg_values (models/core/core_array_agg_values.sql)[0m
[0m17:53:04    Database Error in model core_array_agg_values (models/core/core_array_agg_values.sql)
  could not receive data from server: Connection timed out
  SSL SYSCALL error: Connection timed out
[0m17:53:04  
[0m17:53:04    compiled code at target/compiled/smi_report/models/core/core_array_agg_values.sql
[0m17:53:04  
[0m17:53:04  Done. PASS=0 WARN=0 ERROR=1 SKIP=0 NO-OP=0 TOTAL=1
Sat Jul 19 17:53:04 UTC 2025: dbt run failed.
