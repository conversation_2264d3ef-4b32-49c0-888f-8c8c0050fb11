Sat Jul 19 15:41:18 UTC 2025: Starting selective dbt run with args: --select core_array_agg_values
[0m15:41:20  Running with dbt=1.10.4
[0m15:41:20  Registered adapter: postgres=1.9.0
[0m15:41:21  [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 3 unused configuration paths:
- models.smi_report.intermediate
- seeds.smi_report
- snapshots.smi_report
[0m15:41:21  Found 17 models, 2 data tests, 434 macros
[0m15:41:21  
[0m15:41:21  Concurrency: 1 threads (target='dev')
[0m15:41:21  
[0m15:41:21  1 of 1 START sql incremental model public.core_array_agg_values ................ [RUN]
