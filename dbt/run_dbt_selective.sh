#!/bin/bash

# Load environment variables
source .env

# Export the variables so they're available to dbt
export RDS_USERNAME
export RDS_PASSWORD  
export RDS_DATABASE

# Set environment variables for potentially long-running queries
export PGCONNECT_TIMEOUT=60
export PGCOMMAND_TIMEOUT=14400  # 4 hours in seconds
export PGSSLMODE=prefer
export PGKEEPALIVES_IDLE=7200
export PGKEEPALIVES_INTERVAL=30
export PGKEEPALIVES_COUNT=9

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [MODEL_NAMES...]"
    echo ""
    echo "Options:"
    echo "  --select MODEL1,MODEL2    Run specific models (comma-separated)"
    echo "  --exclude MODEL1,MODEL2   Exclude specific models"
    echo "  --all                     Run all models"
    echo "  --quick                   Run all except long-running models"
    echo "  --long                    Run only long-running models"
    echo "  --help                    Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 --select core_array_agg_values"
    echo "  $0 --exclude mart_verticals_to_charts"
    echo "  $0 --long"
    echo "  $0 --quick"
    echo "  $0 core_array_agg_values mart_verticals_to_charts"
}

# Parse arguments
DBT_ARGS=""
MODELS=""
LONG_MODELS="core_array_agg_values core_pages_to_agg_values mart_verticals_to_charts"

if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

while [[ $# -gt 0 ]]; do
    case $1 in
        --select|--models)
            DBT_ARGS="--select $2"
            MODELS="$2"
            shift 2
            ;;
        --exclude)
            DBT_ARGS="--exclude $2"
            shift 2
            ;;
        --all)
            DBT_ARGS=""
            shift
            ;;
        --quick)
            DBT_ARGS="--exclude $LONG_MODELS"
            shift
            ;;
        --long)
            DBT_ARGS="--select $LONG_MODELS"
            MODELS="$LONG_MODELS"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            # Treat remaining args as model names
            if [ -z "$MODELS" ]; then
                MODELS="$*"
                DBT_ARGS="--select $MODELS"
            fi
            break
            ;;
    esac
done

# Function to run in background
run_dbt_background() {
    # Load environment variables
    source .env

    # Export the variables so they're available to dbt
    export RDS_USERNAME
    export RDS_PASSWORD
    export RDS_DATABASE

    # Set environment variables for potentially long-running queries
    export PGCONNECT_TIMEOUT=60
    export PGCOMMAND_TIMEOUT=14400  # 4 hours in seconds
    export PGSSLMODE=prefer
    export PGKEEPALIVES_IDLE=7200
    export PGKEEPALIVES_INTERVAL=30
    export PGKEEPALIVES_COUNT=9

    # Log start time
    echo "$(date): Starting selective dbt run with args: $1"

    # Run dbt with the specified arguments
    if dbt run $1 --profiles-dir . --log-level info; then
        echo "$(date): dbt run completed successfully."
        exit 0
    else
        echo "$(date): dbt run failed."
        exit 1
    fi
}

# Run in background with logging to single file
LOG_FILE="dbt_run.log"

echo "Starting selective dbt run in background with args: $DBT_ARGS"
echo "Log file: $LOG_FILE"
echo "Monitor with: tail -f $LOG_FILE"

# Run in background with logging
nohup bash -c "$(declare -f run_dbt_background); run_dbt_background '$DBT_ARGS'" > "$LOG_FILE" 2>&1 &

echo "Background process started with PID: $!"
