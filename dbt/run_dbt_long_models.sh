#!/bin/bash

# Load environment variables
source .env

# Export the variables so they're available to dbt
export RDS_USERNAME
export RDS_PASSWORD  
export RDS_DATABASE

# Set additional environment variables for long-running queries
export PGCONNECT_TIMEOUT=60
export PGCOMMAND_TIMEOUT=14400  # 4 hours in seconds
export PGSSLMODE=prefer
export PGKEEPALIVES_IDLE=7200
export PGKEEPALIVES_INTERVAL=30
export PGKEEPALIVES_COUNT=9

# Log start time
echo "$(date): Starting dbt run for LONG-RUNNING models only..."

# Define the long-running models (using short names - dbt will resolve them)
LONG_MODELS="core_array_agg_values core_pages_to_agg_values mart_verticals_to_charts"

# Run only the long-running models with retry logic
MAX_RETRIES=3
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    echo "$(date): Starting dbt run attempt $((RETRY_COUNT + 1)) for long models..."
    
    if dbt run --select $LONG_MODELS --profiles-dir . --log-level info; then
        echo "$(date): Long-running models completed successfully."
        exit 0
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            echo "$(date): Long models failed, retrying in 60 seconds... (attempt $RETRY_COUNT/$MAX_RETRIES)"
            sleep 60
        else
            echo "$(date): Long models failed after $MAX_RETRIES attempts."
            exit 1
        fi
    fi
done
