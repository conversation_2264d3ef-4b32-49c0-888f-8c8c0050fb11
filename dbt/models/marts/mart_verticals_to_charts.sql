-- models/marts/mart_verticals_to_charts.sql

{{ config(
    materialized='table',
    pre_hook=[
        "SET statement_timeout = '24h';",
        "SET lock_timeout = '2h';",
        "SET idle_in_transaction_session_timeout = '24h';",
        "SET work_mem = '256MB';",
        "SET maintenance_work_mem = '512MB';",
        "SET temp_buffers = '64MB';",
        "SET tcp_keepalives_idle = 7200;",
        "SET tcp_keepalives_interval = 30;",
        "SET tcp_keepalives_count = 9;"
    ]
) }}

with vertical_graphs_verticals as (
    select * from {{ ref('core_vertical_graphs_verticals') }}
),

-- this will be for the parents
vertical_graphs_verticals2 as (
    select * from {{ ref('core_vertical_graphs_verticals') }}
),

pages_to_charts as (
    select * from {{ ref('core_pages_to_charts') }}
),

verticals_to_charts as (
    select
        vgv.market_name as market_insights_name,
        vgv.vertical_parent_id,
        vgv2.name_vertical as parent_name,
        vgv.vertical_id,
        vgv.name_vertical as market_name,
        ptc.geo_id,
        ptc.geo_name,
        ptc.order_chapter,
        ptc.chapter_name,
        ptc.order_chart,
        ptc.chart_title as chart_name,
        split_part(ptc.chart_key, '_', 1) as chart_key,
        ptc.chart_type,
        ptc.min_year,
        ptc.max_year,
        ptc.unit,
        ptc.unit_type,
        ptc.time_updated
    from vertical_graphs_verticals as vgv
    inner join vertical_graphs_verticals2 as vgv2 on vgv2.vertical_id = vgv.vertical_parent_id
    inner join pages_to_charts as ptc on ptc.vertical_graph_id = vgv.vertical_graph_id
    where LOWER(vgv.market_name) not in (LOWER('digital market outlook'), LOWER('market outlook highlights'), LOWER('trash outlook'))
    and ptc.chapter_name not in ('Analyst Opinion', 'Methodology', 'Key Market Indicators')
    order by vgv.market_name, parent_name, vgv.name_vertical, ptc.geo_id, ptc.order_chapter, ptc.order_chart
)


select * from verticals_to_charts
