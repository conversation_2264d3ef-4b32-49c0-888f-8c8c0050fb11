-- models/marts/mart_mongodb.sql

{{ config(
    materialized='table',
    pre_hook=[
        "SET statement_timeout = '24h';",
        "SET lock_timeout = '2h';",
        "SET idle_in_transaction_session_timeout = '24h';",
        "SET work_mem = '256MB';",
        "SET maintenance_work_mem = '512MB';",
        "SET temp_buffers = '64MB';",
        "SET tcp_keepalives_idle = 7200;",
        "SET tcp_keepalives_interval = 30;",
        "SET tcp_keepalives_count = 9;"
    ]
) }}

-- Final mart for MongoDB export
-- Combines chart data with aggregated values, filtered by geographic and chart priorities
-- This model provides the complete dataset for PowerPoint report generation

with filtered_data as (
    SELECT
        page_id,
        page_name,
        geo_name,
        outlook_name,
        chapter_name,
        chart_id,
        SPLIT_PART(chart_key, '_', 1) as chart_key,
        chart_type,
        order_chart,
        min_year,
        max_year,
        unit,
        unit_type,
        scale,
        number_decimal,
        chart_title,
        kpi_id,
        name as kpi_name,
        value_time_list,
        value_list
    FROM {{ ref('core_pages_to_agg_values') }}
    WHERE geo_name IN ({{ get_geo_priorities() }})
)

SELECT * FROM filtered_data
