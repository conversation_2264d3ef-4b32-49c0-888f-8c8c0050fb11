
-- models/core/core_pages_to_agg_values.sql

-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table
{{ config(
    materialized='incremental',
    unique_key=['kpi_id', 'geo_id'],
    pre_hook=[
        "SET statement_timeout = '24h';",
        "SET lock_timeout = '2h';",
        "SET idle_in_transaction_session_timeout = '24h';",
        "SET work_mem = '256MB';",
        "SET maintenance_work_mem = '512MB';",
        "SET temp_buffers = '64MB';",
        "SET tcp_keepalives_idle = 7200;",
        "SET tcp_keepalives_interval = 30;",
        "SET tcp_keepalives_count = 9;"
    ]
) }}


with pages_to_charts as (
    select * from {{ ref('core_pages_to_charts') }}
    -- where geo_id in (100)
),

charts_to_kpis as (
    select * from {{ ref('stg_chart_kpis') }}
),

array_agg_values as (
    select * from {{ ref('core_array_agg_values') }}
),

pages_to_values as (
    select
        ptc.page_id,
        ptc.vertical_graph_id,
        ptc.page_name,
        ptc.geo_id as ptc_geo_id,
        ptc.geo_name,
        ptc.geo_iso,
        ptc.outlook_name,
        ptc.definition,
        ptc.key_take_away,
        ptc.in_scope,
        ptc.out_scope,
        ptc.chapter_id,
        ptc.chapter_name,
        ptc.chart_id,
        ptc.chart_key,
        ptc.chart_type,
        ptc.order_chart,
        ptc.min_year,
        ptc.max_year,
        ptc.unit,
        ptc.unit_type,
        ptc.scale,
        ptc.number_decimal,
        ptc.chart_title,
        ptc.info,
        ctk.id as ctk_id,  -- This would be the unique identifier for the chart-kpi relationship
        ctk.chart_id as ctk_chart_id,
        ctk.kpi_id as ctk_kpi_id,
        ctk.aggregation_type as ctk_aggregation_type,
        ctk.order_chart as ctk_order_chart,
        aav.kpi_id,
        aav.name,
        aav.kpi_key,
        aav.geo_id,
        aav.unit_id,
        aav.value_time_list,
        aav.value_list
    from pages_to_charts as ptc
    inner join charts_to_kpis as ctk on ptc.chart_id = ctk.chart_id
    inner join array_agg_values as aav on ctk.kpi_id = aav.kpi_id and ptc.geo_id = aav.geo_id
)

select * from pages_to_values
