-- models/core/core_vertical_graphs_verticals.sql

with verticals as (
    select * from {{ ref('stg_verticals') }}
),

vertical_graphs as (
    select * from {{ ref('stg_vertical_graphs') }}
),

vertical_graphs_verticals as (
    select
        vertical_graphs.id as vertical_graph_id,
        vertical_graphs.vertical_id,
        vertical_graphs.vertical_parent_id,
        verticals.name_vertical,
        vertical_graphs.market_name
    from vertical_graphs
    left join verticals on verticals.id = vertical_graphs.vertical_id
)

select * from vertical_graphs_verticals
