version: 2

models:
  - name: core_kpis_to_values
    description: |
      This model joins KPI and KPI value data to provide a comprehensive view of key performance indicators (KPIs) across different geographical areas and timeframes.
    columns:
      - name: kpi_id
        description: The unique identifier for each KPI.
        tests:
          - not_null
      - name: name
        description: The name of the KPI.
      - name: kpi_key
        description: The key representing the KPI.
      - name: geo_id
        description: The unique identifier for the geographical region.
      - name: unit_id
        description: The unique identifier for the unit of measurement for the KPI value.
      - name: value_time
        description: The time period the KPI value refers to.
      - name: time_updated
        description: The time when the KPI value was last updated.
      - name: responsible_id
        description: The ID of the person responsible for the KPI.
      - name: value
        description: The value of the KPI.
        tests:
          - not_null

  - name: core_pages_to_charts
    description: |
      This model creates a comprehensive mapping between pages, charts, chapters, units, and geographical regions to link all aspects of the reporting data.
    columns:
      - name: page_id
        description: The unique identifier for each page.
      - name: vertical_graph_id
        description: The ID of the vertical graph associated with the page.
      - name: page_name
        description: The name of the page.
      - name: geo_id
        description: The ID of the geographical region the chart pertains to.
      - name: geo_name
        description: The name of the geographical region.
      - name: outlook_name
        description: The name of the outlook associated with the chart.
      - name: definition
        description: The definition provided for the chart.
      - name: key_take_away
        description: The key takeaway message from the chart.
      - name: in_scope
        description: Whether the chart is within the defined scope.
      - name: out_scope
        description: Whether the chart is out of scope.
      - name: chapter_id
        description: The ID of the chapter associated with the page.
      - name: chapter_name
        description: The name of the chapter.
      - name: order_chapter
        description: The order of the chapter in the report.
      - name: chart_id
        description: The unique identifier for the chart.
      - name: chart_key
        description: The key representing the chart.
      - name: chart_type
        description: The type of the chart (e.g., bar, pie).
      - name: order_chart
        description: The order of the chart in the chapter.
      - name: unit
        description: The unit of measurement used in the chart.
      - name: unit_type
        description: The type of unit measurement (e.g., percentage, currency).
      - name: scale
        description: The scale applied to the chart.
      - name: number_decimal
        description: The number of decimal places for the chart’s values.
      - name: chart_title
        description: The title of the chart.
      - name: info
        description: Additional information about the chart.
      - name: time_updated
        description: The last time the chart was updated.

  - name: core_vertical_graphs_verticals
    description: |
      This model maps vertical graph IDs to vertical IDs and their respective parent vertical IDs, providing a comprehensive view of the relationships between verticals and graphs.
    columns:
      - name: vertical_graph_id
        description: The ID of the vertical graph.
      - name: vertical_id
        description: The ID of the vertical within the graph.
      - name: vertical_parent_id
        description: The ID of the parent vertical.
      - name: name_vertical
        description: The name of the vertical.
      - name: market_name
        description: The market name associated with the vertical graph.

  - name: core_array_agg_values
    description: |
      This model aggregates KPI values and value_times into arrays for each KPI and geo combination.
    columns:
      - name: kpi_id
        description: The unique identifier for each KPI.
      - name: name
        description: The name of the KPI.
      - name: kpi_key
        description: The key representing the KPI.
      - name: geo_id
        description: The unique identifier for the geographical region.
      - name: unit_id
        description: The unique identifier for the unit of measurement.
      - name: value_time_list
        description: Array of value_time values for the KPI and geo.
      - name: value_list
        description: Array of KPI values for the KPI and geo.

  - name: core_pages_to_agg_values
    description: |
      This model joins page, chart, and aggregated KPI values for preparation into MongoDB.
    columns:
      - name: page_id
        description: The unique identifier for each page.
      - name: kpi_key
        description: The key representing the KPI.
      - name: geo_id
        description: The unique identifier for the geographical region.
      - name: unit_id
        description: The unique identifier for the unit of measurement.
      - name: value_time_list
        description: Array of value_time values for the KPI and geo.
      - name: value_list
        description: Array of KPI values for the KPI and geo.
