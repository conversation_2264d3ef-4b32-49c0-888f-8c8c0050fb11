-- models/core/core_array_agg_values.sql

-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table
{{ config(
    materialized='incremental',
    unique_key=['kpi_id', 'geo_id'],
    pre_hook=[
        "SET statement_timeout = '4h';",
        "SET lock_timeout = '30min';",
        "SET idle_in_transaction_session_timeout = '4h';",
        "SET work_mem = '64MB';",
        "SET maintenance_work_mem = '128MB';",
        "SET temp_buffers = '32MB';",
        "SET tcp_keepalives_idle = 7200;",
        "SET tcp_keepalives_interval = 30;",
        "SET tcp_keepalives_count = 9;"
    ]
) }}


with kv as (
    select * from {{ ref('core_kpis_to_values') }}
),

-- Filter and prepare data for aggregation
kv_filtered as (
    select
        kv.kpi_id,
        kv.name,
        kv.kpi_key,
        kv.geo_id,
        kv.unit_id,
        kv.value_time,
        kv.value,
        kv.time_updated
    from kv
    where kv.value is not null
),

aav as (
    SELECT
        kv_filtered.kpi_id,
        kv_filtered.name,
        kv_filtered.kpi_key,
        kv_filtered.geo_id,
        kv_filtered.unit_id,
        array_agg(kv_filtered.value_time ORDER BY kv_filtered.value_time) AS value_time_list,
        array_agg(kv_filtered.value ORDER BY kv_filtered.value_time) AS value_list,
        max(kv_filtered.time_updated) as time_updated
    FROM
        kv_filtered
    GROUP BY
        kv_filtered.kpi_id, kv_filtered.name, kv_filtered.kpi_key, kv_filtered.geo_id, kv_filtered.unit_id
    HAVING
        array_length(array_agg(kv_filtered.value) FILTER (WHERE kv_filtered.value IS NOT NULL), 1) > 0
)

SELECT * FROM aav
