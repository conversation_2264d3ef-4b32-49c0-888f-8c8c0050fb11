-- models/core/core_kpis_to_values.sql

-- materlialized as incremental so that it can be updated with new KPI values without rebuilding the entire table
{{ config(materialized='incremental', unique_key='kpi_id') }}


with kpis as (
    select * from {{ ref('stg_kpis') }}
),

kpis_values as (
    select * from {{ ref('stg_kpis_values') }}
),

kpis_to_values as (
    select
        kpis.id as kpi_id,
        kpis.name,
        kpis.kpi_key,
        kpis_values.geo_id,
        kpis_values.unit_id,
        kpis_values.value_time,
        kpis_values.time_updated,
        kpis_values.responsible_id,
        kpis_values.value
    from kpis
    left join kpis_values on kpis.id = kpis_values.kpi_id
    where kpis_values.value is not null
)

select * from kpis_to_values
