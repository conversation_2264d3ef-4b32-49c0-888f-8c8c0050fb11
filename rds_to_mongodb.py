#!/usr/bin/env python3
"""
RDS to MongoDB Data Transfer Script
Transforms flat mart_mongodb data into hierarchical chart-based documents
for the market_insights collection.
"""

import os
import json
import psycopg2
from collections import defaultdict
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def connect_to_postgres():
    """Connect to PostgreSQL RDS database"""
    try:
        connection = psycopg2.connect(
            host="smi-report-db.cisims9lnzeb.eu-central-1.rds.amazonaws.com",
            port=5432,
            database=os.getenv("RDS_DATABASE", "postgres"),
            user=os.getenv("RDS_USERNAME"),
            password=os.getenv("RDS_PASSWORD")
        )

        print("✅ Successfully connected to PostgreSQL RDS")
        return connection

    except Exception as e:
        print(f"❌ Error connecting to PostgreSQL: {e}")
        return None

def connect_to_mongodb():
    """Connect to MongoDB using direct connection"""
    try:
        client = MongoClient(
            host="*************",  # Private IP of MongoDB EC2
            port=27017,
            username="admin",
            password="statista123",
            authSource="admin",
            serverSelectionTimeoutMS=5000  # 5 second timeout
        )

        # Test connection
        client.admin.command('ping')
        print("✅ Successfully connected to MongoDB")

        db = client['statista_insights']
        collection = db['market_insights']

        return client, collection

    except Exception as e:
        print(f"❌ Error connecting to MongoDB: {e}")
        return None, None

def parse_postgres_array(array_data):
    """Convert PostgreSQL array string or Python list to Python list, handling Decimal objects"""
    from decimal import Decimal

    # If it's already a list, convert any Decimal objects to float
    if isinstance(array_data, list):
        result = []
        for item in array_data:
            if isinstance(item, Decimal):
                result.append(float(item))
            else:
                result.append(item)
        return result

    # If it's None or empty string, return empty list
    if not array_data or (isinstance(array_data, str) and array_data.strip() == ''):
        return []

    # If it's a string, parse it as PostgreSQL array format
    if isinstance(array_data, str):
        try:
            # Remove the curly braces and split by comma
            cleaned = array_data.strip('{}')
            if not cleaned:
                return []

            # Split by comma and convert to appropriate type
            items = [item.strip() for item in cleaned.split(',')]

            # Try to convert to numbers if possible, otherwise keep as strings
            result = []
            for item in items:
                try:
                    # Try integer first
                    if '.' not in item:
                        result.append(int(item))
                    else:
                        result.append(float(item))
                except ValueError:
                    # Keep as string if not a number
                    result.append(item)

            return result
        except Exception as e:
            print(f"❌ Error parsing array string '{array_data}': {e}")
            return []

    # For any other type, try to convert to list and handle Decimals
    try:
        result = []
        for item in array_data:
            if isinstance(item, Decimal):
                result.append(float(item))
            else:
                result.append(item)
        return result
    except Exception as e:
        print(f"❌ Error converting to list '{array_data}': {e}")
        return []

def fetch_mart_mongodb_data(connection, page_filter="Household Appliances"):
    """Fetch data from mart_mongodb table and group by chart"""

    cursor = connection.cursor()
    
    # Query mart_mongodb with filter for testing
    query = """
    SELECT
        page_id, page_name, geo_name, outlook_name, chapter_name,
        chart_id, chart_key, chart_type, order_chart,
        min_year, max_year, unit, unit_type, scale, number_decimal, chart_title,
        kpi_id, kpi_name, value_time_list, value_list
    FROM mart_mongodb
    WHERE page_name = %s
    ORDER BY chart_id, geo_name, kpi_id
    """
    
    try:
        cursor.execute(query, (page_filter,))
        rows = cursor.fetchall()

        # Get column names
        columns = [desc[0] for desc in cursor.description]

        # Convert to dictionaries and group by chart name (not chart_id)
        chart_data = defaultdict(list)

        for row in rows:
            row_dict = dict(zip(columns, row))
            # Group by chart_title to create one document per unique chart
            chart_key = row_dict['chart_title']
            chart_data[chart_key].append(row_dict)

        print(f"✅ Fetched {len(rows)} rows for '{page_filter}', grouped into {len(chart_data)} unique chart names")
        return chart_data

    except Exception as e:
        print(f"❌ Error fetching data from PostgreSQL: {e}")
        return None
    finally:
        cursor.close()

def create_mongodb_document(chart_title, rows):
    """Create MongoDB document for a chart with hierarchical geo/kpi structure"""

    first_row = rows[0]
    # Use first chart_id as the document ID (they should all be the same chart type)
    chart_id = first_row['chart_id']

    # Create the base document structure
    document = {
        "unifiedContentMetaData": {
            "contractVersion": "1.0.0",
            "id": int(chart_id),
            "contentId": int(chart_id),
            "contentType": "tbd",
            "title": "Market Insights Chart",
            "subtitle": "Market Insights Chart",
            "description": "Chart based on Market Insights",
            "url": "url",
            "type": "chart",
            "metaData": {
                "chartData": {
                    "outlookName": first_row['outlook_name'],
                    "pageName": first_row['page_name'],
                    "chapterName": first_row['chapter_name'],
                    "chartName": chart_title,
                    "chartKey": first_row['chart_key'],
                    "chartType": first_row['chart_type'],
                    "minYear": first_row['min_year'],
                    "maxYear": first_row['max_year'],
                    "unit": first_row['unit'],
                    "scale": first_row['scale'],
                    "numberDecimal": first_row['number_decimal'],
                    "source": "Market Insights"
                }
            }
        },
        "chartGeos": []
    }

    # Group rows by geo_name to create chartGeos structure
    geo_groups = defaultdict(list)
    for row in rows:
        geo_groups[row['geo_name']].append(row)

    # Create chartGeos array
    for geo_name, geo_rows in geo_groups.items():
        chart_geo = {
            "chartGeo": geo_name,
            "kpis": []
        }

        # Group by KPI within each geo
        kpi_groups = defaultdict(list)
        for row in geo_rows:
            kpi_groups[row['kpi_name']].append(row)

        # Create KPIs array for this geo
        for kpi_name, kpi_rows in kpi_groups.items():
            # For each KPI, we should have one row (since we're grouping properly)
            kpi_row = kpi_rows[0]  # Take first row for this KPI

            kpi_data = {
                "kpiName": kpi_name,
                "years": parse_postgres_array(kpi_row['value_time_list']),
                "values": parse_postgres_array(kpi_row['value_list'])
            }
            chart_geo["kpis"].append(kpi_data)

        document["chartGeos"].append(chart_geo)

    return document

def process_rds_to_mongodb(page_filter="Household Appliances"):
    """Main function to process data from RDS to MongoDB"""

    print(f"🚀 Starting RDS to MongoDB transfer for page: '{page_filter}'")

    # Connect to PostgreSQL
    pg_connection = connect_to_postgres()
    if pg_connection is None:
        return

    # Connect to MongoDB
    mongo_client, mongo_collection = connect_to_mongodb()
    if mongo_collection is None:
        pg_connection.close()
        return
    
    try:
        # Fetch data from PostgreSQL
        print(f"📊 Fetching data from mart_mongodb table...")
        chart_data = fetch_mart_mongodb_data(pg_connection, page_filter)

        if chart_data is None:
            return

        # Clear existing data in MongoDB (optional for testing)
        print("🗑️  Clearing existing MongoDB data...")
        result = mongo_collection.delete_many({})
        print(f"   Deleted {result.deleted_count} existing documents")

        # Process each chart
        inserted_count = 0
        for chart_title, rows in chart_data.items():
            print(f"📈 Processing chart: {chart_title} - {len(rows)} rows")

            # Create MongoDB document
            document = create_mongodb_document(chart_title, rows)

            try:
                # Insert document
                result = mongo_collection.insert_one(document)
                print(f"   ✅ Inserted document with ID: {result.inserted_id}")
                inserted_count += 1

            except Exception as e:
                print(f"   ❌ Error inserting document for chart {chart_title}: {e}")
        
        print(f"\n🎉 Completed! Inserted {inserted_count} chart documents into MongoDB")

        # Print summary
        total_docs = mongo_collection.count_documents({})
        print(f"📊 Total documents in collection: {total_docs}")

    finally:
        pg_connection.close()
        mongo_client.close()
        print("🔌 Database connections closed")

if __name__ == "__main__":
    # Process only Household Appliances for testing
    process_rds_to_mongodb(page_filter="Household Appliances")