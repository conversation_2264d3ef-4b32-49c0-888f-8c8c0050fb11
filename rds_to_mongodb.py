#!/usr/bin/env python3

import os
import json
import psycopg2
from collections import defaultdict
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def connect_to_postgres():
    """Connect to PostgreSQL RDS database"""
    try:
        connection = psycopg2.connect(
            host="smi-report-db.cisims9lnzeb.eu-central-1.rds.amazonaws.com",
            port=5432,
            database=os.getenv("RDS_DATABASE", "postgres"),
            user=os.getenv("RDS_USERNAME"),
            password=os.getenv("RDS_PASSWORD")
        )
        
        print("Successfully connected to PostgreSQL RDS")
        return connection
        
    except Exception as e:
        print(f"Error connecting to PostgreSQL: {e}")
        return None

def connect_to_mongodb():
    """Connect to MongoDB using direct connection"""
    try:
        client = MongoClient(
            host="*************",  # Private IP of MongoDB EC2
            port=27017,
            username="admin",
            password="statista123",
            authSource="admin",
            serverSelectionTimeoutMS=5000  # 5 second timeout
        )

        # Test connection
        client.admin.command('ping')
        print("Successfully connected to MongoDB")

        db = client['statista_insights']
        collection = db['market_insights']

        # Ensure collection exists
        if 'market_insights' not in db.list_collection_names():
            print("Creating 'market_insights' collection...")

        return collection

    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        return None

def parse_postgres_array(array_str):
    """Convert PostgreSQL array string to Python list"""
    if not array_str or array_str.strip() == '':
        return []
    
    try:
        # Remove the curly braces and split by comma
        cleaned = array_str.strip('{}')
        if not cleaned:
            return []
        
        # Split by comma and convert to appropriate type
        items = [item.strip() for item in cleaned.split(',')]
        
        # Try to convert to numbers if possible, otherwise keep as strings
        result = []
        for item in items:
            try:
                # Try integer first
                if '.' not in item:
                    result.append(int(item))
                else:
                    result.append(float(item))
            except ValueError:
                # Keep as string if not a number
                result.append(item)
        
        return result
    except Exception as e:
        print(f"Error parsing array string '{array_str}': {e}")
        return []

def fetch_pages_data(connection, limit=None):
    """Fetch data from core_pages_to_agg_values table and group by page_name"""
    
    cursor = connection.cursor()
    
    # Build query with optional limit
    query = """
    SELECT 
        page_id, vertical_graph_id, page_name, ptc_geo_id, geo_name, geo_iso,
        outlook_name, definition, key_take_away, in_scope, out_scope,
        chapter_id, chapter_name, chart_id, chart_key, chart_type, order_chart,
        min_year, max_year, unit, unit_type, scale, number_decimal, chart_title,
        info, ctk_id, ctk_chart_id, ctk_kpi_id, ctk_aggregation_type, ctk_order_chart,
        kpi_id, name, kpi_key, geo_id, unit_id, value_time_list, value_list
    FROM core_pages_to_agg_values
    ORDER BY page_name, chart_id
    """
    
    if limit:
        query += f" LIMIT {limit}"
    
    try:
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # Get column names
        columns = [desc[0] for desc in cursor.description]
        
        # Convert to dictionaries and group by page_name
        page_data = defaultdict(list)
        
        for row in rows:
            row_dict = dict(zip(columns, row))
            page_name = row_dict['page_name']
            page_data[page_name].append(row_dict)
        
        print(f"Fetched {len(rows)} rows, grouped into {len(page_data)} unique pages")
        return page_data
        
    except Exception as e:
        print(f"Error fetching data from PostgreSQL: {e}")
        return None
    finally:
        cursor.close()

def create_mongodb_document(page_name, rows):
    """Create MongoDB document for a page with its chart data"""
    
    # Use first row for page-level metadata
    first_row = rows[0]
    
    # Create the base document structure
    document = {
        "unifiedContentMetaData": {
            "contractVersion": "1.0.0",
            "id": int(first_row['page_id']),
            "contentId": int(first_row['page_id']),
            "contentType": "tbd",
            "title": "Market Insights Report",
            "subtitle": "Market Insights Report", 
            "description": "Report based on market insights",
            "url": "url",
            "type": "report",
            "metaData": {
                "reportType": "MarketInsightsReport",
                "reportData": {
                    "reportID": int(first_row['page_id']),
                    "reportName": page_name,
                    "reportDefinition": first_row['definition'] or "",
                    "source": "Market Insights",
                    "iso": first_row.get('geo_iso', 'WOR'),
                    "country": first_row.get('geo_name', 'Worldwide')
                }
            }
        },
        "chartData": []
    }
    
    # Add chart data for each row
    for row in rows:
        chart_data = {
            "chartIndex": str(row['chart_id']),
            "chapterName": row['chapter_name'] or "",
            "chartName": row['chart_title'] or "",
            "chartGeo": str(row['ptc_geo_id']),
            "chartGeoName": row['geo_name'] or "",
            "data": {
                "Years": parse_postgres_array(row['value_time_list']),
                "Values": parse_postgres_array(row['value_list'])
            }
        }
        document["chartData"].append(chart_data)
    
    return document

def process_rds_to_mongodb(limit=None):
    """Main function to process data from RDS to MongoDB"""
    
    print(f"Starting RDS to MongoDB transfer (limit: {limit if limit else 'No limit'})")
    
    # Connect to PostgreSQL
    pg_connection = connect_to_postgres()
    if pg_connection is None:
        return
    
    # Connect to MongoDB
    mongo_collection = connect_to_mongodb()
    if mongo_collection is None:
        pg_connection.close()
        return
    
    try:
        # Fetch data from PostgreSQL
        print("Fetching data from core_pages_to_agg_values table...")
        page_data = fetch_pages_data(pg_connection, limit)
        
        if page_data is None:
            return
        
        # Clear existing data in MongoDB (optional for testing)
        print("Clearing existing MongoDB data...")
        mongo_collection.delete_many({})
        
        # Process each page
        inserted_count = 0
        for page_name, rows in page_data.items():
            print(f"Processing page: {page_name} ({len(rows)} charts)")
            
            # Create MongoDB document
            document = create_mongodb_document(page_name, rows)
            
            try:
                # Insert document
                result = mongo_collection.insert_one(document)
                print(f"  Inserted document with ID: {result.inserted_id}")
                inserted_count += 1
                
            except Exception as e:
                print(f"  Error inserting document for page {page_name}: {e}")
        
        print(f"\nCompleted! Inserted {inserted_count} documents into MongoDB")
        
        # Print summary
        total_docs = mongo_collection.count_documents({})
        print(f"Total documents in collection: {total_docs}")
        
    finally:
        pg_connection.close()
        print("PostgreSQL connection closed")

if __name__ == "__main__":
    # Set limit for testing (set to None for no limit)
    TEST_LIMIT = 100
    
    process_rds_to_mongodb(limit=TEST_LIMIT) 