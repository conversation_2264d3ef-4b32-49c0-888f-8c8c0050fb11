#!/usr/bin/env python3
"""
Professional MongoDB to PowerPoint automation script.
Extracts data from MongoDB and populates PowerPoint templates.
"""

import os
from datetime import datetime
from pymongo import MongoClient
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import boto3
from botocore.exceptions import ClientError
from powerpoint_settings import chart_priority
from powerpoint_settings import chart_priority


class MongoToPowerPointProcessor:
    """Professional class for MongoDB to PowerPoint data processing."""

    def __init__(self, mongo_host="*************", mongo_port=27017,
                 mongo_user="admin", mongo_pass="statista123",
                 s3_bucket="market-insights-reports-s3"):
        """Initialize MongoDB connection and S3 settings."""
        self.mongo_host = mongo_host
        self.mongo_port = mongo_port
        self.mongo_user = mongo_user
        self.mongo_pass = mongo_pass
        self.s3_bucket = s3_bucket
        self.client = None
        self.db = None
        self.collection = None
        self.s3_client = None
        
    def connect_to_mongodb(self):
        """Establish connection to MongoDB."""
        try:
            self.client = MongoClient(
                host=self.mongo_host,
                port=self.mongo_port,
                username=self.mongo_user,
                password=self.mongo_pass,
                authSource="admin",
                serverSelectionTimeoutMS=5000
            )
            
            # Test connection
            self.client.admin.command('ping')
            print("✅ MongoDB connection successful!")
            
            # Connect to database and collection
            self.db = self.client['statista_insights']
            self.collection = self.db['market_insights']
            
            return True
            
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False
    
    def get_document_by_id(self, doc_id=None):
        """Get a specific document or the first one if no ID provided."""
        try:
            if doc_id:
                document = self.collection.find_one({"unifiedContentMetaData.id": doc_id})
            else:
                document = self.collection.find_one()
            
            if document:
                print(f"✅ Document retrieved: {document['unifiedContentMetaData']['metaData']['reportData']['reportName']}")
                return document
            else:
                print("❌ No document found")
                return None
                
        except Exception as e:
            print(f"❌ Error retrieving document: {e}")
            return None

    def get_priority_charts_for_page(self, page_name="Household Appliances", max_charts=3):
        """Get the first 3 existing charts from chart_priority for a specific page."""
        try:
            print(f"🔍 Finding priority charts for page: {page_name}")

            # Get all documents for the specified page and outlook
            documents = list(self.collection.find({
                "unifiedContentMetaData.metaData.chartData.pageName": page_name,
                "unifiedContentMetaData.metaData.chartData.outlookName": "cmo"
            }))

            if not documents:
                print(f"❌ No documents found for page: {page_name}")
                return []

            print(f"📊 Found {len(documents)} documents for {page_name}")

            # Extract available chart keys
            available_charts = {}
            for doc in documents:
                chart_data = doc.get("unifiedContentMetaData", {}).get("metaData", {}).get("chartData", {})
                chart_key = chart_data.get("chartKey")
                if chart_key:
                    available_charts[chart_key] = doc
                    print(f"   📈 Available chart: {chart_key}")

            # Find first 3 charts from priority list that exist
            selected_charts = []
            for priority_chart in chart_priority:
                if priority_chart in available_charts and len(selected_charts) < max_charts:
                    selected_charts.append({
                        'chart_key': priority_chart,
                        'document': available_charts[priority_chart],
                        'chart_object': f"chart_global_ind_{len(selected_charts) + 1}"
                    })
                    print(f"✅ Selected priority chart {len(selected_charts)}: {priority_chart} → {selected_charts[-1]['chart_object']}")

            if len(selected_charts) < max_charts:
                print(f"⚠️  Only found {len(selected_charts)} priority charts out of {max_charts} requested")

            return selected_charts

        except Exception as e:
            print(f"❌ Error finding priority charts: {e}")
            return []

    def extract_worldwide_data(self, document, min_year=None, max_year=None):
        """Extract Worldwide geography data from a document, filtered by year range."""
        try:
            chart_geos = document.get("chartGeos", [])

            # Find Worldwide geography
            worldwide_data = None
            for geo in chart_geos:
                if geo.get("chartGeo") == "Worldwide":
                    worldwide_data = geo
                    break

            if not worldwide_data:
                print("❌ No Worldwide data found in document")
                return None

            # Get chart metadata for year filtering
            chart_data = document.get("unifiedContentMetaData", {}).get("metaData", {}).get("chartData", {})
            if min_year is None:
                min_year = chart_data.get("minYear")
            if max_year is None:
                max_year = chart_data.get("maxYear")

            print(f"📅 Filtering data for years: {min_year} - {max_year}")

            # Process KPIs and filter by year range
            filtered_kpis = []
            for kpi in worldwide_data.get("kpis", []):
                years = kpi.get("years", [])
                values = kpi.get("values", [])

                if not years or not values or len(years) != len(values):
                    print(f"⚠️  Skipping KPI {kpi.get('kpiName', 'Unknown')} - invalid data")
                    continue

                # Filter by year range
                filtered_years = []
                filtered_values = []

                for year, value in zip(years, values):
                    if (min_year is None or year >= min_year) and (max_year is None or year <= max_year):
                        filtered_years.append(year)
                        filtered_values.append(value)

                if filtered_years:
                    filtered_kpis.append({
                        'kpiName': kpi.get('kpiName'),
                        'years': filtered_years,
                        'values': filtered_values
                    })
                    print(f"   📈 {kpi.get('kpiName')}: {len(filtered_years)} data points ({filtered_years[0]}-{filtered_years[-1]})")

            return {
                'chartGeo': 'Worldwide',
                'kpis': filtered_kpis,
                'chartData': chart_data
            }

        except Exception as e:
            print(f"❌ Error extracting Worldwide data: {e}")
            return None

    def extract_data_from_document(self, document):
        """Extract relevant data from MongoDB document."""
        try:
            metadata = document['unifiedContentMetaData']['metaData']['reportData']
            
            extracted_data = {
                'report_id': document['unifiedContentMetaData']['id'],
                'report_name': metadata['reportName'],
                'report_definition': metadata.get('reportDefinition', ''),
                'country': metadata.get('country', 'Unknown'),
                'iso': metadata.get('iso', 'N/A'),
                'source': metadata.get('source', 'Market Insights'),
                'chart_data': document.get('chartData', [])
            }
            
            print(f"✅ Data extracted for report: {extracted_data['report_name']}")
            return extracted_data
            
        except Exception as e:
            print(f"❌ Error extracting data: {e}")
            return None
    
    def find_and_update_by_object_name(self, presentation, object_name, replacement_text):
        """Find and update PowerPoint objects by their name (from Selection Pane)."""
        replacements_made = 0

        for slide in presentation.slides:
            for shape in slide.shapes:
                # Check if this shape has the target object name
                if hasattr(shape, 'name') and shape.name == object_name:
                    # Update the text content of this specific object
                    if hasattr(shape, 'text'):
                        shape.text = replacement_text
                        replacements_made += 1
                        print(f"  ✓ Updated object '{object_name}' with '{replacement_text}'")
                    elif hasattr(shape, 'text_frame'):
                        # Clear existing text and add new text
                        shape.text_frame.clear()
                        p = shape.text_frame.paragraphs[0]
                        p.text = replacement_text
                        replacements_made += 1
                        print(f"  ✓ Updated object '{object_name}' text frame with '{replacement_text}'")
                    else:
                        print(f"  ⚠️ Found object '{object_name}' but it doesn't support text")

        return replacements_made

    def update_chart_data(self, presentation, chart_name, years_data, values_data, chart_title=None):
        """Update PowerPoint chart with new data using correct python-pptx approach."""
        charts_updated = 0

        for slide in presentation.slides:
            for shape in slide.shapes:
                # Check if this shape is the target chart
                if hasattr(shape, 'name') and shape.name == chart_name:
                    # Check if it's a chart
                    if hasattr(shape, 'chart'):
                        try:
                            from pptx.chart.data import CategoryChartData

                            chart = shape.chart

                            # Create new chart data with proper structure
                            chart_data = CategoryChartData()

                            # Set categories (X-axis labels) - convert years to strings
                            chart_data.categories = [str(year) for year in years_data]

                            # Add series with values (Y-axis data)
                            chart_data.add_series('Health Indicator', values_data)

                            # Replace the chart data
                            chart.replace_data(chart_data)

                            # Update chart title if provided
                            if chart_title:
                                if chart.has_title:
                                    chart.chart_title.text_frame.text = chart_title
                                else:
                                    chart.has_title = True
                                    chart.chart_title.text_frame.text = chart_title

                            # Apply formatting improvements
                            self._format_chart_for_stacked_display(chart)

                            charts_updated += 1
                            print(f"  ✓ Updated chart '{chart_name}' with {len(values_data)} data points")
                            print(f"    Years: {years_data[0]} - {years_data[-1]}")
                            print(f"    Values range: {min(values_data):.2f} - {max(values_data):.2f}")

                        except Exception as e:
                            print(f"  ❌ Error updating chart '{chart_name}': {e}")
                            # Provide diagnostic information
                            try:
                                chart = shape.chart
                                print(f"    Chart type: {chart.chart_type}")
                                print(f"    Chart has {len(chart.series)} series")
                                if len(chart.series) > 0:
                                    series = chart.series[0]
                                    print(f"    First series name: {series.name}")
                                    print(f"    First series has {len(series.values)} values")
                            except Exception as e2:
                                print(f"    Additional error getting chart info: {e2}")
                    else:
                        print(f"  ⚠️ Object '{chart_name}' found but is not a chart")

        return charts_updated

    def update_chart_data_multi_series(self, presentation, chart_name, kpis_data, chart_title=None):
        """Update PowerPoint chart with multiple KPI series for stacked/multi-line charts."""
        charts_updated = 0

        for slide in presentation.slides:
            for shape in slide.shapes:
                # Check if this shape is the target chart
                if hasattr(shape, 'name') and shape.name == chart_name:
                    # Check if it's a chart
                    if hasattr(shape, 'chart'):
                        try:
                            from pptx.chart.data import CategoryChartData

                            chart = shape.chart

                            # Validate that all KPIs have the same years structure
                            if not kpis_data:
                                print(f"  ❌ No KPI data provided for {chart_name}")
                                continue

                            # Use years from first KPI (they should all be the same)
                            reference_kpi = kpis_data[0]
                            years_data = reference_kpi.get('years', [])

                            if not years_data:
                                print(f"  ❌ No years data in first KPI for {chart_name}")
                                continue

                            # Validate all KPIs have same year structure
                            valid_kpis = []
                            for kpi in kpis_data:
                                kpi_years = kpi.get('years', [])
                                kpi_values = kpi.get('values', [])
                                kpi_name = kpi.get('kpiName', 'Unknown KPI')

                                if (len(kpi_years) == len(years_data) and
                                    len(kpi_values) == len(years_data) and
                                    kpi_years == years_data):  # Same year sequence
                                    valid_kpis.append(kpi)
                                    print(f"    📊 Valid KPI: {kpi_name} ({len(kpi_values)} values)")
                                else:
                                    print(f"    ⚠️  Skipping KPI {kpi_name} - mismatched years/values")

                            if not valid_kpis:
                                print(f"  ❌ No valid KPIs found for {chart_name}")
                                continue

                            # Create new chart data with multiple series
                            chart_data = CategoryChartData()

                            # Set categories (X-axis labels) - convert years to strings
                            chart_data.categories = [str(int(year)) for year in years_data]

                            # Add each KPI as a separate series
                            for kpi in valid_kpis:
                                kpi_name = kpi.get('kpiName', 'Data Series')
                                kpi_values = kpi.get('values', [])

                                # Clean the KPI name for better display
                                clean_name = kpi_name.replace(' revenue by segment', '').replace(' Market', '').strip()
                                if not clean_name:
                                    clean_name = kpi_name

                                chart_data.add_series(clean_name, [float(value) for value in kpi_values])
                                print(f"    ✅ Added series: {clean_name}")

                            # Replace the chart data
                            chart.replace_data(chart_data)

                            # Update chart title if provided
                            if chart_title:
                                if chart.has_title:
                                    chart.chart_title.text_frame.text = chart_title
                                else:
                                    chart.has_title = True
                                    chart.chart_title.text_frame.text = chart_title

                            # Apply formatting improvements for stacked charts
                            self._format_chart_for_stacked_display(chart)

                            charts_updated += 1
                            print(f"  ✅ Updated chart '{chart_name}' with {len(valid_kpis)} series")
                            print(f"    Years: {years_data[0]} - {years_data[-1]} ({len(years_data)} points)")

                            # Show value ranges for each series
                            for kpi in valid_kpis:
                                values = kpi.get('values', [])
                                if values:
                                    kpi_name = kpi.get('kpiName', 'Unknown')
                                    clean_name = kpi_name.replace(' revenue by segment', '').replace(' Market', '').strip()
                                    print(f"    📈 {clean_name}: {min(values):.2f} - {max(values):.2f}")

                        except Exception as e:
                            print(f"  ❌ Error updating chart '{chart_name}': {e}")
                            import traceback
                            traceback.print_exc()
                    else:
                        print(f"  ⚠️ Object '{chart_name}' found but is not a chart")

        return charts_updated

    def analyze_chart_capabilities(self, chart_shape):
        """Analyze a chart to determine if it can handle multiple series."""
        try:
            if not hasattr(chart_shape, 'chart'):
                return {'can_multi_series': False, 'reason': 'Not a chart shape'}

            chart = chart_shape.chart
            chart_type = chart.chart_type
            existing_series_count = len(chart.series) if chart.series else 0

            # Get detailed chart type information
            chart_type_name = str(chart_type).split('.')[-1] if hasattr(chart_type, '__str__') else str(chart_type)
            chart_type_value = int(chart_type) if hasattr(chart_type, '__int__') else chart_type

            # Chart types that typically support multiple series
            multi_series_types = [
                'COLUMN_CLUSTERED',      # Clustered column
                'COLUMN_STACKED',        # Stacked column
                'COLUMN_STACKED_100',    # 100% stacked column
                'BAR_CLUSTERED',         # Clustered bar
                'BAR_STACKED',           # Stacked bar
                'BAR_STACKED_100',       # 100% stacked bar
                'LINE',                  # Line chart
                'LINE_MARKERS',          # Line with markers
                'LINE_MARKERS_STACKED',  # Stacked line with markers
                'LINE_STACKED',          # Stacked line
                'LINE_STACKED_100',      # 100% stacked line
                'AREA',                  # Area chart
                'AREA_STACKED',          # Stacked area
                'AREA_STACKED_100'       # 100% stacked area
            ]

            # Check if chart type supports multiple series
            supports_multi_series = any(multi_type in chart_type_name for multi_type in multi_series_types)

            # Additional check: if chart already has multiple series, it likely supports them
            has_multiple_series = existing_series_count > 1

            # Identify if this is a line chart (which needs special handling)
            is_line_chart = any(line_type in chart_type_name for line_type in ['LINE', 'SCATTER'])

            analysis = {
                'can_multi_series': supports_multi_series or has_multiple_series,
                'chart_type': chart_type_name,
                'chart_type_value': chart_type_value,
                'existing_series_count': existing_series_count,
                'is_line_chart': is_line_chart,
                'reason': f"Chart type: {chart_type_name} ({chart_type_value}), Existing series: {existing_series_count}"
            }

            # Get existing series names for reference
            if chart.series:
                series_names = []
                for series in chart.series:
                    try:
                        series_names.append(series.name or f"Series {len(series_names) + 1}")
                    except:
                        series_names.append(f"Series {len(series_names) + 1}")
                analysis['existing_series_names'] = series_names

            # Add warning for line charts
            if is_line_chart:
                analysis['warning'] = 'Line chart detected - using conservative update approach'

            return analysis

        except Exception as e:
            return {
                'can_multi_series': False,
                'reason': f'Error analyzing chart: {e}',
                'chart_type': 'Unknown',
                'chart_type_value': 0,
                'existing_series_count': 0,
                'is_line_chart': False
            }

    def _format_chart_for_stacked_display(self, chart):
        """Apply formatting improvements for charts (careful with line charts)."""
        try:
            # Detect chart type for safer formatting
            chart_type_name = str(chart.chart_type).split('.')[-1] if hasattr(chart.chart_type, '__str__') else str(chart.chart_type)
            is_line_chart = any(line_type in chart_type_name for line_type in ['LINE', 'SCATTER'])

            if is_line_chart:
                print(f"    🎨 Applying conservative formatting for line chart")
            else:
                print(f"    🎨 Applying full formatting for {chart_type_name} chart")

            # 1. Hide Y-axis (left scale) - but be careful with line charts
            if not is_line_chart:  # Only hide for column/bar charts
                try:
                    value_axis = chart.value_axis
                    value_axis.visible = False
                    print(f"    🎨 Hidden Y-axis scale")
                except Exception as e:
                    print(f"    ⚠️  Could not hide Y-axis: {e}")
            else:
                print(f"    🎨 Keeping Y-axis visible for line chart")

            # 2. Ensure legend is visible and positioned at bottom
            try:
                from pptx.enum.chart import XL_LEGEND_POSITION
                chart.has_legend = True
                legend = chart.legend
                legend.position = XL_LEGEND_POSITION.BOTTOM
                legend.include_in_layout = False
                print(f"    🎨 Legend positioned at bottom")
            except Exception as e:
                print(f"    ⚠️  Could not configure legend: {e}")

            # 3. Add data labels to all series (be more careful with line charts)
            try:
                for series in chart.series:
                    series.has_data_labels = True
                    data_labels = series.data_labels
                    data_labels.show_value = True
                    data_labels.show_category_name = False
                    data_labels.show_series_name = False
                    data_labels.show_percentage = False

                    # Position data labels (different for line vs column charts)
                    try:
                        from pptx.enum.chart import XL_DATA_LABEL_POSITION
                        if is_line_chart:
                            data_labels.position = XL_DATA_LABEL_POSITION.ABOVE  # Better for line charts
                        else:
                            data_labels.position = XL_DATA_LABEL_POSITION.CENTER  # Better for column charts
                    except:
                        pass  # Some chart types don't support positioning

                print(f"    🎨 Added data labels to {len(chart.series)} series")
            except Exception as e:
                print(f"    ⚠️  Could not add data labels: {e}")

            # 4. Ensure all series are included in legend
            try:
                for i, series in enumerate(chart.series):
                    # Make sure series name is set for legend
                    if not series.name or series.name.strip() == '':
                        series.name = f"Series {i+1}"
                print(f"    🎨 Ensured all {len(chart.series)} series have legend entries")
            except Exception as e:
                print(f"    ⚠️  Could not configure series names: {e}")

        except Exception as e:
            print(f"    ❌ Error formatting chart: {e}")

    def update_chart_data_safe(self, chart_shape, years_data, values_data, chart_title=None):
        """Ultra-safe chart update method to prevent PowerPoint corruption."""
        try:
            if not hasattr(chart_shape, 'chart'):
                print(f"    ❌ Shape is not a chart")
                return False

            chart = chart_shape.chart

            # Validate data
            if not years_data or not values_data or len(years_data) != len(values_data):
                print(f"    ❌ Invalid data: years={len(years_data)}, values={len(values_data)}")
                return False

            print(f"    🔄 Safely updating chart with {len(values_data)} data points")

            # Use the MINIMAL approach that we know works
            from pptx.chart.data import CategoryChartData

            # Create new chart data with single series only
            chart_data = CategoryChartData()
            chart_data.categories = [str(int(year)) for year in years_data]
            chart_data.add_series('Data', [float(value) for value in values_data])

            # Replace chart data (this is the risky operation)
            chart.replace_data(chart_data)

            # Update title ONLY if provided and safe
            if chart_title:
                try:
                    if chart.has_title:
                        chart.chart_title.text_frame.text = chart_title
                    else:
                        chart.has_title = True
                        chart.chart_title.text_frame.text = chart_title
                except:
                    pass  # Ignore title errors to prevent corruption

            # NO FORMATTING - this is what causes corruption
            print(f"    ✅ Chart updated safely (no formatting applied)")
            return True

        except Exception as e:
            print(f"    ❌ Error in safe chart update: {e}")
            return False

    def update_chart_data_robust(self, chart_shape, kpis_data, chart_title=None):
        """Smart chart update that pivots data based on chart type (line vs column)."""
        try:
            if not hasattr(chart_shape, 'chart'):
                print(f"    ❌ Shape is not a chart")
                return False

            chart = chart_shape.chart

            # Detect chart type
            chart_type_name = str(chart.chart_type).split('.')[-1] if hasattr(chart.chart_type, '__str__') else str(chart.chart_type)
            chart_type_value = int(chart.chart_type) if hasattr(chart.chart_type, '__int__') else 0

            # Determine if it's a line chart
            is_line_chart = (
                'LINE' in chart_type_name or
                'SCATTER' in chart_type_name or
                chart_type_value in [4, 65, 66, 67, 68, 69, 70, 71, 72, 73]  # Common line chart type values
            )

            print(f"    📊 Chart type: {chart_type_name} ({chart_type_value}) - Line chart: {is_line_chart}")

            # Validate KPI data
            if not kpis_data:
                print(f"    ❌ No KPI data provided")
                return False

            # Use years from first KPI (they should all be the same)
            reference_kpi = kpis_data[0]
            years_data = reference_kpi.get('years', [])

            if not years_data:
                print(f"    ❌ No years data available")
                return False

            # Validate all KPIs have same year structure
            valid_kpis = []
            for kpi in kpis_data:
                kpi_years = kpi.get('years', [])
                kpi_values = kpi.get('values', [])
                kpi_name = kpi.get('kpiName', 'Unknown KPI')

                if (len(kpi_years) == len(years_data) and
                    len(kpi_values) == len(years_data) and
                    kpi_years == years_data):
                    valid_kpis.append(kpi)
                    print(f"      ✅ Valid KPI: {kpi_name[:30]}...")
                else:
                    print(f"      ⚠️  Skipping KPI {kpi_name} - mismatched data")

            if not valid_kpis:
                print(f"    ❌ No valid KPIs found")
                return False

            # Create chart data with appropriate structure based on chart type
            from pptx.chart.data import CategoryChartData
            chart_data = CategoryChartData()

            if is_line_chart:
                # LINE CHART: Pivot data - categories = KPIs, series = years
                print(f"    🔄 Creating LINE chart with PIVOTED data: {len(valid_kpis)} categories, {len(years_data)} series")

                # Categories are the KPI names
                kpi_names = [self._clean_kpi_name(kpi.get('kpiName', 'Data')) for kpi in valid_kpis]
                chart_data.categories = kpi_names

                # Each year becomes a series
                for i, year in enumerate(years_data):
                    year_values = []
                    for kpi in valid_kpis:
                        kpi_values = kpi.get('values', [])
                        year_values.append(float(kpi_values[i]) if i < len(kpi_values) else 0.0)

                    chart_data.add_series(str(int(year)), year_values)
                    print(f"      📊 Added year series: {int(year)}")

            else:
                # COLUMN/BAR CHART: Normal structure - categories = years, series = KPIs
                print(f"    🔄 Creating COLUMN chart with NORMAL data: {len(years_data)} categories, {len(valid_kpis)} series")

                # Categories are the years
                chart_data.categories = [str(int(year)) for year in years_data]

                # Each KPI becomes a series
                for kpi in valid_kpis:
                    kpi_name = kpi.get('kpiName', 'Data Series')
                    kpi_values = kpi.get('values', [])

                    clean_name = self._clean_kpi_name(kpi_name)
                    chart_data.add_series(clean_name, [float(value) for value in kpi_values])
                    print(f"      📊 Added KPI series: {clean_name}")

            # Replace the chart data
            chart.replace_data(chart_data)

            # Update chart title if provided
            if chart_title:
                try:
                    if chart.has_title:
                        chart.chart_title.text_frame.text = chart_title
                except Exception as e:
                    print(f"      ⚠️  Could not update title: {e}")

            print(f"    ✅ Chart updated successfully with {'PIVOTED' if is_line_chart else 'NORMAL'} data structure")
            return True

        except Exception as e:
            print(f"    ❌ Error in chart update: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _clean_kpi_name(self, kpi_name):
        """Clean KPI name for better display."""
        clean_name = (kpi_name
                     .replace(' revenue by segment', '')
                     .replace(' Market', '')
                     .replace(' average revenue per capita', '')
                     .replace(' volume by segment', '')
                     .strip())

        if not clean_name or len(clean_name) < 3:
            clean_name = kpi_name

        return clean_name

    def extract_chart_data_from_document(self, document):
        """Extract chart data from MongoDB document."""
        try:
            chart_data_list = document.get('chartData', [])

            if not chart_data_list:
                print("  ⚠️ No chart data found in document")
                return None

            # Get the first chart data (you can modify this to select specific charts)
            first_chart = chart_data_list[0]
            chart_info = {
                'chart_index': first_chart.get('chartIndex'),
                'chapter_name': first_chart.get('chapterName'),
                'chart_name': first_chart.get('chartName'),
                'chart_geo': first_chart.get('chartGeoName'),
                'years': first_chart['data']['Years'],
                'values': first_chart['data']['Values']
            }

            print(f"  ✓ Extracted chart data: {chart_info['chart_name']} ({len(chart_info['years'])} data points)")
            return chart_info

        except Exception as e:
            print(f"  ❌ Error extracting chart data: {e}")
            return None

    def list_all_object_names(self, presentation):
        """List all object names in the presentation for debugging."""
        print("\n📋 All objects in presentation:")
        for slide_num, slide in enumerate(presentation.slides, 1):
            print(f"  Slide {slide_num}:")
            for shape in slide.shapes:
                if hasattr(shape, 'name'):
                    shape_type = "Text" if hasattr(shape, 'text') else "Other"
                    print(f"    - '{shape.name}' ({shape_type})")
        print()

    def initialize_s3_client(self):
        """Initialize S3 client."""
        try:
            self.s3_client = boto3.client('s3')
            print("✅ S3 client initialized successfully!")
            return True
        except Exception as e:
            print(f"❌ Error initializing S3 client: {e}")
            return False

    def upload_to_s3(self, local_file_path, s3_key=None):
        """Upload file to S3 bucket."""
        try:
            if not self.s3_client:
                if not self.initialize_s3_client():
                    return None

            # Generate S3 key if not provided
            if not s3_key:
                filename = os.path.basename(local_file_path)
                timestamp = datetime.now().strftime("%Y/%m/%d")
                s3_key = f"{filename}"

            print(f"Uploading to S3: s3://{self.s3_bucket}/{s3_key}")

            # Upload file
            self.s3_client.upload_file(
                local_file_path,
                self.s3_bucket,
                s3_key,
                ExtraArgs={'ContentType': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'}
            )

            # Generate download URL
            s3_url = f"https://{self.s3_bucket}.s3.eu-central-1.amazonaws.com/{s3_key}"

            print(f"✅ File uploaded successfully!")
            print(f"   S3 URL: {s3_url}")
            print(f"   S3 Key: {s3_key}")

            return {
                'bucket': self.s3_bucket,
                'key': s3_key,
                'url': s3_url
            }

        except ClientError as e:
            print(f"❌ S3 upload failed: {e}")
            return None
        except Exception as e:
            print(f"❌ Error uploading to S3: {e}")
            return None

    def generate_download_url(self, s3_key, expiration=3600):
        """Generate a presigned URL for downloading the file from S3."""
        try:
            if not self.s3_client:
                if not self.initialize_s3_client():
                    return None

            # Generate presigned URL
            download_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.s3_bucket, 'Key': s3_key},
                ExpiresIn=expiration
            )

            print(f"✅ Generated download URL (expires in {expiration//3600} hours)")
            return download_url

        except Exception as e:
            print(f"❌ Error generating download URL: {e}")
            return None

    def update_powerpoint_with_priority_charts(self, template_path, chart_data_list, output_path=None):
        """Update PowerPoint template with priority chart data."""
        try:
            # Load the template
            print(f"📄 Loading template: {template_path}")
            presentation = Presentation(template_path)

            # Process each priority chart
            for chart_info in chart_data_list:
                chart_key = chart_info['chart_key']
                chart_object = chart_info['chart_object']
                data = chart_info['data']
                document = chart_info['document']

                print(f"\n📊 Updating {chart_object} with {chart_key} data")

                # Find ALL chart objects with this name in PowerPoint
                chart_updated = False
                charts_found = 0
                for slide_num, slide in enumerate(presentation.slides, 1):
                    for shape in slide.shapes:
                        if hasattr(shape, 'name') and shape.name == chart_object:
                            charts_found += 1
                            print(f"   🎯 Found chart object: {chart_object} on slide {slide_num}")

                            # Update the chart with multi-series data
                            if hasattr(shape, 'chart'):
                                # Extract all KPIs for multi-series chart updating
                                kpis = data.get('kpis', [])
                                if kpis:
                                    print(f"   🔄 Updating with {len(kpis)} series: {[kpi.get('kpiName', 'Unknown')[:20] + '...' for kpi in kpis]}")

                                    # Use multi-series approach with careful error handling
                                    success = self.update_chart_data_robust(
                                        shape,
                                        kpis,
                                        f"{chart_key.title()} Chart"
                                    )

                                    if success:
                                        print(f"   ✅ Successfully updated {chart_object} with {len(kpis)} series")
                                        chart_updated = True
                                        # DON'T break - continue to find other charts with same name
                                    else:
                                        print(f"   ❌ Failed to update chart data for {chart_object}")
                                else:
                                    print(f"   ❌ No KPIs found for {chart_object}")
                            else:
                                print(f"   ⚠️  Shape {chart_object} is not a chart")
                    # Continue to next slide - don't break after finding charts

                if chart_updated:
                    print(f"   📊 Total charts updated for {chart_object}: {charts_found}")
                else:
                    print(f"   ❌ Chart object {chart_object} not found in presentation")

            # Generate output filename based on document data
            if not output_path:
                # Get outlook name and page name from first chart document
                first_chart = chart_data_list[0] if chart_data_list else None
                if first_chart:
                    chart_data = first_chart['data'].get('chartData', {})
                    outlook_name = chart_data.get('outlookName', 'Unknown')
                    page_name = chart_data.get('pageName', 'Unknown')
                    output_path = f"{outlook_name} - {page_name}.pptx"
                else:
                    output_path = "Market Insights - Unknown Page.pptx"

            # Save the presentation
            presentation.save(output_path)
            print(f"✅ PowerPoint saved: {output_path}")

            return output_path

        except Exception as e:
            print(f"❌ Error updating PowerPoint with priority charts: {e}")
            return None

    def update_chart_with_kpi_data(self, chart_shape, worldwide_data):
        """Update a PowerPoint chart with KPI data from Worldwide geography."""
        try:
            kpis = worldwide_data.get('kpis', [])
            if not kpis:
                print("   ❌ No KPI data available")
                return False

            # Access chart and chart_data through the shape
            if not hasattr(chart_shape, 'chart'):
                print("   ❌ Shape does not contain a chart")
                return False

            chart = chart_shape.chart
            chart_data = chart.chart_data

            # Use first KPI for demonstration (you can modify this logic)
            primary_kpi = kpis[0]
            years = primary_kpi.get('years', [])
            values = primary_kpi.get('values', [])

            if not years or not values:
                print("   ❌ No valid years/values data")
                return False

            print(f"   📅 Adding {len(years)} data points: {years[0]}-{years[-1]}")
            print(f"   📊 KPI: {primary_kpi.get('kpiName', 'Unknown')}")

            # Replace chart data completely
            from pptx.chart.data import CategoryChartData

            # Create new chart data
            new_chart_data = CategoryChartData()

            # Add categories (years)
            new_chart_data.categories = [str(int(year)) for year in years]

            # Add series with values
            kpi_name = primary_kpi.get('kpiName', 'Data')
            new_chart_data.add_series(kpi_name, [float(value) for value in values])

            # Replace the chart data
            chart.replace_data(new_chart_data)

            print(f"   ✅ Chart updated with {kpi_name} data")
            return True

        except Exception as e:
            print(f"   ❌ Error updating chart data: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_powerpoint_template(self, template_path, extracted_data, output_path=None, document=None):
        """Update PowerPoint template with extracted data."""
        try:
            # Load the template
            print(f"Loading template: {template_path}")
            presentation = Presentation(template_path)

            # List all objects for debugging (optional)
            self.list_all_object_names(presentation)

            # Define object name mappings (these should match your PowerPoint object names)
            object_mappings = {
                'market': extracted_data['report_name'],
                'country': extracted_data['country'],
                'source': extracted_data['source'],
                # Add more mappings as needed based on your template object names
            }

            total_replacements = 0

            # Perform replacements by object name
            print("Updating objects by name...")
            for object_name, replacement_text in object_mappings.items():
                replacements = self.find_and_update_by_object_name(presentation, object_name, replacement_text)
                total_replacements += replacements

                if replacements == 0:
                    print(f"  ⚠️ Object '{object_name}' not found in template")

            # Update charts with data
            print("\nUpdating charts with data...")
            chart_data = self.extract_chart_data_from_document(document)

            if chart_data:
                # Update the specific chart
                chart_updates = self.update_chart_data(
                    presentation,
                    'chart_global_ind_1',  # Target chart name
                    chart_data['years'],   # X-axis labels (years)
                    chart_data['values'],  # Y-axis values
                    f"{chart_data['chart_name']} - {chart_data['chart_geo']}"  # Chart title
                )
                total_replacements += chart_updates
            else:
                print("  ⚠️ No chart data available for updates")
            
            # Generate output filename if not provided
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"market_insights_report_{extracted_data['report_id']}_{timestamp}.pptx"
            
            # Save the updated presentation
            presentation.save(output_path)
            
            print(f"✅ PowerPoint updated successfully!")
            print(f"   - Total replacements made: {total_replacements}")
            print(f"   - Output file: {output_path}")
            
            return output_path
            
        except FileNotFoundError:
            print(f"❌ Template file not found: {template_path}")
            return None
        except Exception as e:
            print(f"❌ Error updating PowerPoint: {e}")
            return None
    
    def process_document_to_powerpoint(self, template_path="template.pptx", page_name="Household Appliances", output_path=None, upload_to_s3=True):
        """Complete workflow: MongoDB → PowerPoint → S3 using chart priority system."""
        print("🚀 Starting MongoDB to PowerPoint processing with chart priority system...")

        # Step 1: Connect to MongoDB
        if not self.connect_to_mongodb():
            return False

        # Step 2: Get priority charts for the specified page
        priority_charts = self.get_priority_charts_for_page(page_name, max_charts=3)
        if not priority_charts:
            print(f"❌ No priority charts found for page: {page_name}")
            return False

        # Step 3: Extract Worldwide data for each priority chart
        chart_data_for_powerpoint = []
        for chart_info in priority_charts:
            chart_key = chart_info['chart_key']
            document = chart_info['document']
            chart_object = chart_info['chart_object']

            print(f"\n📊 Processing {chart_key} for {chart_object}")

            # Extract Worldwide data with year filtering
            worldwide_data = self.extract_worldwide_data(document)
            if worldwide_data:
                chart_data_for_powerpoint.append({
                    'chart_key': chart_key,
                    'chart_object': chart_object,
                    'data': worldwide_data,
                    'document': document
                })
                print(f"✅ Data extracted for {chart_object}")
            else:
                print(f"❌ Failed to extract data for {chart_key}")

        if not chart_data_for_powerpoint:
            print("❌ No valid chart data extracted")
            return False

        # Step 4: Update PowerPoint with priority chart data
        output_file = self.update_powerpoint_with_priority_charts(template_path, chart_data_for_powerpoint, output_path)

        if not output_file:
            return False

        # Step 5: Upload to S3 (optional)
        s3_info = None
        if upload_to_s3:
            print("\n📤 Uploading to S3...")
            s3_info = self.upload_to_s3(output_file)

            if s3_info:
                # Generate presigned URL for secure download
                presigned_url = self.generate_download_url(s3_info['key'], expiration=86400)  # 24 hours

                print(f"\n🎉 Complete workflow finished successfully!")
                print(f"   Local file: {output_file}")
                print(f"   S3 bucket: {s3_info['bucket']}")
                print(f"   S3 key: {s3_info['key']}")

                if presigned_url:
                    print(f"   📥 SECURE DOWNLOAD URL (24h): {presigned_url}")
                    s3_info['download_url'] = presigned_url
                else:
                    print(f"   ⚠️  Could not generate download URL")
                    # Fallback to direct URL (may not work if bucket is private)
                    fallback_url = f"https://{self.s3_bucket}.s3.eu-central-1.amazonaws.com/{s3_info['key']}"
                    print(f"   📥 DIRECT URL (may require permissions): {fallback_url}")
                    s3_info['download_url'] = fallback_url

                # Optionally remove local file after successful upload
                try:
                    os.remove(output_file)
                    print(f"   Local file cleaned up: {output_file}")
                except:
                    pass

                return s3_info
            else:
                print(f"⚠️ S3 upload failed, but local file available: {output_file}")
                return {'local_file': output_file}
        else:
            print(f"🎉 Process completed successfully! Output: {output_file}")
            return {'local_file': output_file}
    
    def close_connection(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            print("MongoDB connection closed.")


def main():
    """Main execution function."""
    processor = MongoToPowerPointProcessor()

    try:
        # Process Household Appliances page with chart priority system
        success = processor.process_document_to_powerpoint(
            template_path="template.pptx",
            page_name="Household Appliances",  # Process this specific page
            output_path=None  # Auto-generate filename
        )
        
        if success:
            print("\n✅ All done! Your PowerPoint presentation has been created.")
        else:
            print("\n❌ Process failed. Please check the error messages above.")
            
    finally:
        processor.close_connection()


if __name__ == "__main__":
    main()
