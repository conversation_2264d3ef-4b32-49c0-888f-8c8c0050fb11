#!/usr/bin/env python3
"""
Professional MongoDB to PowerPoint automation script.
Extracts data from MongoDB and populates PowerPoint templates.
"""

import os
from datetime import datetime
from pymongo import MongoClient
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import boto3
from botocore.exceptions import ClientError
from powerpoint_settings import chart_priority
from powerpoint_settings import chart_priority


class MongoToPowerPointProcessor:
    """Professional class for MongoDB to PowerPoint data processing."""

    def __init__(self, mongo_host="*************", mongo_port=27017,
                 mongo_user="admin", mongo_pass="statista123",
                 s3_bucket="market-insights-reports-s3"):
        """Initialize MongoDB connection and S3 settings."""
        self.mongo_host = mongo_host
        self.mongo_port = mongo_port
        self.mongo_user = mongo_user
        self.mongo_pass = mongo_pass
        self.s3_bucket = s3_bucket
        self.client = None
        self.db = None
        self.collection = None
        self.s3_client = None
        
    def connect_to_mongodb(self):
        """Establish connection to MongoDB."""
        try:
            self.client = MongoClient(
                host=self.mongo_host,
                port=self.mongo_port,
                username=self.mongo_user,
                password=self.mongo_pass,
                authSource="admin",
                serverSelectionTimeoutMS=5000
            )
            
            # Test connection
            self.client.admin.command('ping')
            print("✅ MongoDB connection successful!")
            
            # Connect to database and collection
            self.db = self.client['statista_insights']
            self.collection = self.db['market_insights']
            
            return True
            
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False
    
    def get_document_by_id(self, doc_id=None):
        """Get a specific document or the first one if no ID provided."""
        try:
            if doc_id:
                document = self.collection.find_one({"unifiedContentMetaData.id": doc_id})
            else:
                document = self.collection.find_one()
            
            if document:
                print(f"✅ Document retrieved: {document['unifiedContentMetaData']['metaData']['reportData']['reportName']}")
                return document
            else:
                print("❌ No document found")
                return None
                
        except Exception as e:
            print(f"❌ Error retrieving document: {e}")
            return None

    def get_priority_charts_for_page(self, page_name="Household Appliances", max_charts=3):
        """Get the first 3 existing charts from chart_priority for a specific page."""
        try:
            print(f"🔍 Finding priority charts for page: {page_name}")

            # Get all documents for the specified page
            documents = list(self.collection.find({
                "unifiedContentMetaData.metaData.chartData.pageName": page_name
            }))

            if not documents:
                print(f"❌ No documents found for page: {page_name}")
                return []

            print(f"📊 Found {len(documents)} documents for {page_name}")

            # Extract available chart keys
            available_charts = {}
            for doc in documents:
                chart_data = doc.get("unifiedContentMetaData", {}).get("metaData", {}).get("chartData", {})
                chart_key = chart_data.get("chartKey")
                if chart_key:
                    available_charts[chart_key] = doc
                    print(f"   📈 Available chart: {chart_key}")

            # Find first 3 charts from priority list that exist
            selected_charts = []
            for priority_chart in chart_priority:
                if priority_chart in available_charts and len(selected_charts) < max_charts:
                    selected_charts.append({
                        'chart_key': priority_chart,
                        'document': available_charts[priority_chart],
                        'chart_object': f"chart_global_ind_{len(selected_charts) + 1}"
                    })
                    print(f"✅ Selected priority chart {len(selected_charts)}: {priority_chart} → {selected_charts[-1]['chart_object']}")

            if len(selected_charts) < max_charts:
                print(f"⚠️  Only found {len(selected_charts)} priority charts out of {max_charts} requested")

            return selected_charts

        except Exception as e:
            print(f"❌ Error finding priority charts: {e}")
            return []

    def extract_worldwide_data(self, document, min_year=None, max_year=None):
        """Extract Worldwide geography data from a document, filtered by year range."""
        try:
            chart_geos = document.get("chartGeos", [])

            # Find Worldwide geography
            worldwide_data = None
            for geo in chart_geos:
                if geo.get("chartGeo") == "Worldwide":
                    worldwide_data = geo
                    break

            if not worldwide_data:
                print("❌ No Worldwide data found in document")
                return None

            # Get chart metadata for year filtering
            chart_data = document.get("unifiedContentMetaData", {}).get("metaData", {}).get("chartData", {})
            if min_year is None:
                min_year = chart_data.get("minYear")
            if max_year is None:
                max_year = chart_data.get("maxYear")

            print(f"📅 Filtering data for years: {min_year} - {max_year}")

            # Process KPIs and filter by year range
            filtered_kpis = []
            for kpi in worldwide_data.get("kpis", []):
                years = kpi.get("years", [])
                values = kpi.get("values", [])

                if not years or not values or len(years) != len(values):
                    print(f"⚠️  Skipping KPI {kpi.get('kpiName', 'Unknown')} - invalid data")
                    continue

                # Filter by year range
                filtered_years = []
                filtered_values = []

                for year, value in zip(years, values):
                    if (min_year is None or year >= min_year) and (max_year is None or year <= max_year):
                        filtered_years.append(year)
                        filtered_values.append(value)

                if filtered_years:
                    filtered_kpis.append({
                        'kpiName': kpi.get('kpiName'),
                        'years': filtered_years,
                        'values': filtered_values
                    })
                    print(f"   📈 {kpi.get('kpiName')}: {len(filtered_years)} data points ({filtered_years[0]}-{filtered_years[-1]})")

            return {
                'chartGeo': 'Worldwide',
                'kpis': filtered_kpis,
                'chartData': chart_data
            }

        except Exception as e:
            print(f"❌ Error extracting Worldwide data: {e}")
            return None

    def extract_data_from_document(self, document):
        """Extract relevant data from MongoDB document."""
        try:
            metadata = document['unifiedContentMetaData']['metaData']['reportData']
            
            extracted_data = {
                'report_id': document['unifiedContentMetaData']['id'],
                'report_name': metadata['reportName'],
                'report_definition': metadata.get('reportDefinition', ''),
                'country': metadata.get('country', 'Unknown'),
                'iso': metadata.get('iso', 'N/A'),
                'source': metadata.get('source', 'Market Insights'),
                'chart_data': document.get('chartData', [])
            }
            
            print(f"✅ Data extracted for report: {extracted_data['report_name']}")
            return extracted_data
            
        except Exception as e:
            print(f"❌ Error extracting data: {e}")
            return None
    
    def find_and_update_by_object_name(self, presentation, object_name, replacement_text):
        """Find and update PowerPoint objects by their name (from Selection Pane)."""
        replacements_made = 0

        for slide in presentation.slides:
            for shape in slide.shapes:
                # Check if this shape has the target object name
                if hasattr(shape, 'name') and shape.name == object_name:
                    # Update the text content of this specific object
                    if hasattr(shape, 'text'):
                        shape.text = replacement_text
                        replacements_made += 1
                        print(f"  ✓ Updated object '{object_name}' with '{replacement_text}'")
                    elif hasattr(shape, 'text_frame'):
                        # Clear existing text and add new text
                        shape.text_frame.clear()
                        p = shape.text_frame.paragraphs[0]
                        p.text = replacement_text
                        replacements_made += 1
                        print(f"  ✓ Updated object '{object_name}' text frame with '{replacement_text}'")
                    else:
                        print(f"  ⚠️ Found object '{object_name}' but it doesn't support text")

        return replacements_made

    def update_chart_data(self, presentation, chart_name, years_data, values_data, chart_title=None):
        """Update PowerPoint chart with new data using correct python-pptx approach."""
        charts_updated = 0

        for slide in presentation.slides:
            for shape in slide.shapes:
                # Check if this shape is the target chart
                if hasattr(shape, 'name') and shape.name == chart_name:
                    # Check if it's a chart
                    if hasattr(shape, 'chart'):
                        try:
                            from pptx.chart.data import CategoryChartData

                            chart = shape.chart

                            # Create new chart data with proper structure
                            chart_data = CategoryChartData()

                            # Set categories (X-axis labels) - convert years to strings
                            chart_data.categories = [str(year) for year in years_data]

                            # Add series with values (Y-axis data)
                            chart_data.add_series('Health Indicator', values_data)

                            # Replace the chart data
                            chart.replace_data(chart_data)

                            # Update chart title if provided
                            if chart_title:
                                if chart.has_title:
                                    chart.chart_title.text_frame.text = chart_title
                                else:
                                    chart.has_title = True
                                    chart.chart_title.text_frame.text = chart_title

                            charts_updated += 1
                            print(f"  ✓ Updated chart '{chart_name}' with {len(values_data)} data points")
                            print(f"    Years: {years_data[0]} - {years_data[-1]}")
                            print(f"    Values range: {min(values_data):.2f} - {max(values_data):.2f}")

                        except Exception as e:
                            print(f"  ❌ Error updating chart '{chart_name}': {e}")
                            # Provide diagnostic information
                            try:
                                chart = shape.chart
                                print(f"    Chart type: {chart.chart_type}")
                                print(f"    Chart has {len(chart.series)} series")
                                if len(chart.series) > 0:
                                    series = chart.series[0]
                                    print(f"    First series name: {series.name}")
                                    print(f"    First series has {len(series.values)} values")
                            except Exception as e2:
                                print(f"    Additional error getting chart info: {e2}")
                    else:
                        print(f"  ⚠️ Object '{chart_name}' found but is not a chart")

        return charts_updated

    def extract_chart_data_from_document(self, document):
        """Extract chart data from MongoDB document."""
        try:
            chart_data_list = document.get('chartData', [])

            if not chart_data_list:
                print("  ⚠️ No chart data found in document")
                return None

            # Get the first chart data (you can modify this to select specific charts)
            first_chart = chart_data_list[0]
            chart_info = {
                'chart_index': first_chart.get('chartIndex'),
                'chapter_name': first_chart.get('chapterName'),
                'chart_name': first_chart.get('chartName'),
                'chart_geo': first_chart.get('chartGeoName'),
                'years': first_chart['data']['Years'],
                'values': first_chart['data']['Values']
            }

            print(f"  ✓ Extracted chart data: {chart_info['chart_name']} ({len(chart_info['years'])} data points)")
            return chart_info

        except Exception as e:
            print(f"  ❌ Error extracting chart data: {e}")
            return None

    def list_all_object_names(self, presentation):
        """List all object names in the presentation for debugging."""
        print("\n📋 All objects in presentation:")
        for slide_num, slide in enumerate(presentation.slides, 1):
            print(f"  Slide {slide_num}:")
            for shape in slide.shapes:
                if hasattr(shape, 'name'):
                    shape_type = "Text" if hasattr(shape, 'text') else "Other"
                    print(f"    - '{shape.name}' ({shape_type})")
        print()

    def initialize_s3_client(self):
        """Initialize S3 client."""
        try:
            self.s3_client = boto3.client('s3')
            print("✅ S3 client initialized successfully!")
            return True
        except Exception as e:
            print(f"❌ Error initializing S3 client: {e}")
            return False

    def upload_to_s3(self, local_file_path, s3_key=None):
        """Upload file to S3 bucket."""
        try:
            if not self.s3_client:
                if not self.initialize_s3_client():
                    return None

            # Generate S3 key if not provided
            if not s3_key:
                filename = os.path.basename(local_file_path)
                timestamp = datetime.now().strftime("%Y/%m/%d")
                s3_key = f"{filename}"

            print(f"Uploading to S3: s3://{self.s3_bucket}/{s3_key}")

            # Upload file
            self.s3_client.upload_file(
                local_file_path,
                self.s3_bucket,
                s3_key,
                ExtraArgs={'ContentType': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'}
            )

            # Generate download URL
            s3_url = f"https://{self.s3_bucket}.s3.eu-central-1.amazonaws.com/{s3_key}"

            print(f"✅ File uploaded successfully!")
            print(f"   S3 URL: {s3_url}")
            print(f"   S3 Key: {s3_key}")

            return {
                'bucket': self.s3_bucket,
                'key': s3_key,
                'url': s3_url
            }

        except ClientError as e:
            print(f"❌ S3 upload failed: {e}")
            return None
        except Exception as e:
            print(f"❌ Error uploading to S3: {e}")
            return None

    def generate_download_url(self, s3_key, expiration=3600):
        """Generate a presigned URL for downloading the file from S3."""
        try:
            if not self.s3_client:
                if not self.initialize_s3_client():
                    return None

            # Generate presigned URL
            download_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.s3_bucket, 'Key': s3_key},
                ExpiresIn=expiration
            )

            print(f"✅ Generated download URL (expires in {expiration//3600} hours)")
            return download_url

        except Exception as e:
            print(f"❌ Error generating download URL: {e}")
            return None

    def update_powerpoint_with_priority_charts(self, template_path, chart_data_list, output_path=None):
        """Update PowerPoint template with priority chart data."""
        try:
            # Load the template
            print(f"📄 Loading template: {template_path}")
            presentation = Presentation(template_path)

            # Process each priority chart
            for chart_info in chart_data_list:
                chart_key = chart_info['chart_key']
                chart_object = chart_info['chart_object']
                data = chart_info['data']
                document = chart_info['document']

                print(f"\n📊 Updating {chart_object} with {chart_key} data")

                # Find the chart object in PowerPoint
                chart_updated = False
                for slide in presentation.slides:
                    for shape in slide.shapes:
                        if hasattr(shape, 'name') and shape.name == chart_object:
                            print(f"   🎯 Found chart object: {chart_object}")

                            # Update the chart with KPI data
                            if hasattr(shape, 'chart') and shape.chart:
                                success = self.update_chart_with_kpi_data(shape.chart, data)
                                if success:
                                    print(f"   ✅ Successfully updated {chart_object}")
                                    chart_updated = True
                                else:
                                    print(f"   ❌ Failed to update chart data for {chart_object}")
                            else:
                                print(f"   ⚠️  Shape {chart_object} is not a chart")
                            break
                    if chart_updated:
                        break

                if not chart_updated:
                    print(f"   ❌ Chart object {chart_object} not found in presentation")

            # Generate output filename based on document data
            if not output_path:
                # Get outlook name and page name from first chart document
                first_chart = chart_data_list[0] if chart_data_list else None
                if first_chart:
                    chart_data = first_chart['data'].get('chartData', {})
                    outlook_name = chart_data.get('outlookName', 'Unknown')
                    page_name = chart_data.get('pageName', 'Unknown')
                    output_path = f"{outlook_name} - {page_name}.pptx"
                else:
                    output_path = "Market Insights - Unknown Page.pptx"

            # Save the presentation
            presentation.save(output_path)
            print(f"✅ PowerPoint saved: {output_path}")

            return output_path

        except Exception as e:
            print(f"❌ Error updating PowerPoint with priority charts: {e}")
            return None

    def update_chart_with_kpi_data(self, chart, worldwide_data):
        """Update a PowerPoint chart with KPI data from Worldwide geography."""
        try:
            kpis = worldwide_data.get('kpis', [])
            if not kpis:
                print("   ❌ No KPI data available")
                return False

            # Get chart data
            chart_data = chart.chart_data

            # Clear existing data
            chart_data.categories.clear()
            for series in chart_data.series:
                series.values.clear()

            # Use first KPI for demonstration (you can modify this logic)
            primary_kpi = kpis[0]
            years = primary_kpi.get('years', [])
            values = primary_kpi.get('values', [])

            if not years or not values:
                print("   ❌ No valid years/values data")
                return False

            print(f"   📅 Adding {len(years)} data points: {years[0]}-{years[-1]}")

            # Add categories (years)
            for year in years:
                chart_data.categories.add_category(str(year))

            # Add or update series
            if len(chart_data.series) == 0:
                # Add new series
                series = chart_data.series.add_series(primary_kpi.get('kpiName', 'Data'))
            else:
                # Use existing series
                series = chart_data.series[0]
                series.name = primary_kpi.get('kpiName', 'Data')

            # Add values
            for value in values:
                series.values.add_value(float(value))

            print(f"   ✅ Chart updated with {primary_kpi.get('kpiName')} data")
            return True

        except Exception as e:
            print(f"   ❌ Error updating chart data: {e}")
            return False

    def update_powerpoint_template(self, template_path, extracted_data, output_path=None, document=None):
        """Update PowerPoint template with extracted data."""
        try:
            # Load the template
            print(f"Loading template: {template_path}")
            presentation = Presentation(template_path)

            # List all objects for debugging (optional)
            self.list_all_object_names(presentation)

            # Define object name mappings (these should match your PowerPoint object names)
            object_mappings = {
                'market': extracted_data['report_name'],
                'country': extracted_data['country'],
                'source': extracted_data['source'],
                # Add more mappings as needed based on your template object names
            }

            total_replacements = 0

            # Perform replacements by object name
            print("Updating objects by name...")
            for object_name, replacement_text in object_mappings.items():
                replacements = self.find_and_update_by_object_name(presentation, object_name, replacement_text)
                total_replacements += replacements

                if replacements == 0:
                    print(f"  ⚠️ Object '{object_name}' not found in template")

            # Update charts with data
            print("\nUpdating charts with data...")
            chart_data = self.extract_chart_data_from_document(document)

            if chart_data:
                # Update the specific chart
                chart_updates = self.update_chart_data(
                    presentation,
                    'chart_global_ind_1',  # Target chart name
                    chart_data['years'],   # X-axis labels (years)
                    chart_data['values'],  # Y-axis values
                    f"{chart_data['chart_name']} - {chart_data['chart_geo']}"  # Chart title
                )
                total_replacements += chart_updates
            else:
                print("  ⚠️ No chart data available for updates")
            
            # Generate output filename if not provided
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"market_insights_report_{extracted_data['report_id']}_{timestamp}.pptx"
            
            # Save the updated presentation
            presentation.save(output_path)
            
            print(f"✅ PowerPoint updated successfully!")
            print(f"   - Total replacements made: {total_replacements}")
            print(f"   - Output file: {output_path}")
            
            return output_path
            
        except FileNotFoundError:
            print(f"❌ Template file not found: {template_path}")
            return None
        except Exception as e:
            print(f"❌ Error updating PowerPoint: {e}")
            return None
    
    def process_document_to_powerpoint(self, template_path="template.pptx", page_name="Household Appliances", output_path=None, upload_to_s3=True):
        """Complete workflow: MongoDB → PowerPoint → S3 using chart priority system."""
        print("🚀 Starting MongoDB to PowerPoint processing with chart priority system...")

        # Step 1: Connect to MongoDB
        if not self.connect_to_mongodb():
            return False

        # Step 2: Get priority charts for the specified page
        priority_charts = self.get_priority_charts_for_page(page_name, max_charts=3)
        if not priority_charts:
            print(f"❌ No priority charts found for page: {page_name}")
            return False

        # Step 3: Extract Worldwide data for each priority chart
        chart_data_for_powerpoint = []
        for chart_info in priority_charts:
            chart_key = chart_info['chart_key']
            document = chart_info['document']
            chart_object = chart_info['chart_object']

            print(f"\n📊 Processing {chart_key} for {chart_object}")

            # Extract Worldwide data with year filtering
            worldwide_data = self.extract_worldwide_data(document)
            if worldwide_data:
                chart_data_for_powerpoint.append({
                    'chart_key': chart_key,
                    'chart_object': chart_object,
                    'data': worldwide_data,
                    'document': document
                })
                print(f"✅ Data extracted for {chart_object}")
            else:
                print(f"❌ Failed to extract data for {chart_key}")

        if not chart_data_for_powerpoint:
            print("❌ No valid chart data extracted")
            return False

        # Step 4: Update PowerPoint with priority chart data
        output_file = self.update_powerpoint_with_priority_charts(template_path, chart_data_for_powerpoint, output_path)

        if not output_file:
            return False

        # Step 5: Upload to S3 (optional)
        s3_info = None
        if upload_to_s3:
            print("\n📤 Uploading to S3...")
            s3_info = self.upload_to_s3(output_file)

            if s3_info:
                # Use simple public URL (bucket is now public)
                public_url = f"https://{self.s3_bucket}.s3.eu-central-1.amazonaws.com/{s3_info['key']}"

                print(f"\n🎉 Complete workflow finished successfully!")
                print(f"   Local file: {output_file}")
                print(f"   S3 bucket: {s3_info['bucket']}")
                print(f"   S3 key: {s3_info['key']}")
                print(f"   📥 PUBLIC DOWNLOAD URL: {public_url}")

                # Add public URL to return info
                s3_info['public_url'] = public_url

                # Optionally remove local file after successful upload
                try:
                    os.remove(output_file)
                    print(f"   Local file cleaned up: {output_file}")
                except:
                    pass

                return s3_info
            else:
                print(f"⚠️ S3 upload failed, but local file available: {output_file}")
                return {'local_file': output_file}
        else:
            print(f"🎉 Process completed successfully! Output: {output_file}")
            return {'local_file': output_file}
    
    def close_connection(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            print("MongoDB connection closed.")


def main():
    """Main execution function."""
    processor = MongoToPowerPointProcessor()

    try:
        # Process Household Appliances page with chart priority system
        success = processor.process_document_to_powerpoint(
            template_path="template.pptx",
            page_name="Household Appliances",  # Process this specific page
            output_path=None  # Auto-generate filename
        )
        
        if success:
            print("\n✅ All done! Your PowerPoint presentation has been created.")
        else:
            print("\n❌ Process failed. Please check the error messages above.")
            
    finally:
        processor.close_connection()


if __name__ == "__main__":
    main()
