#!/usr/bin/env python3
"""
Professional MongoDB to PowerPoint automation script.
Extracts data from MongoDB and populates PowerPoint templates.
"""

import os
from datetime import datetime
from pymongo import MongoClient
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import boto3
from botocore.exceptions import ClientError
from powerpoint_settings import chart_priority


class MongoToPowerPointProcessor:
    """Professional class for MongoDB to PowerPoint data processing."""

    def __init__(self, mongo_host="*************", mongo_port=27017,
                 mongo_user="admin", mongo_pass="statista123",
                 s3_bucket="market-insights-reports-s3"):
        """Initialize MongoDB connection and S3 settings."""
        self.mongo_host = mongo_host
        self.mongo_port = mongo_port
        self.mongo_user = mongo_user
        self.mongo_pass = mongo_pass
        self.s3_bucket = s3_bucket
        self.client = None
        self.db = None
        self.collection = None
        self.s3_client = None
        
    def connect_to_mongodb(self):
        """Establish connection to MongoDB."""
        try:
            self.client = MongoClient(
                host=self.mongo_host,
                port=self.mongo_port,
                username=self.mongo_user,
                password=self.mongo_pass,
                authSource="admin",
                serverSelectionTimeoutMS=5000
            )
            
            # Test connection
            self.client.admin.command('ping')
            print("✅ MongoDB connection successful!")
            
            # Connect to database and collection
            self.db = self.client['statista_insights']
            self.collection = self.db['market_insights']
            
            return True
            
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False
    
    def get_document_by_id(self, doc_id=None):
        """Get a specific document or the first one if no ID provided."""
        try:
            if doc_id:
                document = self.collection.find_one({"unifiedContentMetaData.id": doc_id})
            else:
                document = self.collection.find_one()
            
            if document:
                print(f"✅ Document retrieved: {document['unifiedContentMetaData']['metaData']['reportData']['reportName']}")
                return document
            else:
                print("❌ No document found")
                return None
                
        except Exception as e:
            print(f"❌ Error retrieving document: {e}")
            return None
    
    def extract_data_from_document(self, document):
        """Extract relevant data from MongoDB document."""
        try:
            metadata = document['unifiedContentMetaData']['metaData']['reportData']
            
            extracted_data = {
                'report_id': document['unifiedContentMetaData']['id'],
                'report_name': metadata['reportName'],
                'report_definition': metadata.get('reportDefinition', ''),
                'country': metadata.get('country', 'Unknown'),
                'iso': metadata.get('iso', 'N/A'),
                'source': metadata.get('source', 'Market Insights'),
                'chart_data': document.get('chartData', [])
            }
            
            print(f"✅ Data extracted for report: {extracted_data['report_name']}")
            return extracted_data
            
        except Exception as e:
            print(f"❌ Error extracting data: {e}")
            return None
    
    def find_and_update_by_object_name(self, presentation, object_name, replacement_text):
        """Find and update PowerPoint objects by their name (from Selection Pane)."""
        replacements_made = 0

        for slide in presentation.slides:
            for shape in slide.shapes:
                # Check if this shape has the target object name
                if hasattr(shape, 'name') and shape.name == object_name:
                    # Update the text content of this specific object
                    if hasattr(shape, 'text'):
                        shape.text = replacement_text
                        replacements_made += 1
                        print(f"  ✓ Updated object '{object_name}' with '{replacement_text}'")
                    elif hasattr(shape, 'text_frame'):
                        # Clear existing text and add new text
                        shape.text_frame.clear()
                        p = shape.text_frame.paragraphs[0]
                        p.text = replacement_text
                        replacements_made += 1
                        print(f"  ✓ Updated object '{object_name}' text frame with '{replacement_text}'")
                    else:
                        print(f"  ⚠️ Found object '{object_name}' but it doesn't support text")

        return replacements_made

    def update_chart_data(self, presentation, chart_name, years_data, values_data, chart_title=None):
        """Update PowerPoint chart with new data using correct python-pptx approach."""
        charts_updated = 0

        for slide in presentation.slides:
            for shape in slide.shapes:
                # Check if this shape is the target chart
                if hasattr(shape, 'name') and shape.name == chart_name:
                    # Check if it's a chart
                    if hasattr(shape, 'chart'):
                        try:
                            from pptx.chart.data import CategoryChartData

                            chart = shape.chart

                            # Create new chart data with proper structure
                            chart_data = CategoryChartData()

                            # Set categories (X-axis labels) - convert years to strings
                            chart_data.categories = [str(year) for year in years_data]

                            # Add series with values (Y-axis data)
                            chart_data.add_series('Health Indicator', values_data)

                            # Replace the chart data
                            chart.replace_data(chart_data)

                            # Update chart title if provided
                            if chart_title:
                                if chart.has_title:
                                    chart.chart_title.text_frame.text = chart_title
                                else:
                                    chart.has_title = True
                                    chart.chart_title.text_frame.text = chart_title

                            charts_updated += 1
                            print(f"  ✓ Updated chart '{chart_name}' with {len(values_data)} data points")
                            print(f"    Years: {years_data[0]} - {years_data[-1]}")
                            print(f"    Values range: {min(values_data):.2f} - {max(values_data):.2f}")

                        except Exception as e:
                            print(f"  ❌ Error updating chart '{chart_name}': {e}")
                            # Provide diagnostic information
                            try:
                                chart = shape.chart
                                print(f"    Chart type: {chart.chart_type}")
                                print(f"    Chart has {len(chart.series)} series")
                                if len(chart.series) > 0:
                                    series = chart.series[0]
                                    print(f"    First series name: {series.name}")
                                    print(f"    First series has {len(series.values)} values")
                            except Exception as e2:
                                print(f"    Additional error getting chart info: {e2}")
                    else:
                        print(f"  ⚠️ Object '{chart_name}' found but is not a chart")

        return charts_updated

    def extract_chart_data_from_document(self, document):
        """Extract chart data from MongoDB document."""
        try:
            chart_data_list = document.get('chartData', [])

            if not chart_data_list:
                print("  ⚠️ No chart data found in document")
                return None

            # Get the first chart data (you can modify this to select specific charts)
            first_chart = chart_data_list[0]
            chart_info = {
                'chart_index': first_chart.get('chartIndex'),
                'chapter_name': first_chart.get('chapterName'),
                'chart_name': first_chart.get('chartName'),
                'chart_geo': first_chart.get('chartGeoName'),
                'years': first_chart['data']['Years'],
                'values': first_chart['data']['Values']
            }

            print(f"  ✓ Extracted chart data: {chart_info['chart_name']} ({len(chart_info['years'])} data points)")
            return chart_info

        except Exception as e:
            print(f"  ❌ Error extracting chart data: {e}")
            return None

    def list_all_object_names(self, presentation):
        """List all object names in the presentation for debugging."""
        print("\n📋 All objects in presentation:")
        for slide_num, slide in enumerate(presentation.slides, 1):
            print(f"  Slide {slide_num}:")
            for shape in slide.shapes:
                if hasattr(shape, 'name'):
                    shape_type = "Text" if hasattr(shape, 'text') else "Other"
                    print(f"    - '{shape.name}' ({shape_type})")
        print()

    def initialize_s3_client(self):
        """Initialize S3 client."""
        try:
            self.s3_client = boto3.client('s3')
            print("✅ S3 client initialized successfully!")
            return True
        except Exception as e:
            print(f"❌ Error initializing S3 client: {e}")
            return False

    def upload_to_s3(self, local_file_path, s3_key=None):
        """Upload file to S3 bucket."""
        try:
            if not self.s3_client:
                if not self.initialize_s3_client():
                    return None

            # Generate S3 key if not provided
            if not s3_key:
                filename = os.path.basename(local_file_path)
                timestamp = datetime.now().strftime("%Y/%m/%d")
                s3_key = f"{filename}"

            print(f"Uploading to S3: s3://{self.s3_bucket}/{s3_key}")

            # Upload file
            self.s3_client.upload_file(
                local_file_path,
                self.s3_bucket,
                s3_key,
                ExtraArgs={'ContentType': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'}
            )

            # Generate download URL
            s3_url = f"https://{self.s3_bucket}.s3.eu-central-1.amazonaws.com/{s3_key}"

            print(f"✅ File uploaded successfully!")
            print(f"   S3 URL: {s3_url}")
            print(f"   S3 Key: {s3_key}")

            return {
                'bucket': self.s3_bucket,
                'key': s3_key,
                'url': s3_url
            }

        except ClientError as e:
            print(f"❌ S3 upload failed: {e}")
            return None
        except Exception as e:
            print(f"❌ Error uploading to S3: {e}")
            return None

    def generate_download_url(self, s3_key, expiration=3600):
        """Generate a presigned URL for downloading the file from S3."""
        try:
            if not self.s3_client:
                if not self.initialize_s3_client():
                    return None

            # Generate presigned URL
            download_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.s3_bucket, 'Key': s3_key},
                ExpiresIn=expiration
            )

            print(f"✅ Generated download URL (expires in {expiration//3600} hours)")
            return download_url

        except Exception as e:
            print(f"❌ Error generating download URL: {e}")
            return None

    def update_powerpoint_template(self, template_path, extracted_data, output_path=None, document=None):
        """Update PowerPoint template with extracted data."""
        try:
            # Load the template
            print(f"Loading template: {template_path}")
            presentation = Presentation(template_path)

            # List all objects for debugging (optional)
            self.list_all_object_names(presentation)

            # Define object name mappings (these should match your PowerPoint object names)
            object_mappings = {
                'market': extracted_data['report_name'],
                'country': extracted_data['country'],
                'source': extracted_data['source'],
                # Add more mappings as needed based on your template object names
            }

            total_replacements = 0

            # Perform replacements by object name
            print("Updating objects by name...")
            for object_name, replacement_text in object_mappings.items():
                replacements = self.find_and_update_by_object_name(presentation, object_name, replacement_text)
                total_replacements += replacements

                if replacements == 0:
                    print(f"  ⚠️ Object '{object_name}' not found in template")

            # Update charts with data
            print("\nUpdating charts with data...")
            chart_data = self.extract_chart_data_from_document(document)

            if chart_data:
                # Update the specific chart
                chart_updates = self.update_chart_data(
                    presentation,
                    'chart_global_ind_1',  # Target chart name
                    chart_data['years'],   # X-axis labels (years)
                    chart_data['values'],  # Y-axis values
                    f"{chart_data['chart_name']} - {chart_data['chart_geo']}"  # Chart title
                )
                total_replacements += chart_updates
            else:
                print("  ⚠️ No chart data available for updates")
            
            # Generate output filename if not provided
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"market_insights_report_{extracted_data['report_id']}_{timestamp}.pptx"
            
            # Save the updated presentation
            presentation.save(output_path)
            
            print(f"✅ PowerPoint updated successfully!")
            print(f"   - Total replacements made: {total_replacements}")
            print(f"   - Output file: {output_path}")
            
            return output_path
            
        except FileNotFoundError:
            print(f"❌ Template file not found: {template_path}")
            return None
        except Exception as e:
            print(f"❌ Error updating PowerPoint: {e}")
            return None
    
    def process_document_to_powerpoint(self, template_path="template.pptx", doc_id=None, output_path=None, upload_to_s3=True):
        """Complete workflow: MongoDB → PowerPoint → S3."""
        print("🚀 Starting MongoDB to PowerPoint processing...")

        # Step 1: Connect to MongoDB
        if not self.connect_to_mongodb():
            return False

        # Step 2: Get document
        document = self.get_document_by_id(doc_id)
        if not document:
            return False

        # Step 3: Extract data
        extracted_data = self.extract_data_from_document(document)
        if not extracted_data:
            return False

        # Step 4: Update PowerPoint
        output_file = self.update_powerpoint_template(template_path, extracted_data, output_path, document)

        if not output_file:
            return False

        # Step 5: Upload to S3 (optional)
        s3_info = None
        if upload_to_s3:
            print("\n📤 Uploading to S3...")
            s3_info = self.upload_to_s3(output_file)

            if s3_info:
                # Use simple public URL (bucket is now public)
                public_url = f"https://{self.s3_bucket}.s3.eu-central-1.amazonaws.com/{s3_info['key']}"

                print(f"\n🎉 Complete workflow finished successfully!")
                print(f"   Local file: {output_file}")
                print(f"   S3 bucket: {s3_info['bucket']}")
                print(f"   S3 key: {s3_info['key']}")
                print(f"   📥 PUBLIC DOWNLOAD URL: {public_url}")

                # Add public URL to return info
                s3_info['public_url'] = public_url

                # Optionally remove local file after successful upload
                try:
                    os.remove(output_file)
                    print(f"   Local file cleaned up: {output_file}")
                except:
                    pass

                return s3_info
            else:
                print(f"⚠️ S3 upload failed, but local file available: {output_file}")
                return {'local_file': output_file}
        else:
            print(f"🎉 Process completed successfully! Output: {output_file}")
            return {'local_file': output_file}
    
    def close_connection(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            print("MongoDB connection closed.")


def main():
    """Main execution function."""
    processor = MongoToPowerPointProcessor()
    
    try:
        # Process the first document with template.pptx
        success = processor.process_document_to_powerpoint(
            template_path="template.pptx",
            doc_id=None,  # Use None for first document, or specify an ID
            output_path=None  # Auto-generate filename
        )
        
        if success:
            print("\n✅ All done! Your PowerPoint presentation has been created.")
        else:
            print("\n❌ Process failed. Please check the error messages above.")
            
    finally:
        processor.close_connection()


if __name__ == "__main__":
    main()
