#!/usr/bin/env python3
"""
Chart Identification Script
Analyzes chart objects in PowerPoint template to identify chart types and current data structure.
"""

from pptx import Presentation
import sys
import os

class ChartIdentifier:
    def __init__(self, template_path="template.pptx"):
        self.template_path = template_path
        
    def analyze_charts(self):
        """Analyze all chart objects in the PowerPoint template."""
        try:
            if not os.path.exists(self.template_path):
                print(f"❌ Template file not found: {self.template_path}")
                return False
                
            print(f"🔍 Analyzing charts in: {self.template_path}")
            presentation = Presentation(self.template_path)
            
            # Chart objects to analyze
            target_charts = ['chart_global_ind_1', 'chart_global_ind_2', 'chart_global_ind_3']
            
            for chart_name in target_charts:
                print(f"\n{'='*60}")
                print(f"📊 ANALYZING: {chart_name}")
                print(f"{'='*60}")
                
                charts_found = 0
                
                # Search through all slides
                for slide_num, slide in enumerate(presentation.slides, 1):
                    for shape in slide.shapes:
                        if hasattr(shape, 'name') and shape.name == chart_name:
                            charts_found += 1
                            print(f"\n🎯 Found {chart_name} on slide {slide_num}")
                            
                            if hasattr(shape, 'chart'):
                                self.analyze_single_chart(shape.chart, slide_num)
                            else:
                                print(f"   ⚠️  Shape is not a chart object")
                
                if charts_found == 0:
                    print(f"   ❌ No charts found with name: {chart_name}")
                else:
                    print(f"\n📈 Total instances of {chart_name}: {charts_found}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error analyzing charts: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def analyze_single_chart(self, chart, slide_num):
        """Analyze a single chart object."""
        try:
            # Get chart type information
            chart_type = chart.chart_type
            chart_type_name = str(chart_type).split('.')[-1] if hasattr(chart_type, '__str__') else str(chart_type)
            chart_type_value = int(chart_type) if hasattr(chart_type, '__int__') else chart_type
            
            print(f"   📊 Chart Type: {chart_type_name} (Value: {chart_type_value})")
            
            # Determine chart category
            line_chart_types = ['LINE', 'LINE_MARKERS', 'LINE_MARKERS_STACKED', 'LINE_STACKED', 'LINE_STACKED_100', 'SCATTER']
            is_line_chart = any(line_type in chart_type_name for line_type in line_chart_types)
            
            if is_line_chart:
                print(f"   🔍 Category: LINE CHART")
            elif 'COLUMN' in chart_type_name or 'BAR' in chart_type_name:
                print(f"   🔍 Category: COLUMN/BAR CHART")
            elif 'DOUGHNUT' in chart_type_name or 'PIE' in chart_type_name:
                print(f"   🔍 Category: PIE/DOUGHNUT CHART")
            else:
                print(f"   🔍 Category: OTHER CHART TYPE")
            
            # Analyze chart data structure
            self.analyze_chart_data(chart)
            
        except Exception as e:
            print(f"   ❌ Error analyzing chart on slide {slide_num}: {e}")
    
    def analyze_chart_data(self, chart):
        """Show RAW chart data structure from python-pptx."""
        try:
            print(f"\n   📋 RAW CHART DATA FROM PYTHON-PPTX:")
            print(f"   {'-'*50}")

            # Raw series data
            print(f"   chart.series = {chart.series}")
            print(f"   len(chart.series) = {len(chart.series) if chart.series else 'None'}")

            if chart.series:
                for i, series in enumerate(chart.series):
                    print(f"\n   === RAW SERIES {i+1} ===")
                    print(f"   series = {series}")
                    print(f"   series.name = {repr(series.name) if hasattr(series, 'name') else 'No name attr'}")
                    print(f"   hasattr(series, 'values') = {hasattr(series, 'values')}")

                    if hasattr(series, 'values'):
                        print(f"   series.values = {series.values}")
                        print(f"   type(series.values) = {type(series.values)}")
                        if series.values:
                            print(f"   len(series.values) = {len(series.values)}")
                            print(f"   series.values[:10] = {series.values[:10]}")  # First 10 raw values

                    # Check for other attributes
                    print(f"   dir(series) = {[attr for attr in dir(series) if not attr.startswith('_')]}")

            # Raw chart structure
            print(f"\n   === RAW CHART STRUCTURE ===")
            print(f"   hasattr(chart, 'plots') = {hasattr(chart, 'plots')}")

            if hasattr(chart, 'plots'):
                print(f"   chart.plots = {chart.plots}")
                print(f"   len(chart.plots) = {len(chart.plots) if chart.plots else 'None'}")

                if chart.plots:
                    plot = chart.plots[0]
                    print(f"\n   === RAW PLOT DATA ===")
                    print(f"   plot = {plot}")
                    print(f"   hasattr(plot, 'categories') = {hasattr(plot, 'categories')}")

                    if hasattr(plot, 'categories'):
                        print(f"   plot.categories = {plot.categories}")
                        print(f"   type(plot.categories) = {type(plot.categories)}")
                        if plot.categories:
                            print(f"   len(plot.categories) = {len(plot.categories)}")
                            print(f"   plot.categories[:10] = {plot.categories[:10]}")  # First 10 raw categories

                    print(f"   dir(plot) = {[attr for attr in dir(plot) if not attr.startswith('_')]}")

            # Try to access chart data directly
            print(f"\n   === RAW CHART DATA ACCESS ===")
            print(f"   hasattr(chart, 'chart_data') = {hasattr(chart, 'chart_data')}")
            print(f"   hasattr(chart, 'data') = {hasattr(chart, 'data')}")

            # Show all chart attributes
            print(f"\n   === ALL CHART ATTRIBUTES ===")
            chart_attrs = [attr for attr in dir(chart) if not attr.startswith('_')]
            print(f"   dir(chart) = {chart_attrs}")

        except Exception as e:
            print(f"   ❌ Error getting raw chart data: {e}")
            import traceback
            traceback.print_exc()

def main():
    """Main function to run chart identification."""
    print("🚀 Chart Identification Tool")
    print("="*50)
    
    # Check if template file exists
    template_path = "template.pptx"
    if not os.path.exists(template_path):
        print(f"❌ Template file not found: {template_path}")
        print("Please make sure template.pptx is in the current directory.")
        return 1
    
    # Create identifier and analyze charts
    identifier = ChartIdentifier(template_path)
    success = identifier.analyze_charts()
    
    if success:
        print(f"\n✅ Chart identification completed successfully!")
        return 0
    else:
        print(f"\n❌ Chart identification failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
