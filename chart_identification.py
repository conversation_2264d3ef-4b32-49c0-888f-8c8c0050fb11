#!/usr/bin/env python3
"""
Chart Identification Script
Analyzes chart objects in PowerPoint template to identify chart types and current data structure.
"""

from pptx import Presentation
import sys
import os

class ChartIdentifier:
    def __init__(self, template_path="template.pptx"):
        self.template_path = template_path
        
    def analyze_charts(self):
        """Analyze all chart objects in the PowerPoint template."""
        try:
            if not os.path.exists(self.template_path):
                print(f"❌ Template file not found: {self.template_path}")
                return False
                
            print(f"🔍 Analyzing charts in: {self.template_path}")
            presentation = Presentation(self.template_path)
            
            # Chart objects to analyze
            target_charts = ['chart_global_ind_1', 'chart_global_ind_2', 'chart_global_ind_3']
            
            for chart_name in target_charts:
                print(f"\n{'='*60}")
                print(f"📊 ANALYZING: {chart_name}")
                print(f"{'='*60}")
                
                charts_found = 0
                
                # Search through all slides
                for slide_num, slide in enumerate(presentation.slides, 1):
                    for shape in slide.shapes:
                        if hasattr(shape, 'name') and shape.name == chart_name:
                            charts_found += 1
                            print(f"\n🎯 Found {chart_name} on slide {slide_num}")
                            
                            if hasattr(shape, 'chart'):
                                self.analyze_single_chart(shape.chart, slide_num)
                            else:
                                print(f"   ⚠️  Shape is not a chart object")
                
                if charts_found == 0:
                    print(f"   ❌ No charts found with name: {chart_name}")
                else:
                    print(f"\n📈 Total instances of {chart_name}: {charts_found}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error analyzing charts: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def analyze_single_chart(self, chart, slide_num):
        """Analyze a single chart object."""
        try:
            # Get chart type information
            chart_type = chart.chart_type
            chart_type_name = str(chart_type).split('.')[-1] if hasattr(chart_type, '__str__') else str(chart_type)
            chart_type_value = int(chart_type) if hasattr(chart_type, '__int__') else chart_type
            
            print(f"   📊 Chart Type: {chart_type_name} (Value: {chart_type_value})")
            
            # Determine chart category
            line_chart_types = ['LINE', 'LINE_MARKERS', 'LINE_MARKERS_STACKED', 'LINE_STACKED', 'LINE_STACKED_100', 'SCATTER']
            is_line_chart = any(line_type in chart_type_name for line_type in line_chart_types)
            
            if is_line_chart:
                print(f"   🔍 Category: LINE CHART")
            elif 'COLUMN' in chart_type_name or 'BAR' in chart_type_name:
                print(f"   🔍 Category: COLUMN/BAR CHART")
            elif 'DOUGHNUT' in chart_type_name or 'PIE' in chart_type_name:
                print(f"   🔍 Category: PIE/DOUGHNUT CHART")
            else:
                print(f"   🔍 Category: OTHER CHART TYPE")
            
            # Analyze chart data structure
            self.analyze_chart_data(chart)
            
        except Exception as e:
            print(f"   ❌ Error analyzing chart on slide {slide_num}: {e}")
    
    def analyze_chart_data(self, chart):
        """Analyze the current data structure in the chart."""
        try:
            print(f"\n   📋 CURRENT CHART DATA STRUCTURE:")
            print(f"   {'-'*40}")
            
            # Get series information
            series_count = len(chart.series) if chart.series else 0
            print(f"   📈 Number of Series: {series_count}")
            
            if series_count == 0:
                print(f"   ⚠️  No series data found")
                return
            
            # Analyze each series
            for i, series in enumerate(chart.series, 1):
                try:
                    series_name = series.name if hasattr(series, 'name') and series.name else f"Series {i}"
                    print(f"\n   📊 Series {i}: {series_name}")
                    
                    # Get values if available
                    if hasattr(series, 'values') and series.values:
                        values = series.values
                        print(f"      📍 Data Points: {len(values)}")
                        
                        # Show first few values
                        if len(values) > 0:
                            sample_values = values[:5]  # First 5 values
                            print(f"      📊 Sample Values: {sample_values}")
                            if len(values) > 5:
                                print(f"      📊 ... and {len(values) - 5} more values")
                            
                            # Show value range
                            try:
                                numeric_values = [float(v) for v in values if v is not None]
                                if numeric_values:
                                    min_val = min(numeric_values)
                                    max_val = max(numeric_values)
                                    print(f"      📊 Value Range: {min_val:.2f} - {max_val:.2f}")
                            except:
                                pass
                    else:
                        print(f"      ⚠️  No values found for this series")
                        
                except Exception as e:
                    print(f"      ❌ Error analyzing series {i}: {e}")
            
            # Try to get categories (X-axis labels)
            try:
                # This is tricky with python-pptx, but let's try
                print(f"\n   🏷️  CATEGORIES (X-axis labels):")
                
                # Try to access categories through the chart's plot area
                if hasattr(chart, 'plots') and chart.plots:
                    plot = chart.plots[0]
                    if hasattr(plot, 'categories') and plot.categories:
                        categories = plot.categories
                        print(f"      📍 Number of Categories: {len(categories)}")
                        
                        # Show first few categories
                        if len(categories) > 0:
                            sample_cats = categories[:5]
                            print(f"      🏷️  Sample Categories: {sample_cats}")
                            if len(categories) > 5:
                                print(f"      🏷️  ... and {len(categories) - 5} more categories")
                    else:
                        print(f"      ⚠️  No categories found in plot")
                else:
                    print(f"      ⚠️  No plots found in chart")
                    
            except Exception as e:
                print(f"      ⚠️  Could not access categories: {e}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing chart data: {e}")

def main():
    """Main function to run chart identification."""
    print("🚀 Chart Identification Tool")
    print("="*50)
    
    # Check if template file exists
    template_path = "template.pptx"
    if not os.path.exists(template_path):
        print(f"❌ Template file not found: {template_path}")
        print("Please make sure template.pptx is in the current directory.")
        return 1
    
    # Create identifier and analyze charts
    identifier = ChartIdentifier(template_path)
    success = identifier.analyze_charts()
    
    if success:
        print(f"\n✅ Chart identification completed successfully!")
        return 0
    else:
        print(f"\n❌ Chart identification failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
