#!/usr/bin/env python3
"""
Upload template.pptx to S3 and generate signed URL for download
"""

import boto3
from botocore.exceptions import NoCredentialsError
import os
import sys

def upload_template_to_s3():
    """Upload template.pptx to S3 and generate signed download URL."""
    try:
        # Initialize S3 client
        s3_client = boto3.client('s3')
        
        # S3 bucket and file details
        bucket_name = 'market-insights-reports-s3'
        local_file = 'template.pptx'
        s3_key = 'template.pptx'
        
        # Check if local file exists
        if not os.path.exists(local_file):
            print(f'❌ Local file not found: {local_file}')
            return False
        
        # Get file size
        file_size = os.path.getsize(local_file)
        print(f'📤 Uploading {local_file} ({file_size:,} bytes) to S3...')
        
        # Upload file to S3
        s3_client.upload_file(local_file, bucket_name, s3_key)
        print(f'✅ Successfully uploaded to s3://{bucket_name}/{s3_key}')
        
        # Generate signed URL (24 hours)
        signed_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': s3_key},
            ExpiresIn=86400  # 24 hours
        )
        
        print(f'\n🔗 SIGNED DOWNLOAD URL (24h):')
        print(f'{signed_url}')
        
        return True
        
    except NoCredentialsError:
        print('❌ AWS credentials not found')
        return False
    except Exception as e:
        print(f'❌ Error uploading to S3: {e}')
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("🚀 Template Upload to S3")
    print("=" * 40)
    
    success = upload_template_to_s3()
    
    if success:
        print(f"\n✅ Template upload completed successfully!")
        return 0
    else:
        print(f"\n❌ Template upload failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
